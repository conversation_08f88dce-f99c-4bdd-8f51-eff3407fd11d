/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";

export interface IMsgPermissionContext {
  userId?: string;
  userRoles?: string[];
  userPermissions?: string[];
  resourceId?: string;
  action?: string;
  [key: string]: any;
}

export interface IMsgPermissionResult {
  allowed: boolean;
  reason?: string;
  requiredPermissions?: string[];
  missingPermissions?: string[];
}

@Injectable({
  providedIn: 'root'
})
export abstract class MsgPermissionAbstractService {
  protected _currentUser: any = null;
  protected _userPermissions: string[] = [];
  protected _userRoles: string[] = [];

  public abstract checkPermission(permission?: any, context?: IMsgPermissionContext): boolean;

  public abstract checkPermissionAsync(permission?: any, context?: IMsgPermissionContext): Observable<boolean>;

  public abstract checkPermissionDetailed(permission?: any, context?: IMsgPermissionContext): IMsgPermissionResult;

  public abstract hasRole(role: string): boolean;

  public abstract hasAnyRole(roles: string[]): boolean;

  public abstract hasAllRoles(roles: string[]): boolean;

  public abstract canAccess(resource: string, action?: string): boolean;

  public abstract getCurrentUser(): any;

  public abstract setCurrentUser(user: any): void;

  public abstract refreshPermissions(): Observable<void>;

  protected _hasPermission(permission: string): boolean {
    return this._userPermissions.includes(permission);
  }

  protected _hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this._userPermissions.includes(permission));
  }

  protected _hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this._userPermissions.includes(permission));
  }

  protected _hasRoleInternal(role: string): boolean {
    return this._userRoles.includes(role);
  }

  protected _hasAnyRoleInternal(roles: string[]): boolean {
    return roles.some(role => this._userRoles.includes(role));
  }

  protected _hasAllRolesInternal(roles: string[]): boolean {
    return roles.every(role => this._userRoles.includes(role));
  }

  protected _createPermissionResult(
    allowed: boolean,
    reason?: string,
    requiredPermissions?: string[],
    missingPermissions?: string[]
  ): IMsgPermissionResult {
    return {
      allowed,
      reason,
      requiredPermissions,
      missingPermissions
    };
  }
}
