import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MsgFormSubmitDirective } from './directives/msg-form-submit.directive';
import { MsgValidateSharedComponent } from './msg-validate.component';

@NgModule({
  imports: [
    CommonModule,
    MsgValidateSharedComponent,
    MsgFormSubmitDirective,
  ],
  exports: [
    MsgValidateSharedComponent,
    MsgFormSubmitDirective
  ]
})

export class MsgValidateModule { }
