import { NgTemplateOutlet } from '@angular/common';
import { Component, input, OnDestroy, OnInit, signal } from '@angular/core';
import { MsgButtonSharedComponent } from '../msg-button/msg-button.component';
import { MsgIconSharedComponent } from '../msg-icon/msg-icon.component';
import { IMsgNotificationDto, TYPE_NOTIFICATION_POSITION } from './interfaces/msg-notification.interface';

@Component({
  selector: 'msg-notification-shared',
  templateUrl: './msg-notification.component.html',
  styleUrls: ['./msg-notification.component.scss'],
  standalone: true,
  imports: [
    MsgIconSharedComponent,
    MsgButtonSharedComponent,
    NgTemplateOutlet
  ],
  host: {
    '[class.hide]': 'isHiding()',
    '[class.position-left]': 'msgPosition().includes("left")',
    '[class.position-right]': 'msgPosition().includes("right")',
    '[class.position-center]': 'msgPosition().includes("center")'
  }
})
export class MsgNotificationSharedComponent implements OnDestroy, OnInit {
  public notificationData = input.required<IMsgNotificationDto>();
  public msgOnRemove = input<() => void>();
  public msgShowProcessBar = input<boolean>(true);
  public msgPosition = input<TYPE_NOTIFICATION_POSITION>('top-right');

  protected isHiding = signal<boolean>(false);
  protected isPaused = signal<boolean>(false);

  private _timerId = signal<number | null>(null);
  private _remainingTime = signal<number | null>(null);
  private _startTime = signal<number | null>(null);

  constructor() { }

  ngOnInit(): void {
    const notification = this.notificationData();
    if (notification.duration && notification.duration > 0) {
      this._startTimer(notification.duration);
    }
  }

  ngOnDestroy(): void {
    this._clearTimer();
  }

  private _startTimer(duration: number): void {
    this._remainingTime.set(duration);
    this._startTime.set(Date.now());

    this._timerId.set(window.setTimeout(() => {
      this._removeNotification();
    }, duration));
  }

  private _clearTimer(): void {
    const timerId = this._timerId();
    if (timerId) {
      window.clearTimeout(timerId);
      this._timerId.set(null);
    }
  }

  private _pauseTimer(): void {
    const timerId = this._timerId();
    const startTime = this._startTime();
    const remainingTime = this._remainingTime();
    if (timerId && startTime && remainingTime) {
      this._clearTimer();
      const elapsed = Date.now() - startTime;
      this._remainingTime.set(Math.max(0, remainingTime - elapsed));
      this.isPaused.set(true);

      const currentData = this.notificationData();
      if (currentData) {
        currentData.isPaused = true;
      }
    }
  }

  private _resumeTimer(): void {
    const remainingTime = this._remainingTime();
    if (!remainingTime || remainingTime <= 0) {
      return;
    }

    if (remainingTime > 0) {
      this.isPaused.set(false);

      const currentData = this.notificationData();
      if (currentData) {
        currentData.isPaused = false;
      }

      this._startTimer(remainingTime);
    }
  }

  private _removeNotification(isManualClose = false): void {
    if (isManualClose) {
      const removeCallback = this.msgOnRemove();
      if (removeCallback) {
        removeCallback();
      }
      return;
    }

    this.isHiding.set(true);

    const currentData = this.notificationData();
    if (currentData) {
      currentData.isHiding = true;
    }

    setTimeout(() => {
      const removeCallback = this.msgOnRemove();
      if (removeCallback) {
        removeCallback();
      }
    }, 300);
  }

  protected handlerRemoveNotification(event: MouseEvent): void {
    event.stopPropagation();
    this._removeNotification(true);
  }

  protected handlerMouseOver(event: MouseEvent): void {
    event.stopPropagation();
    this._pauseTimer();
  }

  protected handlerMouseLeave(event: MouseEvent): void {
    event.stopPropagation();
    this._resumeTimer();
  }

  protected getProgressBarAnimationClass(): string {
    const position = this.msgPosition();
    if (position.includes('left')) {
      return 'progress-countdown-left';
    }
    if (position.includes('right')) {
      return 'progress-countdown-right';
    }
    if (position.includes('center')) {
      return 'progress-countdown-center';
    }

    return 'progress-countdown-right';
  }
}
