import { AfterViewInit, ChangeDetectorRef, contentChild, Directive, ElementRef, inject, input, OnDestroy, OnInit, output, Renderer2 } from '@angular/core';
import { NzTableComponent } from 'ng-zorro-antd/table';
import { Subject } from 'rxjs';
import { debounceTime, tap } from 'rxjs/operators';

@Directive({
  selector: '[msg-table-full-height], [msgTableFullHeight]',
  standalone: true
})
export class MsgTableFullHeightDirective implements OnInit, AfterViewInit, OnDestroy {
  public _table = contentChild<NzTableComponent<unknown>>(NzTableComponent);
  public msgScrollYChange = output<string>();

  public msgTableHeight = input<number>(95);
  private _observer: ResizeObserver;
  private _resize$ = new Subject<void>();

  private _element = inject(ElementRef);
  private _cdr = inject(ChangeDetectorRef);
  private _render2 = inject(Renderer2);

  constructor() {
    this._observer = new ResizeObserver(() => this._resize$.next());
    this._resize$.pipe(
      debounceTime(200),
      tap(() => this._resizeTable())
    ).subscribe();
  }

  ngOnInit(): void {
    this._render2.setStyle(this._element.nativeElement, 'height', '100%');
    this._observer.observe(this._element.nativeElement);
  }

  ngOnDestroy(): void {
    this._observer.disconnect();
    this._resize$.complete();
  }

  public ngAfterViewInit(): void {
    if (!this._table) {
      return;
    }

    this._resizeTable();
  }

  private _resizeTable(): void {
    const nzTable: HTMLElement = this._element.nativeElement;
    const scrollY = `${nzTable.clientHeight - this.msgTableHeight()}px`;
    this.msgScrollYChange.emit(scrollY);
    const table = this._table();
    if (table) {
      table.scrollY = scrollY;
      this._cdr.detectChanges();
    }
  }
}
