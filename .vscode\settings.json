{
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit"
  },
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "[typescript]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.formatOnSave": true
  },
  "[javascript]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.formatOnSave": true
  },
  "[html]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "boivn.mobio-format-angular-html",
    "editor.formatOnSave": true
  },
  "[scss]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "vscode.css-language-features",
    "editor.formatOnSave": true
  },
  "[css]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "vscode.css-language-features",
    "editor.formatOnSave": true
  },
  "[json]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "vscode.json-language-features",
    "editor.formatOnSave": true
  },
  "[jsonc]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.defaultFormatter": "vscode.json-language-features",
    "editor.formatOnSave": true
  },
  "files.associations": {
    "*.ts": "typescript",
    "*.html": "html"
  },
  "emmet.includeLanguages": {
    "typescript": "html"
  },
  "typescript.format.enable": true,
  "typescript.format.insertSpaceAfterCommaDelimiter": true,
  "typescript.format.insertSpaceAfterSemicolonInForStatements": true,
  "typescript.format.insertSpaceBeforeAndAfterBinaryOperators": true,
  "typescript.format.insertSpaceAfterKeywordsInControlFlowStatements": true,
  "typescript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": false,
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis": false,
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": false,
  "typescript.format.insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces": false,
  "html.format.enable": true,
  "html.format.indentInnerHtml": false,
  "html.format.preserveNewLines": true,
  "html.format.maxPreserveNewLines": 2,
  "html.format.indentHandlebars": false,
  "html.format.extraLiners": "head, body, /html",
  "css.format.enable": true,
  "scss.format.enable": true,
  /** Enable GitHub Copilot and Chat features **/
  "chat.agent.enabled": true,
  "chat.tools.autoApprove": false,
  "github.copilot.chat.agent.runTasks": true,
  "github.copilot.enable": {
    "*": false
  },
  "github.copilot.editor.enableCodeActions": true,
  "chat.edits2.enabled": true,
  "github.copilot.chat.agent.thinkingTool": true,
  "github.copilot.chat.localeOverride": "en",
  /** Save settings **/
  "html.format.wrapAttributes": "force",
  "html.format.wrapLineLength": 250,
  "editor.wordWrapColumn": 120,
}
