import { AfterViewInit, computed, Directive, ElementRef, inject, input, Renderer2, RendererStyleFlags2 } from "@angular/core";
import { MsgTruncatePipe } from "../../../pipes";
import { createIntervalObserver$ } from "../../../utils";
import { IMsgTableColumn } from "../interfaces";

@Directive({
  selector: "[msg-table], [msgTable], th[msgWidth], td[msgWidth], th[msgLeft], th[msgRight], td[msgLeft], td[msgRight], th[msgRow], td[msgRow], th[msgCol], td[msgCol], th[msgRowIndex], td[msgRowIndex], th[msgClass], td[msgClass] th[msgStyle] td[msgStyle], tr[msgRowStyle], tr[msgRowClass], td[msgAttr]",
  host: {
    "[style.width]": "msgWidthComputed()",
    "[style.min-width]": "msgWidthComputed()",
    "[style.max-width]": "msgWidthComputed()",
    "[class.msg-sticky-left]": "msgLeft()",
    "[class.msg-sticky-right]": "msgRight()",
    "[attr.msg-col]": "msgCol()",
    "[attr.msg-row]": "msgRow()",
    "[style.position]": "(msgLeft() || msgRight()) ? 'sticky' : null",
  },
  standalone: true,
  providers: [MsgTruncatePipe],
})

export class MsgTableDirective implements AfterViewInit {
  public msgAttr = input<IMsgTableColumn['attr']>();
  public msgWidth = input<number | string>();
  public msgLeft = input<boolean>(false);
  public msgRight = input<boolean>(false);
  public msgHeight = input<number | string>();
  public msgRow = input<number>();
  public msgCol = input<number>();
  public msgRowIndex = input<number>();
  public msgClass = input<string>('');
  public msgStyle = input<string>('');
  public msgRowStyle = input<string>('');
  public msgRowClass = input<string>('');
  protected msgWidthComputed = computed((): string | null => {
    const width = this.msgWidth();
    return !width ? 'auto' : (typeof width === 'number' ? `${width}px` : width);
  });

  private _elementRef = inject(ElementRef<HTMLTableCellElement>);
  private _renderer = inject(Renderer2);

  constructor() { }

  ngAfterViewInit(): void {
    this._setCellClasses();
    this._setCellStyle();
    this._setRowClass();
    this._setRowStyle();
    this._setCellAttribute();
    this._setTruncateText();
  }

  private _setCellClasses(): void {
    const element = this._elementRef.nativeElement;
    const msgClass = this.msgClass();

    if (msgClass && msgClass.trim()) {
      const classNames = msgClass.trim().split(/\s+/).filter(cls => cls.length > 0);

      classNames.forEach(className => {
        if (className.trim()) {
          this._renderer.addClass(element, className.trim());
        }
      });
    }
  }

  private _setCellStyle(): void {
    const element = this._elementRef.nativeElement;
    const style = this.msgStyle();
    const isThTagElement = element.tagName.toLowerCase() === 'th';

    if (isThTagElement) {
      this._renderer.setStyle(element, 'background-color', 'var(--color-neutral-100)');
    }

    if (style) {
      const styles = style.split(';').filter((s: string) => s.trim());

      styles.forEach((styleDeclaration: string) => {
        const [property, value] = styleDeclaration.split(':').map((s: string) => s.trim());
        if (property && value) {
          this._renderer.setStyle(element, property, value, RendererStyleFlags2.Important);
        }
      });
    }
  }

  private _setRowClass(): void {
    const element = this._elementRef.nativeElement;
    const isTrTagElement = element.tagName.toLowerCase() === 'tr';
    if (!isTrTagElement) {
      return;
    }

    const msgRowClass = this.msgRowClass();
    if (msgRowClass && msgRowClass.trim()) {
      const classNames = msgRowClass.trim().split(/\s+/).filter(cls => cls.length > 0);
      const cellsOfRow = element.querySelectorAll('td');
      classNames.forEach(className => {
        if (className.trim()) {
          cellsOfRow.forEach((cell: HTMLTableCellElement) => {
            this._renderer.addClass(cell, className.trim());
          });
        }
      });
    }
  }

  private _setRowStyle(): void {
    const element = this._elementRef.nativeElement;
    const isTrTagElement = element.tagName.toLowerCase() === 'tr';
    if (!isTrTagElement) {
      return;
    }

    const msgRowStyle = this.msgRowStyle();
    if (msgRowStyle) {
      const styles = msgRowStyle.split(';').filter((s: string) => s.trim());
      const cellsOfRow = element.querySelectorAll('td');
      styles.forEach((styleDeclaration: string) => {
        const [property, value] = styleDeclaration.split(':').map((s: string) => s.trim());
        if (property && value) {
          cellsOfRow.forEach((cell: HTMLTableCellElement) => {
            this._renderer.setStyle(cell, property, value, RendererStyleFlags2.Important);
          });
        }
      });
    }
  }

  private _setCellAttribute(): void {
    for (const [key, value] of Object.entries(this.msgAttr() || {})) {
      if (value !== undefined && value !== null) {
        this._renderer.setAttribute(this._elementRef.nativeElement, key, String(value));
      }
    }
  }

  private _setTruncateText(): void {
    const element = this._elementRef.nativeElement;
    if (!element) {
      return;
    }

    const { observable$ } = createIntervalObserver$(
      (elapsed, unsubscribe) => {
        const textContent = element.textContent?.trim();
        const rect = element.getBoundingClientRect();
        if ((rect?.width ?? 0) > 0 || elapsed > 1500) {
          const { scrollWidth, clientWidth } = element;
          const isTruncated = scrollWidth > clientWidth;

          if (isTruncated) {
            this._renderer.addClass(element, 'truncate');
            if (textContent) {
              this._renderer.setAttribute(element, 'title', textContent);
            }
          }

          unsubscribe();
        }
      },
      200,
      2000
    );

    observable$.subscribe();
  }
}
