import { ChangeDetectionStrategy, Component, computed, HostBinding, input, output } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSizeLDSType } from 'ng-zorro-antd/core/types';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { TYPE_MSG_ICON_NAME, TYPE_MSG_ICON_SIZE } from '../msg-icon/interfaces/msg-icon.interface';
import { MsgIconSharedComponent } from '../msg-icon/msg-icon.component';
import { IMsgTooltipDto, TYPE_MSG_BUTTON } from './interfaces/msg-button.interface';

@Component({
  selector: 'msg-button-shared',
  templateUrl: './msg-button.component.html',
  styleUrls: ['./msg-button.component.scss'],
  standalone: true,
  imports: [
    MsgIconSharedComponent,
    NzToolTipModule,
    NzButtonModule,
    TranslatePipe
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class MsgButtonSharedComponent {
  public msgSize = input<NzSizeLDSType>('default');
  public msgType = input<TYPE_MSG_BUTTON>('primary');
  public msgTooltip = input<IMsgTooltipDto>();
  public msgLoading = input<boolean>(false);
  public msgDisabled = input<boolean>(false);
  public msgClass = input<string>('');
  public msgClassIcon = input<string>('');
  public msgStrokeIcon = input<number | null>();
  public msgText = input<string>('');
  public msgIconRight = input<TYPE_MSG_ICON_NAME | null>(null);
  public msgIconLeft = input<TYPE_MSG_ICON_NAME | null>(null);
  public msgIconSize = input<TYPE_MSG_ICON_SIZE>(16);
  public msgDisableWave = input<boolean>(false);
  public msgCount = input<number | null>(null);
  public msgMaxCount = input<number>(99);
  public msgButtonSubmit = input<boolean>(false);
  public msgShowTooltip = input<boolean>(false);

  public msgClick = output<MouseEvent>();

  protected msgTooltipConfig = computed(() => {
    return this.msgTooltip() || {
      content: this.msgText() || '',
      placement: 'top',
      trigger: 'hover'
    };
  });
  protected msgColorIcon = computed(() => {
    switch (this.msgType()) {
      case 'primary':
      case 'success':
      case 'warn':
      case 'danger':
      case 'info':
        return this.msgClassIcon() + ' text-white';
      case 'secondary':
        return this.msgClassIcon() + ' text-slate-600';
      case 'outline':
      case 'outline-success':
        return this.msgClassIcon() + ' text-green-600';
      case 'outline-warn':
        return this.msgClassIcon() + ' text-orange-600';
      case 'outline-danger':
        return this.msgClassIcon() + ' text-red-600';
      case 'outline-info':
        return this.msgClassIcon() + ' text-blue-600';
      case 'outline-secondary':
        return this.msgClassIcon() + ' text-slate-600';
      case 'link':
        return this.msgClassIcon() + ' text-blue-600';
      case 'text':
        return this.msgClassIcon() + ' text-slate-600';
      default:
        return this.msgClassIcon() + ' text-white';
    }
  });

  public handlerClick(event: MouseEvent): void {
    if (this.msgDisabled()) {
      return;
    }

    this.msgClick.emit(event);
  }

  @HostBinding('class')
  protected get hostClass(): string {
    return this.msgClass();
  }
}
