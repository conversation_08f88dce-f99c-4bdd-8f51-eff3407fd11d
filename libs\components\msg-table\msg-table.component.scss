@use 'responsive' as *;

::ng-deep {
  .msg-table-container {
    --msg-table-padding: 5px 10px;
    --msg-table-padding-tbody: 3px 10px;
    --msg-table-cell-width: auto;
    --msg-table-bg-color: white;
    --msg-table-border-color: var(--color-gray-200);
    --msg-table-border-light: var(--color-gray-200);
    --msg-table-header-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --msg-table-z-index-base: 10;
    --msg-table-z-index-sticky-left: 11;
    --msg-table-z-index-sticky-right: 12;
    --msg-table-z-index-header: 15;
    --msg-table-z-index-footer: 15;
    --msg-table-font-weight: 600;
    --msg-table-transition: 0.2s ease;
    --msg-table-hover-bg-color: var(--color-gray-100);
    --msg-table-font-size: 12px;

    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: hidden;
    position: relative;
  }

  .msg-table-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    padding: 10px;
  }

  .msg-table-wrapper {
    flex: 1;
    overflow-y: auto;
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }

  .msg-table-pagination {
    position: sticky;
    bottom: 0;
    left: 0;
    width: 100%;
    min-width: max-content;
    background: var(--msg-table-bg-color);
    padding: 8px 16px;
    border-top: 1px solid var(--msg-table-border-color);
    display: flex;
    justify-content: flex-end;
    z-index: var(--msg-table-z-index-footer);
    flex-shrink: 0;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
  }

  .msg-table {
    width: 100%;
    min-width: max-content;
    border-collapse: separate;
    border-spacing: 0;
    table-layout: auto;
    margin-bottom: 0;
    font-size: var(--msg-table-font-size);
  }

  .msg-table-unified {
    thead.msg-table-thead {
      display: table;
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      position: sticky;
      top: 0;
      z-index: var(--msg-table-z-index-header);
      background: var(--msg-table-bg-color);
      box-shadow: var(--msg-table-header-shadow);

      tr {
        display: table-row;
        width: 100%;
      }

      th {
        padding: var(--msg-table-padding);
        text-align: left;
        font-weight: var(--msg-table-font-weight);
        border-bottom: 1px solid var(--msg-table-border-color);
        border-right: 1px solid var(--msg-table-border-light);
        white-space: nowrap;
        width: var(--msg-table-cell-width);
        display: table-cell;
        position: relative;
        background: var(--msg-table-bg-color);
        user-select: text !important;

        &:last-child {
          border-right: none;
        }

        &.msg-sticky-left {
          @include respond-to-max('mobile') {
            position: static !important;
            z-index: auto !important;
            box-shadow: none !important;
          }

          @include respond-to('mobile') {
            position: sticky;
            z-index: calc(var(--msg-table-z-index-header) + 1);
          }
        }

        &.msg-sticky-right {
          position: sticky;
          z-index: calc(var(--msg-table-z-index-header) + 2);
        }
      }
    }

    tbody.msg-table-tbody {
      display: table;
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;

      tr.msg-tr-expand {

        &:hover,
        &:focus,
        &:active {
          background-color: transparent !important;

          td {
            background-color: transparent !important;
          }
        }
      }

      tr {
        display: table-row;
        width: 100%;

        &:not(.msg-tr-expand) {

          &:hover,
          &:focus,
          &:active {
            background-color: var(--msg-table-hover-bg-color);

            td {
              background-color: var(--msg-table-hover-bg-color) !important;
              white-space: nowrap !important;

              &.msg-sticky-left,
              &.msg-sticky-right {
                background-color: var(--msg-table-hover-bg-color) !important;
              }
            }
          }
        }
      }

      td {
        padding: var(--msg-table-padding-tbody);
        border-bottom: 1px solid var(--msg-table-border-light);
        border-right: 1px solid var(--msg-table-border-light);
        display: table-cell;
        width: var(--msg-table-cell-width);
        vertical-align: middle;
        white-space: nowrap !important;
        user-select: text !important;

        &:last-child {
          border-right: none;
        }

        &.msg-sticky-left {
          @include respond-to-max('mobile') {
            position: static !important;
            z-index: auto !important;
            box-shadow: none !important;
            background: inherit !important;
            // border-right: 1px solid var(--color-gray-200) !important;
          }

          @include respond-to('mobile') {
            position: sticky;
            background: var(--msg-table-bg-color);
            z-index: var(--msg-table-z-index-sticky-left);
          }
        }

        &.msg-sticky-right {
          position: sticky;
          background: var(--msg-table-bg-color);
          z-index: var(--msg-table-z-index-sticky-right);
        }
      }

      tr td[style*="display: none"] {
        pointer-events: none;
      }

      td.msg-row-hover {
        background-color: var(--msg-table-hover-bg-color) !important;

        &.msg-sticky-left,
        &.msg-sticky-right {
          background-color: var(--msg-table-hover-bg-color) !important;
        }
      }
    }

    tfoot.msg-table-tfoot {
      display: table;
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      position: sticky;
      background-color: var(--msg-table-bg-color);
      bottom: 0;
      z-index: var(--msg-table-z-index-footer);
      box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);

      tr {
        display: table-row;
        width: 100%;
      }

      td {
        padding: var(--msg-table-padding);
        text-align: left;
        font-weight: var(--msg-table-font-weight);
        border-top: 1px solid var(--msg-table-border-color);
        // border-bottom: 1px solid var(--msg-table-border-color);
        white-space: nowrap;
        width: var(--msg-table-cell-width);
        display: table-cell;
        position: relative;
        background: var(--msg-table-bg-color);
        user-select: text !important;

        &.msg-sticky-left {
          @include respond-to-max('mobile') {
            position: static !important;
            z-index: auto !important;
            box-shadow: none !important;
            background: inherit !important;
          }

          @include respond-to('mobile') {
            position: sticky;
            z-index: calc(var(--msg-table-z-index-footer) + 1);
          }
        }

        &.msg-sticky-right {
          position: sticky;
          z-index: calc(var(--msg-table-z-index-footer) + 2);
        }
      }

      tr:not(.msg-tr-expand):hover {

        td[msg-row],
        td[msg-col] {
          background-color: var(--msg-table-hover-bg-color) !important;

          &.msg-sticky-left,
          &.msg-sticky-right {
            background-color: var(--msg-table-hover-bg-color) !important;
          }
        }
      }

      td.msg-row-hover {
        background-color: var(--msg-table-hover-bg-color) !important;

        &.msg-sticky-left,
        &.msg-sticky-right {
          background-color: var(--msg-table-hover-bg-color) !important;
        }
      }
    }

    @include respond-to-max('mobile') {
      .msg-sticky-shadow-left {
        box-shadow: none !important;
      }

      .msg-sticky-left {
        position: static !important;
        z-index: auto !important;
        box-shadow: none !important;
        background: inherit !important;
      }
    }

    @include respond-to('mobile') {
      .msg-sticky-left {
        position: sticky;
        background: var(--msg-table-bg-color);
        z-index: var(--msg-table-z-index-sticky-left);
      }
    }

    .msg-sticky-right {
      position: sticky;
      background: var(--msg-table-bg-color);
      z-index: var(--msg-table-z-index-sticky-right);
    }
  }

  @include respond-to('mobile') {
    &.msg-table-scroll-left {
      .msg-table-unified {
        .msg-sticky-shadow-left {
          border-right: none !important;
        }

        .msg-sticky-shadow-left::after {
          content: "";
          position: absolute;
          top: 0;
          bottom: 0;
          right: -30px;
          width: 30px;
          pointer-events: none;
          transition: box-shadow 0.3s ease;
          box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
          z-index: 16;
        }
      }
    }
  }

  &.msg-table-scroll-right {
    .msg-table-unified {
      // .msg-sticky-shadow-right {
      // }

      .msg-sticky-shadow-right:after {
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        left: -30px;
        width: 30px;
        pointer-events: none;
        transition: box-shadow 0.3s ease;
        box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
        z-index: 16;
      }
    }
  }
}
