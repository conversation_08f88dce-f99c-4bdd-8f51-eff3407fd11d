#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const readline = require('readline');
const { colors, log } = require('./utils/console-utils');

console.log(`${colors.cyan}${colors.bright}┌${'─'.repeat(48)}┐${colors.reset}`);
console.log(
  `${colors.cyan}${colors.bright}│${' '.repeat(
    7
  )}🚀 Angular Workspace Smart Runner${' '.repeat(8)}│${colors.reset}`
);
console.log(`${colors.cyan}${colors.bright}└${'─'.repeat(48)}┘${colors.reset}`);
log.spacer();

const args = process.argv.slice(2);
const command = args[0];
const projectName = args[1];
const customPort = args[2] && /^\d+$/.test(args[2]) ? args[2] : null;
const isListCommand =
  command === 'list' || command === '--list' || command === '-l';

function getAvailableProjects() {
  try {
    const appsDir = './apps';
    if (!fs.existsSync(appsDir)) {
      return [];
    }

    const apps = fs
      .readdirSync(appsDir, { withFileTypes: true })
      .filter((dirent) => dirent.isDirectory())
      .map((dirent) => dirent.name);

    return apps;
  } catch {
    console.log('⚠ Không thể lấy danh sách projects');
    return [];
  }
}

function showAvailableProjects(action = 'chạy') {
  const projects = getAvailableProjects();

  if (projects.length === 0) {
    log.error('Không tìm thấy project nào trong workspace');
    return;
  }

  log.spacer();
  console.log(
    `${colors.blue}${colors.bright}▶ Các project có thể ${action}:${colors.reset}`
  );
  projects.forEach((project, index) => {
    const number = `${colors.cyan}${index + 1}${colors.reset}`;
    const name = `${colors.white}${colors.bright}${project}${colors.reset}`;
    console.log(`   ${number}. ${name}`);
  });
  log.spacer();

  console.log(`${colors.yellow}${colors.bright}▶ Cách sử dụng:${colors.reset}`);

  if (action === 'chạy') {
    console.log(
      `   ${colors.green}npm run start <project-name>${colors.reset}`
    );
    console.log(
      `   ${colors.green}npm run build <project-name>${colors.reset}`
    );
    console.log(
      `   ${colors.dim}Ví dụ: npm run dev ${projects[0]}${colors.reset}`
    );
    log.spacer();
    return;
  }

  if (action === 'build') {
    console.log(
      `   ${colors.green}npm run build <project-name> [env]${colors.reset}`
    );
    console.log(
      `   ${colors.dim}Ví dụ: npm run build ${projects[0]} production${colors.reset}`
    );
    console.log(
      `   ${colors.dim}Ví dụ: npm run build ${projects[0]} development${colors.reset}`
    );
  }
  log.spacer();
}

function checkAndInstallDependencies() {
  if (fs.existsSync('node_modules')) {
    log.success('Dependencies đã có sẵn');
    return;
  }

  console.log(
    `${colors.yellow}📦 Chưa có node_modules, đang cài đặt dependencies...${colors.reset}`
  );
  try {
    execSync('npm install', { stdio: 'inherit' });
    log.success('Cài đặt dependencies thành công!');
    log.spacer();
  } catch (error) {
    log.error(`Lỗi khi cài đặt dependencies: ${error.message}`);
    process.exit(1);
  }
}

function checkAndSetupHusky() {
  const huskyPath = '.husky';
  const preCommitPath = '.husky/pre-commit';
  const commitMsgPath = '.husky/commit-msg';

  if (
    fs.existsSync(huskyPath) &&
    fs.existsSync(preCommitPath) &&
    fs.existsSync(commitMsgPath)
  ) {
    log.success('Husky hooks đã có sẵn');
    log.spacer();
    return;
  }

  console.log(`${colors.yellow}🐺 Thiết lập Husky hooks...${colors.reset}`);

  try {
    if (!fs.existsSync('.husky/_/husky.sh')) {
      execSync('npx husky install', { stdio: 'inherit' });
    }

    const preCommitContent = 'npx lint-staged\n';
    fs.writeFileSync(preCommitPath, preCommitContent);
    fs.chmodSync(preCommitPath, '755');

    const commitMsgContent = 'node scripts/validate-commit.js $1\n';
    fs.writeFileSync(commitMsgPath, commitMsgContent);
    fs.chmodSync(commitMsgPath, '755');

    log.success('Husky hooks đã được thiết lập!');
  } catch (error) {
    log.error(`Lỗi khi thiết lập Husky: ${error.message}`);
    process.exit(1);
  }
}

function runProject(projectName, port) {
  const availableProjects = getAvailableProjects();

  if (!availableProjects.includes(projectName)) {
    log.error(`Project "${projectName}" không tồn tại!`);
    showAvailableProjects('chạy');
    process.exit(1);
  }

  console.log(
    `${colors.green}${colors.bright}▶ Đang khởi chạy project: ${colors.cyan}${projectName}${colors.green} (development)${port ? `, port ${port}` : ''}...${colors.reset}`
  );
  log.spacer();

  try {
    const serveArgs = ['nx', 'serve', projectName, '--configuration=development'];
    if (port) {
      serveArgs.push(`--port=${port}`);
    }
    const child = spawn(
      'npx',
      serveArgs,
      {
        stdio: 'inherit',
        shell: true,
      }
    );

    child.on('close', (code) => {
      if (code !== 0) {
        log.error(`Project dừng với mã lỗi: ${code}`);
      }
    });

    process.on('SIGINT', () => {
      console.log(`\n${colors.yellow}▶ Đang dừng project...${colors.reset}`);
      child.kill('SIGINT');
      process.exit(0);
    });
  } catch (error) {
    log.error(`Lỗi khi chạy project: ${error.message}`);
    process.exit(1);
  }
}

function buildProject(projectName) {
  const availableProjects = getAvailableProjects();

  if (!availableProjects.includes(projectName)) {
    log.error(`Project "${projectName}" không tồn tại!`);
    showAvailableProjects('build');
    process.exit(1);
  }

  console.log(
    `${colors.blue}${colors.bright}▶ Đang build project: ${colors.cyan}${projectName}${colors.blue} (production)...${colors.reset}`
  );
  log.spacer();

  const cacheFolders = [
    '.nx',
    'node_modules/.cache',
    '.angular/cache'
  ];
  cacheFolders.forEach((folder) => {
    if (fs.existsSync(folder)) {
      try {
        fs.rmSync(folder, { recursive: true, force: true });
        log.success(`Đã xoá cache: ${folder}`);
      } catch (e) {
        log.error(`Không thể xoá cache: ${folder}`);
      }
    }
  });

  try {
    execSync(`npx nx build ${projectName} --configuration=production`, {
      stdio: 'inherit',
    });
    log.spacer();
    log.success(
      `Build thành công! Output: ${colors.cyan}dist/apps/${projectName}${colors.reset}`
    );
  } catch (error) {
    log.error(`Lỗi khi build project: ${error.message}`);
    process.exit(1);
  }
}

function promptProjectSelection() {
  return new Promise((resolve) => {
    const availableProjects = getAvailableProjects();

    if (availableProjects.length === 0) {
      console.error('✗ Không tìm thấy project nào!');
      process.exit(1);
    }

    console.log(
      `${colors.blue}${colors.bright}▶ Chọn project để chạy:${colors.reset}`
    );
    availableProjects.forEach((project, index) => {
      const number = `${colors.cyan}${index + 1}${colors.reset}`;
      const name = `${colors.white}${colors.bright}${project}${colors.reset}`;
      console.log(`   ${number}. ${name}`);
    });

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    rl.question(
      '\n▶ Nhập số thứ tự project (hoặc Enter để thoát): ',
      (answer) => {
        if (!answer.trim()) {
          rl.close();
          console.log('◐ Tạm biệt!');
          process.exit(0);
        }

        const projectIndex = parseInt(answer) - 1;

        if (
          isNaN(projectIndex) ||
          projectIndex < 0 ||
          projectIndex >= availableProjects.length
        ) {
          rl.close();
          console.error('✗ Lựa chọn không hợp lệ!');
          process.exit(1);
        }

        const selectedProject = availableProjects[projectIndex];
        rl.close();
        resolve(selectedProject);
      }
    );
  });
}

function promptPortSelection() {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    rl.question(
      `▶ Nhập port muốn chạy (Enter để dùng mặc định): `,
      (answer) => {
        rl.close();
        const port = answer.trim();
        if (!port) {
          resolve(null);
        } else if (/^\d+$/.test(port)) {
          resolve(port);
        } else {
          console.error('✗ Port không hợp lệ!');
          process.exit(1);
        }
      }
    );
  });
}

async function main() {
  try {
    if (isListCommand) {
      showAvailableProjects();
      process.exit(0);
    }

    let selectedProject;
    let selectedPort = customPort;

    if (command === 'build') {
      selectedProject = projectName;

      if (!selectedProject) {
        console.log('▣ BUILD MODE');
        selectedProject = await promptProjectSelection();
      }
    }

    if (!command || command === 'start' || command === 'dev') {
      selectedProject =
        command === 'start' || command === 'dev' ? projectName : command;

      if (!selectedProject) {
        console.log('▣ DEV MODE');
        selectedProject = await promptProjectSelection();
        if (!selectedPort) {
          selectedPort = await promptPortSelection();
        }
      }
    }

    checkAndInstallDependencies();
    checkAndSetupHusky();
    if (command === 'build') {
      buildProject(selectedProject);
      return;
    }

    runProject(selectedProject, selectedPort);
  } catch (error) {
    console.error('✗ Lỗi không mong muốn:', error.message);
    process.exit(1);
  }
}

main();
