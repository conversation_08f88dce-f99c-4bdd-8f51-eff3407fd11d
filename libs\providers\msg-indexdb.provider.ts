import { APP_INITIALIZER, EnvironmentProviders, inject, makeEnvironmentProviders } from '@angular/core';
import { MsgIndexDbConfig } from 'libs/services/interfaces/msg-indexdb.interface';
import { MsgBrowserIndexDbService } from 'libs/services/msg-indexdb.service';


export function provideMsgIndexDb(config: MsgIndexDbConfig = {}): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: MsgBrowserIndexDbService,
      useFactory: () => new MsgBrowserIndexDbService(
        config.dbName,
        config.storeName,
        config.version,
        config.mode
      )
    },
    {
      provide: APP_INITIALIZER,
      useFactory: () => {
        inject(MsgBrowserIndexDbService);
        return () => Promise.resolve();
      },
      multi: true
    }
  ]);
}
