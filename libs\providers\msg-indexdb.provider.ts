import { APP_INITIALIZER, EnvironmentProviders, inject, makeEnvironmentProviders } from '@angular/core';
import { MsgIndexDbConfig } from '../services/interfaces/msg-indexdb.interface';
import { MsgIndexDbService } from '../services/msg-indexdb.service';

export function provideMsgIndexDb(config: MsgIndexDbConfig = {}): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: MsgIndexDbService,
      useFactory: () => new MsgIndexDbService(
        config.dbName,
        config.storeName,
        config.version,
        config.mode
      )
    },
    {
      provide: APP_INITIALIZER,
      useFactory: () => {
        inject(MsgIndexDbService);
        return () => Promise.resolve();
      },
      multi: true
    }
  ]);
}
