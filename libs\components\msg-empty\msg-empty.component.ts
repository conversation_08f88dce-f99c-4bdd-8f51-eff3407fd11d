import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { TYPE_MSG_ICON_SIZE_INPUT } from '../msg-icon/interfaces/msg-icon.interface';
import { MsgIconSharedComponent } from '../msg-icon/msg-icon.component';

@Component({
  selector: 'msg-empty-shared',
  templateUrl: './msg-empty.component.html',
  styleUrls: ['./msg-empty.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TranslatePipe,
    MsgIconSharedComponent
  ]
})

export class MsgEmptySharedComponent {
  public msgDescription = input<string>('');
  public msgSizeIcon = input<TYPE_MSG_ICON_SIZE_INPUT>(48);
}
