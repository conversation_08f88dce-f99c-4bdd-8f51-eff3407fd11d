import { AsyncPipe, CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, ElementRef, inject, input, OnDestroy, OnInit, output, Renderer2, signal } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { Subscription, switchMap, take, timer } from 'rxjs';
import { MsgTruncatePipe } from '../../pipes';
import { MsgDynamicComponentService } from '../../services/msg-dynamic-component.service';
import { MsgButtonSharedComponent } from '../msg-button/msg-button.component';

@Component({
  selector: 'msg-drawer-shared',
  templateUrl: './msg-drawer.component.html',
  styleUrls: ['./msg-drawer.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    MsgButtonSharedComponent,
    TranslatePipe,
    NzToolTipModule,
    MsgTruncatePipe,
    AsyncPipe,
  ],
})
export class MsgDrawerSharedComponent implements OnInit, OnDestroy {
  public msgTitle = input<string>('i18n_msg_drawer_title');
  public msgPosition = input<'left' | 'right'>('right');
  public msgWidth = input<number>(400);
  public msgShowOverlay = input<boolean>(true);
  public msgCloseOnOverlayClick = input<boolean>(true);

  public msgAfterOpen = output<void>();
  public msgAfterClose = output<void>();
  public msgOk = output<void>();
  public msgCancel = output<void>();

  protected isVisible = signal<boolean>(false);
  protected isAnimating = signal<boolean>(false);
  private _renderer2 = inject(Renderer2);
  private _elementRef = inject(ElementRef);
  private _dynamicService = inject(MsgDynamicComponentService);
  private _animationSub?: Subscription;

  protected drawerClasses = computed(() => ({
    'msg-drawer': true,
    [`msg-drawer-${this.msgPosition()}`]: true,
    'msg-drawer-visible': this.isVisible(),
    'msg-drawer-animating': this.isAnimating()
  }));

  protected overlayClasses = computed(() => ({
    'msg-drawer-overlay': true,
    'msg-drawer-overlay-visible': this.isVisible()
  }));

  protected drawerStyles = computed(() => ({
    width: `${this.msgWidth()}px`,
    [this.msgPosition()]: '0'
  }));

  constructor() {
    effect(() => {
      const method = this.isVisible() ? 'setStyle' : 'removeStyle';
      this._renderer2[method](document.body, 'overflow', 'hidden');
    });
  }

  ngOnInit(): void {
    this._animationSub?.unsubscribe();
    this.isAnimating.set(true);
    this._animationSub = timer(100).pipe(
      take(1),
      switchMap(() => {
        this.isVisible.set(true);
        return timer(300);
      }),
      take(1)
    ).subscribe(() => {
      this.isAnimating.set(false);
      this.msgAfterOpen.emit();
    });
  }

  ngOnDestroy(): void {
    this._animationSub?.unsubscribe();
    this._renderer2.removeStyle(document.body, 'overflow');
    this.isVisible.set(false);
    this.isAnimating.set(false);
  }

  protected handlerCloseDrawer(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this._closeDrawer();
    this.msgCancel.emit();
  }

  protected handlerSaveChanges(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this._closeDrawer();
    this.msgOk.emit();
  }

  protected handleOverlayClick(event: MouseEvent): void {
    if (!this.msgCloseOnOverlayClick()) {
      return;
    }

    event.preventDefault();
    event.stopPropagation();
    this._closeDrawer();
    this.msgCancel.emit();
  }

  private _closeDrawer(): void {
    this.isAnimating.set(true);
    timer(50).pipe(
      take(1),
      switchMap(() => {
        this.isVisible.set(false);
        return timer(300);
      }),
      take(1)
    ).subscribe(() => {
      this.isAnimating.set(false);
      this.msgAfterClose.emit();
      this._destroyComponent();
    });
  }

  private _destroyComponent(): void {
    const msgId = this._elementRef.nativeElement.getAttribute('msgId');
    if (msgId) {
      this._dynamicService.removeComponentById(msgId);
    }
  }
}
