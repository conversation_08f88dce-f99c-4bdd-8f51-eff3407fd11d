import { ChangeDetectionStrategy, Component, input, OnDestroy, OnInit, output, signal } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { TYPE_POPOVER_PLACEMENT } from '../../interfaces/common.interface';
import { MsgIconSharedComponent } from '../msg-icon/msg-icon.component';
import { IMsgPopoverFunctionControl } from '../msg-popover/interfaces/msg-popover.interface';
import { MsgPopoverSharedComponent } from '../msg-popover/msg-popover.component';
import { menuActionDefine } from './defines/msg-menu-action.define';
import { IMsgMenuActionItemDto } from './interfaces/msg-menu-action.interface';

@Component({
  selector: 'msg-menu-action-shared',
  templateUrl: './msg-menu-action.component.html',
  styleUrls: ['./msg-menu-action.component.scss'],
  standalone: true,
  imports: [
    MsgIconSharedComponent,
    TranslatePipe,
    MsgPopoverSharedComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MsgMenuActionSharedComponent implements OnInit, OnDestroy {
  public msgActions = input<IMsgMenuActionItemDto[]>(menuActionDefine());
  public msgPlacement = input<TYPE_POPOVER_PLACEMENT>('bottomRight');
  public msgClickAction = output<IMsgMenuActionItemDto['type']>();
  public msgVisibilityChange = output<boolean>();
  protected popoverControl = signal<IMsgPopoverFunctionControl | null>(null);

  ngOnInit(): void {
    window.addEventListener('scroll', () => {
      this.popoverControl()?.hide();
    }, { capture: true });
  }

  ngOnDestroy(): void {
    window.removeEventListener('scroll', () => {
      this.popoverControl()?.hide();
    }, { capture: true });
    this.popoverControl.set(null);
  }

  protected onActionClick(event: MouseEvent, action: IMsgMenuActionItemDto['type']): void {
    event.stopPropagation();
    this.popoverControl()?.hide();
    this.msgClickAction.emit(action);
  }

  protected onFunctionControl(control: IMsgPopoverFunctionControl): void {
    this.popoverControl.set(control);
  }

}
