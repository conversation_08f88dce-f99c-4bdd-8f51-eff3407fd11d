import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, OnInit, signal, TemplateRef, viewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MsgButtonSharedComponent } from '@libs/components/msg-button';
import { MsgHeaderTitleSharedComponent } from '@libs/components/msg-header-title';
import { MsgMenuActionSharedComponent } from '@libs/components/msg-menu-action';
import { IMsgModalFunctionControl } from '@libs/components/msg-modal/interfaces';
import { MsgPopoverSharedComponent } from '@libs/components/msg-popover';
import { IMsgPopoverFunctionControl } from '@libs/components/msg-popover/interfaces';
import { MsgSpinnerSharedComponent } from '@libs/components/msg-spinner';
import { MsgTableModule } from '@libs/components/msg-table';
import { IMsgTableFunctionControl, IMsgTablePagination } from '@libs/components/msg-table/interfaces';
import { MsgTabsSharedComponent } from '@libs/components/msg-tabs';
import { IMsgTabDto } from '@libs/components/msg-tabs/interfaces';
import { MsgBrowserIndexDbService, MsgDynamicComponentService, MsgModalService, MsgNotificationService } from '@libs/services';
import { NzNoAnimationModule } from 'ng-zorro-antd/core/no-animation';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { finalize, tap, timer } from 'rxjs';
import { dashboardDefine, expandTemplate, getDashboardTableConfig, IDashboardData, listHospital, tabDashboard } from './defines/dashboard.define';

@Component({
  selector: 'msg-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzDatePickerModule,
    NzSelectModule,
    NzToolTipModule,
    NzNoAnimationModule,
    NzDrawerModule,
    MsgButtonSharedComponent,
    MsgTableModule,
    MsgMenuActionSharedComponent,
    MsgTabsSharedComponent,
    MsgSpinnerSharedComponent,
    MsgPopoverSharedComponent,
    MsgHeaderTitleSharedComponent
  ]
})
export class MsgDashboardComponent implements OnInit {
  protected actionTemplate = viewChild.required<TemplateRef<HTMLElement>>('actionTemplate');
  protected expandTemplate = viewChild.required<TemplateRef<HTMLElement>>('expandTemplate');
  protected cellValueTemplate = viewChild.required<TemplateRef<HTMLElement>>('cellValueTemplate');

  protected date = signal<Date | null>(null);
  protected selectedSite = signal<string | null>(null);
  protected dashboardData = signal<IDashboardData[]>([]);
  protected loading = signal<boolean>(false);
  protected actionVisibility = signal<Record<number, boolean>>({});
  protected modalControl: IMsgModalFunctionControl | null = null;
  protected popoverControl: IMsgPopoverFunctionControl | null = null;
  private _tableControl!: IMsgTableFunctionControl;
  protected listOfSelectedValue = signal<string[]>([]);;

  private _notificationService = inject(MsgNotificationService);
  private _modalService = inject(MsgModalService);
  private _indexDb = inject(MsgBrowserIndexDbService);
  private _dynamicService = inject(MsgDynamicComponentService);
  protected listOfSite = signal<string[]>(listHospital());
  protected tabs = signal<IMsgTabDto[]>(tabDashboard() as any);
  protected activeTab = signal<IMsgTabDto | null>(this.tabs()[0]);
  public tableConfig = computed(() => getDashboardTableConfig(this.actionTemplate(), this.expandTemplate()));
  public expandTableConfig = computed(() => expandTemplate());

  constructor() {
    this._loadDashboardData();
  }

  ngOnInit(): void { }

  protected handlerChangeDate(date: Date | null): void {
    this.date.set(date);
  }

  protected handlerFilter(): void {
    this._notificationService.info('Filter button clicked');
  }

  public async handlerActionClick(action: string, item: IDashboardData): Promise<void> {
    if (action === 'edit') {
      const drawerComp = await import('./dash-modal/dash-modal.component').then(m => m.MsgDashModalComponent);
      const compRef = this._dynamicService.addToBody(drawerComp);
      compRef.setInput('dashTitle', 'Chỉnh sửa hạng mục');
      compRef.setInput('dashItem', item);

      const subOk = compRef.instance.msgOk.subscribe(() => {
        this._notificationService.success('Chỉnh sửa hạng mục thành công');
        subOk.unsubscribe();
      });

      const subClose = compRef.instance.msgClose.subscribe(() => {
        subClose.unsubscribe();
      });
    }

    if (action === 'delete') {
      const modal = this._modalService.danger({
        title: 'Xóa hạng mục',
        content: `Bạn có chắc chắn muốn xóa hạng mục "${item.items}" không?`,
        textOk: 'Xóa',
        textCancel: 'Hủy'
      });

      modal.instance.msgOk.subscribe(() => {
        modal.setLoading(true);
        timer(1000).pipe(
          tap(() => {
            this._tableControl.onDelete(item);
            this._notificationService.success('Xóa hạng mục thành công');
            this._loadDashboardData();
          }),
          finalize(() => {
            modal.setLoading(false);
            modal.remove();
          })
        ).subscribe();
      });
    }
  }

  protected onFunctionControlTable(control: IMsgTableFunctionControl): void {
    this._tableControl = control;
  }

  protected handleTabChange(tab: IMsgTabDto): void {
    this.activeTab.set(tab);
  }

  protected onPageChange(event: IMsgTablePagination): void {
    console.log('Page change:', event);
  }

  protected showSuccessModal(): void {
    const modalRef = this._modalService.success({
      title: 'Export Data',
      content: 'Are you sure you want to export the data?'
    });

    const sub = modalRef.instance.msgOk.subscribe(() => {
      modalRef.remove();
      sub.unsubscribe();
    });
  }

  protected async showNotifyModal(): Promise<void> {
    this._notificationService.info('Notify button clicked');
    await this._indexDb.set('D_TOKEN', '123456');
    await this._indexDb.set('D_USER_INFO', { username: 'Vu Ngoc Boi' });
    const value = await this._indexDb.get('D_TOKEN');
    const userInfo = await this._indexDb.get('D_USER_INFO');
    const data = await this._indexDb.getAll();
    console.log("🚀 ~ MsgDashboardComponent ~ showNotifyModal ~ data:", data);
    // console.log("🚀 ~ MsgDashboardComponent ~ showNotifyModal ~ userInfo:", userInfo);
    // console.log("🚀 ~ MsgDashboardComponent ~ showNotifyModal ~ value:", value);
  }

  protected async handlerReport(): Promise<void> {
    const modalComp = await import('@libs/components/msg-modal').then(m => m.MsgModalSharedComponent);
    const compRef = this._dynamicService.addToBody(modalComp);
    compRef.setInput('msgTitle', 'Export Financial Data');
    compRef.setInput('msgContent', 'Bạn có chắc chắn muốn xuất báo cáo tài chính không?');
    compRef.instance.show();

    const subCloseRef = compRef.instance.msgAfterClose.subscribe(() => {
      subCloseRef.unsubscribe();
    });

    const subCompRef = compRef.instance.msgOk.subscribe(() => {
      const modalRef = this._modalService.success({
        title: 'Thành công',
        content: 'Xuất báo cáo thành công!',
      });

      const subModalRef = modalRef.instance.msgOk.subscribe(() => {
        modalRef.setLoading(true);
        timer(500).pipe(
          finalize(() => {
            modalRef.setLoading(false);
            this._notificationService.success('Xuất báo cáo tài chính thành công! Vui lòng kiểm tra thư mục tải xuống.');
          }),
          tap(() => {
            modalRef.remove();
            this._dynamicService.removeComponent(compRef);
            subModalRef.unsubscribe();
            subCompRef.unsubscribe();
          })
        ).subscribe();
      });
    });
  }

  private _loadDashboardData(): void {
    this.loading.set(true);
    timer(1000).pipe(
      tap(() => {
        const data = dashboardDefine();
        this.dashboardData.set(data);
        this.loading.set(false);
      })
    ).subscribe();
  }

  protected handlerPopover(): void {
    this.popoverControl?.hide();
  }

  protected onFunctionControl(control: IMsgPopoverFunctionControl): void {
    this.popoverControl = control;
  }

}
