import { Pipe, PipeTransform } from '@angular/core';
import { TYPE_NUMBER_FORMAT } from '../utils/interfaces/msg-common.interface';
import { formatMsgNumber } from '../utils/msg-common.util';

@Pipe({
  name: 'msgFormatNumber',
  standalone: true
})

export class MsgFormatNumberPipe implements PipeTransform {
  transform(value: any, divideBillion = false, numberFormat: TYPE_NUMBER_FORMAT = '1.0-2'): string {
    return formatMsgNumber(value, divideBillion, numberFormat);
  }
}
