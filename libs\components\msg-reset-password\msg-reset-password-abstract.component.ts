import { Directive, inject, input, signal } from "@angular/core";
import { <PERSON>bstractControl, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { TYPE_FORM } from "../../interfaces/form.interface";
import { MsgNotificationService } from "../../services/msg-notification.service";
import { MsgTranslateService } from "../../services/msg-translate.service";
import { markFormTouchedAndEmit } from "../../utils";
import { IMsgResetPasswordDto } from "./interfaces/msg-reset-password.interface";

@Directive({})
export class MsgResetPasswordAbstractComponent {
  public msgLogo = input<string>('/assets/images/msg-logo-msg-connect.png');
  public msgTitle = input<string>('i18n_msg_connect');
  public msgDescription = input<string>('i18n_msg_description');
  protected msgShowPassword = signal<boolean>(false);
  protected msgShowConfirmPassword = signal<boolean>(false);
  protected msgLoading = signal<boolean>(false);

  protected msgFormResetPassword!: FormGroup<TYPE_FORM<IMsgResetPasswordDto>>;
  protected fb = inject(FormBuilder);
  protected router = inject(Router);
  protected notificationService = inject(MsgNotificationService);
  protected translate = inject(MsgTranslateService);

  constructor() {
    this.initForm();
  }

  protected initForm(): void {
    this.msgFormResetPassword = this.fb.group({
      email: ['', [Validators.required, this._emailValidator]],
    });
    // this.msgFormResetPassword = this.fb.group({
    //   email: ['', [Validators.required, Validators.email]],
    //   password: ['', [Validators.required, Validators.minLength(6)]],
    //   confirmPassword: ['', [Validators.required]]
    // }, {
    //   validators: this._passwordMatchValidator
    // });
  }

  private _emailValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@matsaigon\.com$/;
    if (control.value && !emailRegex.test(control.value)) {
      return { 'validMailMsg': true };
    }

    return null;
  }

  private _passwordMatchValidator(formGroup: FormGroup): { [key: string]: boolean } | null {
    const passwordControl = formGroup.get('password');
    const confirmPasswordControl = formGroup.get('confirmPassword');

    if (!passwordControl || !confirmPasswordControl) {
      return null;
    }

    const password = passwordControl.value;
    const confirmPassword = confirmPasswordControl.value;

    const currentErrors = confirmPasswordControl.errors || {};

    if (password !== confirmPassword && confirmPassword) {
      confirmPasswordControl.setErrors({
        ...currentErrors,
        'passwordMismatch': true
      });
      return null;
    }

    if (!currentErrors['passwordMismatch']) {
      return null;
    }

    const remainingErrors = { ...currentErrors };
    delete remainingErrors['passwordMismatch'];

    if (Object.keys(remainingErrors).length > 0) {
      confirmPasswordControl.setErrors(remainingErrors);
      return null;
    }

    confirmPasswordControl.setErrors(null);
    return null;
  }

  protected toggleShowPassword(event: MouseEvent): void {
    event.preventDefault();
    this.msgShowPassword.set(!this.msgShowPassword());
  }

  protected toggleShowConfirmPassword(event: MouseEvent): void {
    event.preventDefault();
    this.msgShowConfirmPassword.set(!this.msgShowConfirmPassword());
  }

  protected handlerResetPassword(): void {
    markFormTouchedAndEmit(this.msgFormResetPassword);
    if (this.msgFormResetPassword.invalid) {
      return;
    }

    this.msgLoading.set(true);
    setTimeout(() => {
      this.notificationService.success(this.translate.instant('i18n_reset_password_success'));
      this.msgLoading.set(false);
    }, 2000);
  }

  protected handlerBackToLogin(): void {
    if (this.msgLoading()) {
      return;
    }

    this.router.navigateByUrl('/login');
  }
}
