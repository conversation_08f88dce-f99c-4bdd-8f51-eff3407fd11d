.msg-skeleton-container {
  display: block;
  width: 100%;
  height: 100%;
}

.msg-skeleton {
  background-color: #e0e0e0;
  border-radius: 4px;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    animation: shimmer 1.5s infinite;
  }

  &-circle {
    border-radius: 50%;
    aspect-ratio: 1/1;

    &-size-sm {
      width: 32px;
      height: 32px;
    }

    &-size-md {
      width: 48px;
      height: 48px;
    }

    &-size-lg {
      width: 64px;
      height: 64px;
    }
  }

  &-bar {
    width: 100%;
    height: 100%;
  }

  &-bar-row {
    width: 100%;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}
