export type TYPE_NOTIFICATION = 'info' | 'warning' | 'danger' | 'success';
export type TYPE_NOTIFICATION_POSITION =
  'top-left' | 'top-right' | 'top-center' |
  'bottom-left' | 'bottom-right' | 'bottom-center';

export interface IMsgNotificationDto {
  id: string;
  type: TYPE_NOTIFICATION;
  message: string;
  duration?: number;
  position?: TYPE_NOTIFICATION_POSITION;
  isHiding?: boolean;
  isPaused?: boolean;
}

export interface IMsgNotificationTimerDto {
  id: string;
  timestamp: number;
  timerId?: number;
  pausedAt?: number;
  remainingTime?: number;
}

export interface IMsgNotificationConfigDto {
  position?: TYPE_NOTIFICATION_POSITION;
  duration?: number;
  showProgressBar?: boolean;
  maxVisible?: number;
  preventDuplicate?: boolean;
}
