import { ChangeDetectionStrategy, Component, effect, inject, input, model, OnInit, output, signal } from "@angular/core";
import { NavigationEnd, Router } from "@angular/router";
import { NzButtonModule } from "ng-zorro-antd/button";
import { NzIconModule } from "ng-zorro-antd/icon";
import { NzMenuModule } from "ng-zorro-antd/menu";
import { NzToolTipModule } from "ng-zorro-antd/tooltip";
import { MsgClickOutsideDirective } from '../../directives/msg-click-outside.directive';
import { MsgButtonSharedComponent } from "../msg-button/msg-button.component";
import { IMsgControlSidebarMenuDto, IMsgMenuDto } from "./interfaces";
import { MsgMenuItemComponent } from "./msg-menu-item/msg-menu-item.component";

@Component({
  selector: "msg-sidebar-menu-shared",
  templateUrl: "./msg-sidebar-menu.component.html",
  styleUrls: ["./msg-sidebar-menu.component.scss"],
  standalone: true, imports: [
    NzMenuModule,
    NzButtonModule,
    NzToolTipModule,
    NzIconModule,
    MsgMenuItemComponent,
    MsgButtonSharedComponent,
    MsgClickOutsideDirective,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class MsgSidebarMenuSharedComponent implements OnInit {
  public msgLogo = input<string>('/assets/images/msg-logo-msg-connect.png');
  public msgLogoMobile = input<string>('/assets/images/msg-logo-msg-connect.png');
  public msgMenus = input<IMsgMenuDto[], IMsgMenuDto[]>([], {
    transform: (menu) => {
      const menusSetup = this._setupMenus(menu);
      this.msgMenusProcess.set(menusSetup);
      return menusSetup;
    }
  });
  public msgCollapsed = model<boolean>(true);
  public msgFunctionControl = output<IMsgControlSidebarMenuDto>({});

  public msgIsDesktop = signal<boolean>(window.innerWidth >= 768);
  protected msgMenuChangeUpdateParent = signal<string | null>(null);
  protected msgMenusProcess = signal<IMsgMenuDto[]>([]);
  protected msgMenuActive = signal<IMsgMenuDto | undefined>(undefined);
  protected msgIsAnimating = signal<boolean>(false);

  private _router = inject(Router);
  private _animationTimeout?: number;

  constructor() {
    effect(() => {
      const msgMenus = this.msgMenus();
      const menusSetup = this._setupMenus(msgMenus);
      this.msgMenusProcess.set(menusSetup);
    });
  }

  ngOnInit(): void {
    this._functionControl();
    this._activeInitRouter();
    this._listenRouterChange();
  }

  private _listenRouterChange(): void {
    this._router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this._activeInitRouter();
      }
    });
  }

  private _activeInitRouter(): void {
    const menuActive = this._findMenuByPath(this.msgMenusProcess(), this._router.url);
    if (!menuActive) {
      return;
    }

    if (!menuActive?.parents?.length) {
      const menuActive = this.msgMenusProcess().find(menu => menu.id === menuActive?.id) as IMsgMenuDto | undefined;
      this.handlerMenuActive(menuActive);
      return;
    }

    this._setExpandedMenus(this.msgMenusProcess(), menuActive);
  }

  private _setExpandedMenus(menus: IMsgMenuDto[], menuActive: IMsgMenuDto): void {
    const parents = [...(menuActive.parents || [])];
    const expandChild = (menus: IMsgMenuDto[], parents: string[]) => {
      const parent = parents.shift();
      if (!parent) {
        const activeMenu = menus.find(m => m.id === menuActive.id);
        this.handlerMenuActive(activeMenu);
        return;
      }

      const menu = menus.find(m => m.id === parent);
      if (!menu || !menu.children?.length) {
        return;
      }

      menu.expanded = true;
      expandChild(menu.children, parents);
    };

    expandChild(menus, parents);
  }

  protected handlerMenuActive(menu: IMsgMenuDto | undefined | null): void {
    const menuActive = this.msgMenuActive();
    if (menuActive === menu || !menu) {
      return;
    }

    this._setDeActiveMenu(this.msgMenusProcess());
    menu.active = true;
    this.msgMenuActive.set(menu);
    this.msgMenuChangeUpdateParent.set(menu.id);
    this._router.navigate([menu.path || '']);
  }

  private _setDeActiveMenu(menus: IMsgMenuDto[]): void {
    const menuLastActive = this.msgMenuActive();
    if (!menuLastActive) {
      return;
    }

    const parents = [...(menuLastActive.parents || [])];
    const deActiveChild = (menus: IMsgMenuDto[], parents: string[]) => {
      const parent = parents.shift();
      if (!parent) {
        const menuActive = menus.find(m => m.id === menuLastActive.id);
        if (menuActive) {
          menuActive.active = false;
        }
        return;
      }

      const menu = menus.find(m => m.id === parent);
      if (!menu || !menu.children?.length) {
        return;
      }

      deActiveChild(menu.children, parents);
    };

    deActiveChild(menus, parents);
  }

  protected handlerToggleCollapsed(): void {
    // Clear previous timeout if exists
    if (this._animationTimeout) {
      clearTimeout(this._animationTimeout);
    }

    // Set animating state for performance optimization
    this.msgIsAnimating.set(true);
    this.msgCollapsed.set(!this.msgCollapsed());

    // Remove animating state after animation completes
    this._animationTimeout = window.setTimeout(() => {
      this.msgIsAnimating.set(false);
    }, 300); // Match transition duration
  }

  private _functionControl(): void {
    this.msgFunctionControl.emit({
      toggleCollapsed: this.handlerToggleCollapsed.bind(this),
    });
  }

  private _setupMenus(menus: IMsgMenuDto[], level = 0, parent?: IMsgMenuDto): IMsgMenuDto[] {
    return menus.map(menu => {
      const parents = (parent ? [...(parent.parents || []), parent.id] : []);
      return {
        ...menu,
        level,
        expanded: menu.expanded ?? false,
        active: menu.active ?? false,
        parents,
        children: menu.children ? this._setupMenus(menu.children, level + 1, { ...menu, parents }) : [],
      };
    });
  }


  public onToggleSidebar(): void {
    this.handlerToggleCollapsed();
  }

  public handleClickOutside(): void {
    // TODO Change check device
    if (!this.msgCollapsed() && this._isMobileOrTablet()) {
      this.msgCollapsed.set(true);
    }
  }

  public handleOverlayClick(event: MouseEvent): void {
    event.stopPropagation();
    this.msgCollapsed.set(true);
  }

  private _isMobileOrTablet(): boolean {
    return window.innerWidth <= 1024;
  }

  private _findMenuByPath(menus: IMsgMenuDto[], path: string | null): IMsgMenuDto | undefined {
    if (!path) {
      return undefined;
    }

    for (const menu of menus) {
      if (`/${menu.path}` === path) {
        return menu;
      }

      if (menu.children?.length) {
        const found = this._findMenuByPath(menu.children, path);
        if (found) {
          return found;
        }
      }
    }

    return undefined;
  }
}
