import { ChangeDetectionStrategy, Component, computed, effect, inject, input, model, On<PERSON><PERSON>roy, OnInit, output, signal } from "@angular/core";
import { toSignal } from "@angular/core/rxjs-interop";
import { NavigationEnd, Router } from "@angular/router";
import { NzButtonModule } from "ng-zorro-antd/button";
import { NzIconModule } from "ng-zorro-antd/icon";
import { NzMenuModule } from "ng-zorro-antd/menu";
import { NzToolTipModule } from "ng-zorro-antd/tooltip";
import { MsgIndexDbDefine } from "../../constants/msg-indexdb.define";
import { MsgClickOutsideDirective } from '../../directives/msg-click-outside.directive';
import { MsgDeviceService, MsgIndexDbService } from "../../services";
import { ENUM_DEVICE_TYPE } from "../../services/interfaces/msg-device.interface";
import { MsgButtonSharedComponent } from "../msg-button/msg-button.component";
import { IMsgControlSidebarMenuDto, IMsgMenuDto } from "./interfaces";
import { MsgMenuItemComponent } from "./msg-menu-item/msg-menu-item.component";

@Component({
  selector: "msg-sidebar-menu-shared",
  templateUrl: "./msg-sidebar-menu.component.html",
  styleUrls: ["./msg-sidebar-menu.component.scss"],
  standalone: true, imports: [
    NzMenuModule,
    NzButtonModule,
    NzToolTipModule,
    NzIconModule,
    MsgMenuItemComponent,
    MsgButtonSharedComponent,
    MsgClickOutsideDirective,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class MsgSidebarMenuSharedComponent implements OnInit, OnDestroy {
  public msgLogo = input<string>('/assets/images/msg-logo-msg-connect.png');
  public msgLogoMobile = input<string>('/assets/images/msg-logo-msg-connect.png');
  public msgMenus = input<IMsgMenuDto[], IMsgMenuDto[]>([], {
    transform: (menu) => {
      const menusSetup = this._setupMenus(menu);
      this.msgMenusProcess.set(menusSetup);
      return menusSetup;
    }
  });
  public msgCollapsed = model<boolean>(false);
  public msgFunctionControl = output<IMsgControlSidebarMenuDto>({});

  protected msgIsDesktop = computed(() => this._msgDevice() === ENUM_DEVICE_TYPE.TABLET);
  protected msgMenuChangeUpdateParent = signal<string | null>(null);
  protected msgMenusProcess = signal<IMsgMenuDto[]>([]);
  protected msgMenuActive = signal<IMsgMenuDto | undefined>(undefined);
  protected msgIsAnimating = signal<boolean>(false);
  private _animationTimeout = signal<number | null>(null);

  private _router = inject(Router);
  private _msgDeviceService = inject(MsgDeviceService);
  private _msgIndexDb = inject(MsgIndexDbService);
  private _msgDevice = toSignal(this._msgDeviceService.deviceType$);

  constructor() {
    effect(() => {
      const msgMenus = this.msgMenus();
      const menusSetup = this._setupMenus(msgMenus);
      this.msgMenusProcess.set(menusSetup);
    });
  }

  ngOnInit(): void {
    this._functionControl();
    this._activeInitRouter();
    this._listenRouterChange();
    this._getStateToggle();
  }

  private async _getStateToggle(): Promise<void> {
    const stateToggle = await this._msgIndexDb.get<boolean>(MsgIndexDbDefine.D_MENU_SIDEBAR_TOGGLE, false);
    this.msgCollapsed.set(stateToggle || false);
  }

  private _listenRouterChange(): void {
    this._router.events.subscribe(event => {
      if (event instanceof NavigationEnd) {
        this._activeInitRouter();
      }
    });
  }

  private _activeInitRouter(): void {
    const menuActive = this._findMenuByPath(this.msgMenusProcess(), this._router.url);
    if (!menuActive) {
      return;
    }

    if (!menuActive?.parents?.length) {
      const menuActive = this.msgMenusProcess().find(menu => menu.id === menuActive?.id) as IMsgMenuDto | undefined;
      this.handlerMenuActive(menuActive);
      return;
    }

    this._setExpandedMenus(this.msgMenusProcess(), menuActive);
  }

  private _setExpandedMenus(menus: IMsgMenuDto[], menuActive: IMsgMenuDto): void {
    const parents = [...(menuActive.parents || [])];
    const expandChild = (menus: IMsgMenuDto[], parents: string[]) => {
      const parent = parents.shift();
      if (!parent) {
        const activeMenu = menus.find(m => m.id === menuActive.id);
        this.handlerMenuActive(activeMenu);
        return;
      }

      const menu = menus.find(m => m.id === parent);
      if (!menu || !menu.children?.length) {
        return;
      }

      menu.expanded = true;
      expandChild(menu.children, parents);
    };

    expandChild(menus, parents);
  }

  protected handlerMenuActive(menu: IMsgMenuDto | undefined | null): void {
    const menuActive = this.msgMenuActive();
    if (menuActive === menu || !menu) {
      return;
    }

    this._setDeActiveMenu(this.msgMenusProcess());
    menu.active = true;
    this.msgMenuActive.set(menu);
    this.msgMenuChangeUpdateParent.set(menu.id);
    this._router.navigate([menu.path || '']);
  }

  private _setDeActiveMenu(menus: IMsgMenuDto[]): void {
    const menuLastActive = this.msgMenuActive();
    if (!menuLastActive) {
      return;
    }

    const parents = [...(menuLastActive.parents || [])];
    const deActiveChild = (menus: IMsgMenuDto[], parents: string[]) => {
      const parent = parents.shift();
      if (!parent) {
        const menuActive = menus.find(m => m.id === menuLastActive.id);
        if (menuActive) {
          menuActive.active = false;
        }
        return;
      }

      const menu = menus.find(m => m.id === parent);
      if (!menu || !menu.children?.length) {
        return;
      }

      deActiveChild(menu.children, parents);
    };

    deActiveChild(menus, parents);
  }

  protected handlerToggleCollapsed(): void {
    const animationTimeout = this._animationTimeout();
    if (animationTimeout) {
      clearTimeout(animationTimeout);
    }
    this.msgIsAnimating.set(true);
    this.msgCollapsed.set(!this.msgCollapsed());
    this._animationTimeout.set(window.setTimeout(() => {
      this.msgIsAnimating.set(false);
    }, 300));

    // Save the collapsed state to IndexedDB only for desktop mode
    if (this._msgDevice() === ENUM_DEVICE_TYPE.DESKTOP) {
      this._msgIndexDb.set(MsgIndexDbDefine.D_MENU_SIDEBAR_TOGGLE, this.msgCollapsed());
    }
  }

  private _functionControl(): void {
    this.msgFunctionControl.emit({
      toggleCollapsed: this.handlerToggleCollapsed.bind(this),
    });
  }

  private _setupMenus(menus: IMsgMenuDto[], level = 0, parent?: IMsgMenuDto): IMsgMenuDto[] {
    return menus.map(menu => {
      const parents = (parent ? [...(parent.parents || []), parent.id] : []);
      return {
        ...menu,
        level,
        expanded: menu.expanded ?? false,
        active: menu.active ?? false,
        parents,
        children: menu.children ? this._setupMenus(menu.children, level + 1, { ...menu, parents }) : [],
      };
    });
  }

  public onToggleSidebar(): void {
    this.handlerToggleCollapsed();
  }

  public handleClickOutside(): void {
    const deviceType = this._msgDeviceService.currentDeviceType;
    if (!this.msgCollapsed() && (deviceType === ENUM_DEVICE_TYPE.MOBILE || deviceType === ENUM_DEVICE_TYPE.TABLET)) {
      this.msgCollapsed.set(true);
    }
  }

  public handleOverlayClick(event: MouseEvent): void {
    event.stopPropagation();
    this.msgCollapsed.set(true);
  }

  private _findMenuByPath(menus: IMsgMenuDto[], path: string | null): IMsgMenuDto | undefined {
    if (!path) {
      return undefined;
    }

    for (const menu of menus) {
      if (`/${menu.path}` === path) {
        return menu;
      }

      if (menu.children?.length) {
        const found = this._findMenuByPath(menu.children, path);
        if (found) {
          return found;
        }
      }
    }

    return undefined;
  }

  ngOnDestroy(): void {
    const animationTimeout = this._animationTimeout();
    if (animationTimeout) {
      clearTimeout(animationTimeout);
    }
  }
}
