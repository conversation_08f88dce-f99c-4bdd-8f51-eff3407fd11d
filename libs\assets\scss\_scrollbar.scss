.os-theme-dark {
  --os-handle-bg: var(--color-gray-300) !important;
  --os-handle-bg-hover: var(--color-gray-400) !important;
  --os-handle-bg-active: var(--color-gray-500) !important;
  --os-size: 10px;
  --os-padding-perpendicular: 2px;
  --os-padding-axis: 0px;
  --os-track-border-radius: 10px;
  --os-handle-interactive-area-offset: 0px;
  --os-handle-border-radius: 10px;
}

.dark .os-theme-dark {
  --os-handle-bg: var(--color-neutral-300) !important;
  --os-handle-bg-hover: var(--color-neutral-400) !important;
  --os-handle-bg-active: var(--color-neutral-500) !important;
}

* {

  &::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-gray-200);
    border-radius: 3px;

    &:hover {
      background: var(--color-gray-300);
    }

    &:active {
      background: var(--color-gray-400);
    }
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* For Firefox */
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}
