/* @import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap'); */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import "tailwindcss";

:root {
  /* --font-sans: "Be Vietnam Pro", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: "Be Vietnam Pro", ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: "Be Vietnam Pro", SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; */
  --font-sans: "Inter", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: "Inter", ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: "Inter", SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  --color-red-50: oklch(0.971 0.013 17.38);
  --color-red-100: oklch(0.936 0.032 17.717);
  --color-red-200: oklch(0.885 0.062 18.334);
  --color-red-300: oklch(0.808 0.114 19.571);
  --color-red-400: oklch(0.704 0.191 22.216);
  --color-red-500: oklch(0.637 0.237 25.331);
  --color-red-600: oklch(0.577 0.245 27.325);
  --color-red-700: oklch(0.505 0.213 27.518);
  --color-red-800: oklch(0.444 0.177 26.899);
  --color-red-900: oklch(0.396 0.141 25.723);
  --color-red-950: oklch(0.258 0.092 26.042);

  --color-orange-50: oklch(0.98 0.016 73.684);
  --color-orange-100: oklch(0.954 0.038 75.164);
  --color-orange-200: oklch(0.901 0.076 70.697);
  --color-orange-300: oklch(0.837 0.128 66.29);
  --color-orange-400: oklch(0.75 0.183 55.934);
  --color-orange-500: oklch(0.705 0.213 47.604);
  --color-orange-600: oklch(0.646 0.222 41.116);
  --color-orange-700: oklch(0.553 0.195 38.402);
  --color-orange-800: oklch(0.47 0.157 37.304);
  --color-orange-900: oklch(0.408 0.123 38.172);
  --color-orange-950: oklch(0.266 0.079 36.259);

  --color-lime-50: oklch(0.986 0.031 120.757);
  --color-lime-100: oklch(0.967 0.067 122.328);
  --color-lime-200: oklch(0.938 0.127 124.321);
  --color-lime-300: oklch(0.897 0.196 126.665);
  --color-lime-400: oklch(0.841 0.238 128.85);
  --color-lime-500: oklch(0.768 0.233 130.85);
  --color-lime-600: oklch(0.648 0.2 131.684);
  --color-lime-700: oklch(0.532 0.157 131.589);
  --color-lime-800: oklch(0.453 0.124 130.933);
  --color-lime-900: oklch(0.405 0.101 131.063);
  --color-lime-950: oklch(0.274 0.072 132.109);

  --color-green-50: oklch(0.982 0.018 155.826);
  --color-green-100: oklch(0.962 0.044 156.743);
  --color-green-200: oklch(0.925 0.084 155.995);
  --color-green-300: oklch(0.871 0.15 154.449);
  --color-green-400: oklch(0.792 0.209 151.711);
  --color-green-500: oklch(0.723 0.219 149.579);
  --color-green-600: oklch(0.627 0.194 149.214);
  --color-green-700: oklch(0.527 0.154 150.069);
  --color-green-800: oklch(0.448 0.119 151.328);
  --color-green-900: oklch(0.393 0.095 152.535);
  --color-green-950: oklch(0.266 0.065 152.934);

  --color-sky-50: oklch(0.977 0.013 236.62);
  --color-sky-100: oklch(0.951 0.026 236.824);
  --color-sky-200: oklch(0.901 0.058 230.902);
  --color-sky-300: oklch(0.828 0.111 230.318);
  --color-sky-400: oklch(0.746 0.16 232.661);
  --color-sky-500: oklch(0.685 0.169 237.323);
  --color-sky-600: oklch(0.588 0.158 241.966);
  --color-sky-700: oklch(0.5 0.134 242.749);
  --color-sky-800: oklch(0.443 0.11 240.79);
  --color-sky-900: oklch(0.391 0.09 240.876);
  --color-sky-950: oklch(0.293 0.066 243.157);

  --color-blue-50: oklch(0.97 0.014 254.604);
  --color-blue-100: oklch(0.932 0.032 255.585);
  --color-blue-200: oklch(0.882 0.059 254.128);
  --color-blue-300: oklch(0.809 0.105 251.813);
  --color-blue-400: oklch(0.707 0.165 254.624);
  --color-blue-500: oklch(0.623 0.214 259.815);
  --color-blue-600: oklch(0.546 0.245 262.881);
  --color-blue-700: oklch(0.488 0.243 264.376);
  --color-blue-800: oklch(0.424 0.199 265.638);
  --color-blue-900: oklch(0.379 0.146 265.522);
  --color-blue-950: oklch(0.282 0.091 267.935);

  --color-gray-50: oklch(0.985 0.002 247.839);
  --color-gray-100: oklch(0.967 0.003 264.542);
  --color-gray-200: oklch(0.928 0.006 264.531);
  --color-gray-300: oklch(0.872 0.01 258.338);
  --color-gray-400: oklch(0.707 0.022 261.325);
  --color-gray-500: oklch(0.551 0.027 264.364);
  --color-gray-600: oklch(0.446 0.03 256.802);
  --color-gray-700: oklch(0.373 0.034 259.733);
  --color-gray-800: oklch(0.278 0.033 256.848);
  --color-gray-900: oklch(0.21 0.034 264.665);
  --color-gray-950: oklch(0.13 0.028 261.692);

  --color-slate-50: oklch(0.984 0.003 247.858);
  --color-slate-100: oklch(0.968 0.007 247.896);
  --color-slate-200: oklch(0.929 0.013 255.508);
  --color-slate-300: oklch(0.869 0.022 252.894);
  --color-slate-400: oklch(0.704 0.04 256.788);
  --color-slate-500: oklch(0.554 0.046 257.417);
  --color-slate-600: oklch(0.446 0.043 257.281);
  --color-slate-700: oklch(0.372 0.044 257.287);
  --color-slate-800: oklch(0.279 0.041 260.031);
  --color-slate-900: oklch(0.208 0.042 265.755);
  --color-slate-950: oklch(0.129 0.042 264.695);

  --color-neutral-50: oklch(0.985 0 0);
  --color-neutral-100: oklch(0.97 0 0);
  --color-neutral-200: oklch(0.922 0 0);
  --color-neutral-300: oklch(0.87 0 0);
  --color-neutral-400: oklch(0.708 0 0);
  --color-neutral-500: oklch(0.556 0 0);
  --color-neutral-600: oklch(0.439 0 0);
  --color-neutral-700: oklch(0.371 0 0);
  --color-neutral-800: oklch(0.269 0 0);
  --color-neutral-900: oklch(0.205 0 0);
  --color-neutral-950: oklch(0.145 0 0);

  --color-black: #000;
  --color-white: #fff;

  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  --container-3xs: 256px;
  --container-2xs: 288px;
  --container-xs: 320px;
  --container-sm: 384px;
  --container-md: 448px;
  --container-lg: 512px;
  --container-xl: 576px;
  --container-2xl: 672px;
  --container-3xl: 768px;
  --container-4xl: 896px;
  --container-5xl: 1024px;
  --container-6xl: 1152px;
  --container-7xl: 1280px;

  --text-xs: 12px;
  --text-xs--line-height: calc(1 / 0.75);
  --text-sm: 14px;
  --text-sm--line-height: calc(1.25 / 0.875);
  --text-base: 16px;
  --text-base--line-height: calc(1.5 / 1);
  --text-lg: 18px;
  --text-lg--line-height: calc(1.75 / 1.125);
  --text-xl: 20px;
  --text-xl--line-height: calc(1.75 / 1.25);
  --text-2xl: 24px;
  --text-2xl--line-height: calc(2 / 1.5);
  --text-3xl: 30px;
  --text-3xl--line-height: calc(2.25 / 1.875);
  --text-4xl: 36px;
  --text-4xl--line-height: calc(2.5 / 2.25);
  --text-5xl: 48px;
  --text-5xl--line-height: 1;
  --text-6xl: 60px;
  --text-6xl--line-height: 1;
  --text-7xl: 72px;
  --text-7xl--line-height: 1;
  --text-8xl: 96px;
  --text-8xl--line-height: 1;
  --text-9xl: 128px;
  --text-9xl--line-height: 1;

  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;

  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  --radius-3xl: 24px;
  --radius-4xl: 32px;

  --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1),
    0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);
  --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);
  --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);
  --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);
  --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);
  --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);
  --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);
  --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);
  --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);

  --shadow-uniform-2xs: 0 0 1px rgba(0, 0, 0, 0.1);
  --shadow-uniform-xs: 0 0 2px rgba(0, 0, 0, 0.2);
  --shadow-uniform-sm: 0 0 5px rgba(0, 0, 0, 0.3);
  --shadow-uniform-md: 0 0 10px rgba(0, 0, 0, 0.4);
  --shadow-uniform-lg: 0 0 15px rgba(0, 0, 0, 0.5);
  /* --shadow-uniform-2xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-uniform-xs: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-uniform-sm: 0 3px 6px rgba(0, 0, 0, 0.15);
  --shadow-uniform-md: 0 5px 10px rgba(0, 0, 0, 0.2);
  --shadow-uniform-lg: 0 8px 20px rgba(0, 0, 0, 0.25); */


  --blur-xs: 4px;
  --blur-sm: 8px;
  --blur-md: 12px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;

  --perspective-dramatic: 100px;
  --perspective-near: 300px;
  --perspective-normal: 500px;
  --perspective-midrange: 800px;
  --perspective-distant: 1200px;

  --aspect-video: 16 / 9;

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-duration-fast: 0.15s;
  --ease-duration: 0.3s;
  --ease-duration-slow: 0.75s;
  --animate-spin: spin 1s linear infinite;
  --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-bounce: bounce 1s infinite;
  --animate-shake: shake 0.5s ease-in-out;
  --animate-fade-in-up: fade-in-up 0.5s ease-out;
  --animate-fade-in-down: fade-in-up 0.5s ease-out reverse;
  --animate-fade-in-left: fade-in-up 0.5s ease-out reverse;
  --animate-fade-in-right: fade-in-up 0.5s ease-out;
  --animate-rotate-90: rotate-90 0.5s ease-out;
  --animate-rotate-180: rotate-180 0.5s ease-out;
  --animate-slide-in-up: slide-in-up 0.5s ease-out;
  --animate-slide-in-down: slide-in-down 0.5s ease-out;
  --animate-slide-in-left: slide-in-left 0.5s ease-out;
  --animate-slide-in-right: slide-in-right 0.5s ease-out;

  /* Popover placement*/
  --msg-popover-placement-top: 5px;
  --msg-popover-placement-bottom: 5px;
  --msg-popover-placement-left: 5px;
  --msg-popover-placement-right: 5px;

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse {

    0%,
    100% {
      opacity: 1;
    }

    50% {
      opacity: 0.5;
    }
  }

  @keyframes bounce {

    0%,
    100% {
      transform: translateY(-25%);
      animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }

    50% {
      transform: none;
      animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
  }

  @keyframes shake {

    0%,
    100% {
      transform: translateX(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
      transform: translateX(-5px);
    }

    20%,
    40%,
    60%,
    80% {
      transform: translateX(5px);
    }
  }

  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in-down {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in-left {
    from {
      opacity: 0;
      transform: translateX(20px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fade-in-right {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes rotate-90 {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(90deg);
    }
  }

  @keyframes rotate-180 {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(180deg);
    }
  }

  @keyframes slide-in-up {
    from {
      transform: translateY(40px);
      opacity: 1;
    }

    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-down {
    from {
      transform: translateY(-40px);
      opacity: 1;
    }

    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-left {
    from {
      transform: translateX(-40px);
      opacity: 1;
    }

    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slide-in-right {
    from {
      transform: translateX(40px);
      opacity: 1;
    }

    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
}

@theme {
  --font-sans: var(--font-sans);
  --font-serif: var(--font-serif);
  --font-mono: var(--font-mono);

  --color-red-50: var(--color-red-50);
  --color-red-100: var(--color-red-100);
  --color-red-200: var(--color-red-200);
  --color-red-300: var(--color-red-300);
  --color-red-400: var(--color-red-400);
  --color-red-500: var(--color-red-500);
  --color-red-600: var(--color-red-600);
  --color-red-700: var(--color-red-700);
  --color-red-800: var(--color-red-800);
  --color-red-900: var(--color-red-900);
  --color-red-950: var(--color-red-950);

  --color-orange-50: var(--color-orange-50);
  --color-orange-100: var(--color-orange-100);
  --color-orange-200: var(--color-orange-200);
  --color-orange-300: var(--color-orange-300);
  --color-orange-400: var(--color-orange-400);
  --color-orange-500: var(--color-orange-500);
  --color-orange-600: var(--color-orange-600);
  --color-orange-700: var(--color-orange-700);
  --color-orange-800: var(--color-orange-800);
  --color-orange-900: var(--color-orange-900);
  --color-orange-950: var(--color-orange-950);

  --color-lime-50: var(--color-lime-50);
  --color-lime-100: var(--color-lime-100);
  --color-lime-200: var(--color-lime-200);
  --color-lime-300: var(--color-lime-300);
  --color-lime-400: var(--color-lime-400);
  --color-lime-500: var(--color-lime-500);
  --color-lime-600: var(--color-lime-600);
  --color-lime-700: var(--color-lime-700);
  --color-lime-800: var(--color-lime-800);
  --color-lime-900: var(--color-lime-900);
  --color-lime-950: var(--color-lime-950);

  --color-green-50: var(--color-green-50);
  --color-green-100: var(--color-green-100);
  --color-green-200: var(--color-green-200);
  --color-green-300: var(--color-green-300);
  --color-green-400: var(--color-green-400);
  --color-green-500: var(--color-green-500);
  --color-green-600: var(--color-green-600);
  --color-green-700: var(--color-green-700);
  --color-green-800: var(--color-green-800);
  --color-green-900: var(--color-green-900);
  --color-green-950: var(--color-green-950);

  --color-sky-50: var(--color-sky-50);
  --color-sky-100: var(--color-sky-100);
  --color-sky-200: var(--color-sky-200);
  --color-sky-300: var(--color-sky-300);
  --color-sky-400: var(--color-sky-400);
  --color-sky-500: var(--color-sky-500);
  --color-sky-600: var(--color-sky-600);
  --color-sky-700: var(--color-sky-700);
  --color-sky-800: var(--color-sky-800);
  --color-sky-900: var(--color-sky-900);
  --color-sky-950: var(--color-sky-950);

  --color-blue-50: var(--color-blue-50);
  --color-blue-100: var(--color-blue-100);
  --color-blue-200: var(--color-blue-200);
  --color-blue-300: var(--color-blue-300);
  --color-blue-400: var(--color-blue-400);
  --color-blue-500: var(--color-blue-500);
  --color-blue-600: var(--color-blue-600);
  --color-blue-700: var(--color-blue-700);
  --color-blue-800: var(--color-blue-800);
  --color-blue-900: var(--color-blue-900);
  --color-blue-950: var(--color-blue-950);

  --color-gray-50: var(--color-gray-50);
  --color-gray-100: var(--color-gray-100);
  --color-gray-200: var(--color-gray-200);
  --color-gray-300: var(--color-gray-300);
  --color-gray-400: var(--color-gray-400);
  --color-gray-500: var(--color-gray-500);
  --color-gray-600: var(--color-gray-600);
  --color-gray-700: var(--color-gray-700);
  --color-gray-800: var(--color-gray-800);
  --color-gray-900: var(--color-gray-900);
  --color-gray-950: var(--color-gray-950);

  --color-slate-50: var(--color-slate-50);
  --color-slate-100: var(--color-slate-100);
  --color-slate-200: var(--color-slate-200);
  --color-slate-300: var(--color-slate-300);
  --color-slate-400: var(--color-slate-400);
  --color-slate-500: var(--color-slate-500);
  --color-slate-600: var(--color-slate-600);
  --color-slate-700: var(--color-slate-700);
  --color-slate-800: var(--color-slate-800);
  --color-slate-900: var(--color-slate-900);
  --color-slate-950: var(--color-slate-950);

  --color-neutral-50: var(--color-neutral-50);
  --color-neutral-100: var(--color-neutral-100);
  --color-neutral-200: var(--color-neutral-200);
  --color-neutral-300: var(--color-neutral-300);
  --color-neutral-400: var(--color-neutral-400);
  --color-neutral-500: var(--color-neutral-500);
  --color-neutral-600: var(--color-neutral-600);
  --color-neutral-700: var(--color-neutral-700);
  --color-neutral-800: var(--color-neutral-800);
  --color-neutral-900: var(--color-neutral-900);
  --color-neutral-950: var(--color-neutral-950);

  --color-black: var(--color-black);
  --color-white: var(--color-white);

  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  --container-3xs: var(--container-3xs);
  --container-2xs: var(--container-2xs);
  --container-xs: var(--container-xs);
  --container-sm: var(--container-sm);
  --container-md: var(--container-md);
  --container-lg: var(--container-lg);
  --container-xl: var(--container-xl);
  --container-2xl: var(--container-2xl);
  --container-3xl: var(--container-3xl);
  --container-4xl: var(--container-4xl);
  --container-5xl: var(--container-5xl);
  --container-6xl: var(--container-6xl);
  --container-7xl: var(--container-7xl);

  --text-xs: var(--text-xs);
  --text-xs--line-height: var(--text-xs--line-height);
  --text-sm: var(--text-sm);
  --text-sm--line-height: var(--text-sm--line-height);
  --text-base: var(--text-base);
  --text-base--line-height: var(--text-base--line-height);
  --text-lg: var(--text-lg);
  --text-lg--line-height: var(--text-lg--line-height);
  --text-xl: var(--text-xl);
  --text-xl--line-height: var(--text-xl--line-height);
  --text-2xl: var(--text-2xl);
  --text-2xl--line-height: var(--text-2xl--line-height);
  --text-3xl: var(--text-3xl);
  --text-3xl--line-height: var(--text-3xl--line-height);
  --text-4xl: var(--text-4xl);
  --text-4xl--line-height: var(--text-4xl--line-height);
  --text-5xl: var(--text-5xl);
  --text-5xl--line-height: var(--text-5xl--line-height);
  --text-6xl: var(--text-6xl);
  --text-6xl--line-height: var(--text-6xl--line-height);
  --text-7xl: var(--text-7xl);
  --text-7xl--line-height: var(--text-7xl--line-height);
  --text-8xl: var(--text-8xl);
  --text-8xl--line-height: var(--text-8xl--line-height);
  --text-9xl: var(--text-9xl);
  --text-9xl--line-height: var(--text-9xl--line-height);

  --font-weight-thin: var(--font-weight-thin);
  --font-weight-extralight: var(--font-weight-extralight);
  --font-weight-light: var(--font-weight-light);
  --font-weight-normal: var(--font-weight-normal);
  --font-weight-medium: var(--font-weight-medium);
  --font-weight-semibold: var(--font-weight-semibold);
  --font-weight-bold: var(--font-weight-bold);
  --font-weight-extrabold: var(--font-weight-extrabold);
  --font-weight-black: var(--font-weight-black);

  --tracking-tighter: var(--tracking-tighter);
  --tracking-tight: var(--tracking-tight);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: var(--tracking-wide);
  --tracking-wider: var(--tracking-wider);
  --tracking-widest: var(--tracking-widest);

  --leading-tight: var(--leading-tight);
  --leading-snug: var(--leading-snug);
  --leading-normal: var(--leading-normal);
  --leading-relaxed: var(--leading-relaxed);
  --leading-loose: var(--leading-loose);

  --radius-xs: var(--radius-xs);
  --radius-sm: var(--radius-sm);
  --radius-md: var(--radius-md);
  --radius-lg: var(--radius-lg);
  --radius-xl: var(--radius-xl);
  --radius-2xl: var(--radius-2xl);
  --radius-3xl: var(--radius-3xl);
  --radius-4xl: var(--radius-4xl);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
  --inset-shadow-2xs: var(--inset-shadow-2xs);
  --inset-shadow-xs: var(--inset-shadow-xs);
  --inset-shadow-sm: var(--inset-shadow-sm);
  --drop-shadow-xs: var(--drop-shadow-xs);
  --drop-shadow-sm: var(--drop-shadow-sm);
  --drop-shadow-md: var(--drop-shadow-md);
  --drop-shadow-lg: var(--drop-shadow-lg);
  --drop-shadow-xl: var(--drop-shadow-xl);
  --drop-shadow-2xl: var(--drop-shadow-2xl);

  --shadow-uniform-2xs: var(--shadow-uniform-2xs);
  --shadow-uniform-xs: var(--shadow-uniform-xs);
  --shadow-uniform-sm: var(--shadow-uniform-sm);
  --shadow-uniform-md: var(--shadow-uniform-md);
  --shadow-uniform-lg: var(--shadow-uniform-lg);

  --blur-xs: var(--blur-xs);
  --blur-sm: var(--blur-sm);
  --blur-md: var(--blur-md);
  --blur-lg: var(--blur-lg);
  --blur-xl: var(--blur-xl);
  --blur-2xl: var(--blur-2xl);
  --blur-3xl: var(--blur-3xl);

  --perspective-dramatic: var(--perspective-dramatic);
  --perspective-near: var(--perspective-near);
  --perspective-normal: var(--perspective-normal);
  --perspective-midrange: var(--perspective-midrange);
  --perspective-distant: var(--perspective-distant);

  --aspect-video: var(--aspect-video);

  --ease-in: var(--ease-in);
  --ease-out: var(--ease-out);
  --ease-in-out: var(--ease-in-out);
  --animate-spin: var(--animate-spin);
  --animate-ping: var(--animate-ping);
  --animate-pulse: var(--animate-pulse);
  --animate-bounce: var(--animate-bounce);
  --animate-shake: var(--animate-shake);
  --animate-fade-in-up: var(--animate-fade-in-up);
  --animate-fade-in-down: var(--animate-fade-in-down);
  --animate-fade-in-left: var(--animate-fade-in-left);
  --animate-fade-in-right: var(--animate-fade-in-right);
  --animate-rotate-90: var(--animate-rotate-90);
  --animate-rotate-180: var(--animate-rotate-180);
  --animate-slide-in-up: var(--animate-slide-in-up);
  --animate-slide-in-down: var(--animate-slide-in-down);
  --animate-slide-in-left: var(--animate-slide-in-left);
  --animate-slide-in-right: var(--animate-slide-in-right);
}

@layer base {

  *,
  *::before,
  *::after {
    font-family: var(--font-sans)
  }

  * {
    box-sizing: border-box;
  }

  html,
  body {
    font-family: var(--font-sans);
    font-weight: var(--font-weight-normal);
    font-size: var(--text-sm);
    color: var(--color-neutral-900);
    background-color: var(--color-neutral-50);
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;

    button {
      &:focus {
        outline: none !important;
      }
    }

    a {
      text-decoration: none !important;
    }

    /* textarea:-webkit-autofill,
    textarea:-webkit-autofill:hover,
    textarea:-webkit-autofill:focus,
    select:-webkit-autofill,
    select:-webkit-autofill:hover,
    select:-webkit-autofill:focus */
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active {
      -webkit-background-clip: text;
      background-color: #fff;
      transition: background-color 5000s ease-in-out 0s;

      /* -webkit-box-shadow: 0 0 0 1000px #fff inset !important;
      box-shadow: 0 0 0 1000px #fff inset !important;
      background-color: #fff !important;
      -webkit-text-fill-color: #222 !important;
      color: #222 !important;
      transition: background-color 5000s ease-in-out 0s; */
    }

    input::placeholder,
    textarea::placeholder {
      color: var(--color-gray-400);
      opacity: 1;
      font-size: 13px;
    }

    /* Css Input readonly */
    input[readonly] {
      cursor: not-allowed !important;
      opacity: 0.7;
      color: var(--color-gray-400);
    }

    input:disabled {
      cursor: not-allowed !important;
      opacity: 0.7;
      color: var(--color-gray-400);
    }

    textarea[readonly] {
      cursor: not-allowed !important;
      opacity: 0.7;
      color: var(--color-gray-400);
    }
  }
}

@layer utilities {
  .bg-gradient {
    background: radial-gradient(103.89% 81.75% at 95.41% 106.34%, #eaf8ef 6%, #eaf8ef00 79.68%), radial-gradient(297.85% 151.83% at -21.39% 8.81%, #faf1f1 0% 15.29%, #f3edf5 21.39%, #e5f0fa 40.79%);
    background-image: radial-gradient(103.89% 81.75% at 95.41% 106.34%, rgb(234, 248, 239) 6%, rgba(234, 248, 239, 0) 79.68%), radial-gradient(297.85% 151.83% at -21.39% 8.81%, rgb(250, 241, 241) 0%, rgb(250, 241, 241) 15.29%, rgb(243, 237, 245) 21.39%, rgb(229, 240, 250) 40.79%);
    background-position-x: initial, initial;
    background-position-y: initial, initial;
    background-size: initial, initial;
    background-repeat: initial, initial;
    background-attachment: initial, initial;
    background-origin: initial, initial;
    background-clip: initial, initial;
    background-color: initial;
  }
}
