<msg-popover-shared [msgPlacement]="'bottomRight'"
  [msgTrigger]="'click'"
  [msgContent]="contentTemplate"
  (msgFunctionControl)="onFunctionControl($event)">
  <ng-content></ng-content>
</msg-popover-shared>

<ng-template #contentTemplate>
  <div class="msg-menu-action-popover-content">
    @for (item of msgActions(); track item.type) {
      <div class="msg-menu-action-popover-content-item"
        [class]="item.hoverClass || 'hover:bg-info-100'"
        (click)="onActionClick($event, item.type)">
        <msg-icon-shared [msgName]="item.icon" />
        <span class="msg-menu-action-popover-content-item-label">
          {{ item.label | translate }}
        </span>
      </div>
    }
  </div>
</ng-template>
