import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ComponentRef, computed, effect, ElementRef, inject, input, OnDestroy, OnInit, output, Renderer2, signal, viewChild } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { take, tap, timer } from 'rxjs';
import { MsgTruncatePipe } from '../../pipes/msg-truncate.pipe';
import { MsgDynamicComponentService } from '../../services';
import { MsgButtonSharedComponent } from '../msg-button/msg-button.component';
import { TYPE_MSG_ICON_NAME } from '../msg-icon/interfaces/msg-icon.interface';
import { MsgIconSharedComponent } from '../msg-icon/msg-icon.component';
import { IMsgModalFunctionControl, TYPE_MODAL } from './interfaces/msg-modal.interface';

@Component({
  selector: 'msg-modal-shared',
  templateUrl: './msg-modal.component.html',
  styleUrls: ['./msg-modal.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    MsgIconSharedComponent,
    MsgButtonSharedComponent,
    TranslatePipe,
    NzToolTipModule,
    MsgTruncatePipe
  ],
})

export class MsgModalSharedComponent implements OnInit, OnDestroy {
  public msgType = input<TYPE_MODAL>();
  public msgTitle = input<string>('i18n_msg_modal_title');
  public msgContent = input<string>('i18n_msg_modal_content');
  public msgShowOk = input<boolean>(true);
  public msgShowCancel = input<boolean>(true);
  public msgTextOk = input<string>('i18n_msg_modal_apply');
  public msgTextCancel = input<string>('i18n_msg_modal_cancel');
  public msgClassContainer = input<string>('msg-modal');
  public msgCloseOnOverlayClick = input<boolean>(true);

  public msgAfterOpen = output<void>();
  public msgAfterClose = output<void>();
  public msgOk = output<void>();
  public msgCancel = output<void>();
  public msgFunctionControl = output<IMsgModalFunctionControl>();

  protected modalIcon = computed<TYPE_MSG_ICON_NAME | null>(() => {
    const type = this.msgType();
    if (!type) { return null; }
    switch (type) {
      case 'info':
        return 'BadgeInfo';
      case 'success':
        return 'CheckCheck';
      case 'warn':
        return 'TriangleAlert';
      case 'danger':
        return 'X';
      default:
        return 'BadgeInfo';
    }
  });

  protected modalIconClass = computed(() => {
    const type = this.msgType();
    if (!type) { return ''; }
    return `msg-modal-icon-${type}`;
  });

  protected modalClasses = computed(() => {
    const baseClass = this.msgClassContainer();
    return baseClass;
  });
  protected msgTitleWidth = signal<number>(0);
  protected msgServiceMode = computed(() => !!this.msgType());
  protected msgLoading = signal<boolean>(false);

  private _isClosing = signal<boolean>(false);
  private _isVisible = signal<boolean>(false);
  private _isAnimating = signal<boolean>(false);
  private _modalElement = viewChild.required<ElementRef<HTMLElement>>('modalElement');
  private _transitionEndListener?: (event: Event) => void;

  private _renderer2 = inject(Renderer2);
  private _elementRef = inject(ElementRef);
  private _dynamicService = inject(MsgDynamicComponentService);

  constructor() {
    effect(() => {
      const isVisible = this._isVisible();
      if (isVisible) {
        this._renderer2.setStyle(document.body, 'overflow', 'hidden');
        this._renderer2.setAttribute(this._elementRef.nativeElement, 'data-visible', 'true');
        return;
      }

      this._renderer2.removeStyle(document.body, 'overflow');
      this._renderer2.removeAttribute(this._elementRef.nativeElement, 'data-visible');
    });

    effect(() => {
      if (this._isAnimating()) {
        this._renderer2.addClass(this._elementRef.nativeElement, 'msg-modal-animating');
        return;
      }

      this._renderer2.removeClass(this._elementRef.nativeElement, 'msg-modal-animating');
    });
  }

  ngOnInit(): void {
    if (!this.msgServiceMode()) {
      this._moveToBody();
    }

    this._setupTransitionListeners();
    this.msgFunctionControl.emit({
      show: this.show.bind(this),
      remove: this.remove.bind(this),
      setLoading: (loading: boolean) => this.msgLoading.set(loading),
    });

    if (this.msgServiceMode()) {
      requestAnimationFrame(() => {
        this._isAnimating.set(true);
        this._isVisible.set(true);
      });
    }
  }

  ngOnDestroy(): void {
    if (this._transitionEndListener && this._modalElement().nativeElement) {
      this._modalElement().nativeElement.removeEventListener('transitionend', this._transitionEndListener);
    }

    this._renderer2.removeStyle(document.body, 'overflow');
  }

  protected handlerCloseModal(event: MouseEvent): void {
    event.stopPropagation();
    this.msgCancel.emit();
    this._closeModal();
  }

  protected handlerSaveChanges(event: MouseEvent): void {
    event.stopPropagation();
    this.msgOk.emit();
  }

  protected handleOverlayClick(event: MouseEvent): void {
    if (this.msgLoading()) {
      event.stopPropagation();
      return;
    }

    if (this.msgCloseOnOverlayClick()) {
      event.stopPropagation();
      this._closeModal();
    }
  }

  private _setupTransitionListeners(): void {
    this._transitionEndListener = (event: Event) => {
      const transitionEvent = event as TransitionEvent;
      if (transitionEvent.target === transitionEvent.currentTarget ||
        transitionEvent.target === this._modalElement().nativeElement) {

        if (this._isVisible() && this._isAnimating() && !this._isClosing()) {
          this._isAnimating.set(false);
          this.msgAfterOpen.emit();
          return;
        }

        if (this._isClosing() && this._isAnimating()) {
          this._isClosing.set(false);
          this._isAnimating.set(false);
          this.msgAfterClose.emit();
          this._renderer2.removeStyle(document.body, 'overflow');
          this._destroyComponent();
        }
      }
    };

    if (this._modalElement().nativeElement && this._transitionEndListener) {
      this._modalElement().nativeElement.addEventListener('transitionend', this._transitionEndListener);
    }
  }

  private _destroyComponent(): void {
    const msgId = this._elementRef.nativeElement.getAttribute('msgId');
    if (!msgId) {
      return;
    }

    this._dynamicService.removeComponentById(msgId);
  }

  private _closeModal(): void {
    if (this._isAnimating() || this._isClosing()) {
      return;
    }

    this._isClosing.set(true);
    this._isAnimating.set(true);
    this._isVisible.set(false);
  }

  private _calculateTitleWidth(): void {
    if (this._modalElement().nativeElement) {
      const modalRect = this._modalElement().nativeElement.getBoundingClientRect();
      const titleWidth = modalRect.width - 150;
      this.msgTitleWidth.set(titleWidth);
    }
  }

  private _moveToBody(): void {
    const element = this._elementRef.nativeElement;
    this._renderer2.appendChild(document.body, element);
  }

  public show(): void {
    requestAnimationFrame(() => {
      this._calculateTitleWidth();
      this._isAnimating.set(true);
      this._isVisible.set(true);
    });
  }

  public hide(): void {
    this._closeModal();
  }

  public remove(compRef?: ComponentRef<MsgModalSharedComponent>): void {
    this._closeModal();
    if (compRef) {
      timer(200).pipe(
        take(1),
        tap(() => {
          this._dynamicService.removeComponent(compRef);
          compRef.destroy();
        })
      ).subscribe();
    }
  }

  public setLoading(loading: boolean): void {
    this.msgLoading.set(loading);
  }
}
