<div class="flex h-[100dvh]">
  <div class="msg-left-container border-r border-gray-50 w-full absolute top-0 right-0"></div>
  <div class="msg-right-container w-full flex items-center justify-center px-8 z-[10] bg-gradient">
    <div class="w-full max-w-md shadow-xl p-6 bg-white rounded-[10px]">
      <div class="text-center mb-4">
        <h1 class="text-xl font-[400] !text-green-600 !text-shadow-sm mb-0! p-0!">{{ msgTitle() }}</h1>
        <p class="font-[400] text-gray-500 p-0! m-0! !pt-[5px] !text-[13px]">{{ msgDescription() }}</p>
      </div>
      <form class="min-w-[300px]">
        <div class="space-y-4">
          <div>
            <label for="username"
              class="text-slate-600 text-[13px] font-medium mb-[5px] block">Email</label>
            <div class="relative flex items-center">
              <input name="username"
                id="username"
                type="text"
                required
                autofocus
                class="!text-[13px] w-full text-slate-900 text-sm border border-slate-300 px-4 py-[10px] pr-8 rounded-[3px] outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 !shadow-sm focus:!shadow-md !bg-white transition-all duration-200"
                placeholder="Enter user name" />
              <msg-icon-shared [msgName]="'Mail'"
                [msgClass]="'text-slate-400'"
                [class]="'absolute right-4'"
                [msgSize]="20" />
            </div>
          </div>
          <div>
            <label for="password"
              class="text-slate-600 text-[13px] font-medium mb-[5px] block">Mật khẩu</label>
            <div class="relative flex items-center">
              <input required
                autocomplete
                name="password"
                id="password"
                [type]="msgShowPassword() ? 'text' : 'password'"
                class="!text-[13px] w-full text-slate-900 text-sm border border-slate-300 px-4 py-[10px] pr-8 rounded-[3px] outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 !shadow-sm focus:!shadow-md !bg-white transition-all duration-200"
                placeholder="Enter password" />
              <msg-icon-shared [msgName]="msgShowPassword() ? 'EyeOff': 'Eye'"
                (click)="toggleShowPassword($event)"
                [msgClass]="'text-slate-400 cursor-pointer'"
                [class]="'absolute right-4'"
                [msgSize]="20" />
            </div>
          </div>

          <msg-button-shared [msgType]="'primary'"
            [msgText]="'Đăng nhập'"
            [msgLoading]="msgLoading()"
            [msgClass]="'w-full text-center !font-[400] !text-[14px]'"
            [msgSize]="'large'"
            (click)="handlerLogin()" />
        </div>
        <div class="msg-login-with-socials flex flex-col items-center justify-center">
          <div class="flex items-center w-full my-[20px]">
            <div class="flex-grow border-t border-slate-300"></div>
            <span class="px-4 text-slate-500 text-[12px]">Hoặc đăng nhập với</span>
            <div class="flex-grow border-t border-slate-300"></div>
          </div>
          <div class="msg-flex-col-center items-center gap-3 w-full">
            <msg-button-shared [msgSize]="'large'"
              [msgType]="'outline'"
              [class]="'w-full'"
              [msgClass]="'!border-slate-300 !bg-white !shadow-sm w-full'">
              <div class="flex items-center gap-2">
                <svg class="w-5 h-5"
                  viewBox="0 0 24 24"
                  fill="none">
                  <path
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    fill="#4285F4" />
                  <path
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    fill="#34A853" />
                  <path
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    fill="#FBBC05" />
                  <path
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    fill="#EA4335" />
                </svg>
                <span class="!text-[14px] font-[400] text-slate-700">Đăng nhập với Google</span>
              </div>
            </msg-button-shared>
            <msg-button-shared [msgSize]="'large'"
              [msgType]="'outline'"
              [class]="'w-full'"
              [msgClass]="'!border-slate-300 !bg-white !shadow-sm w-full'">
              <div class="flex items-center gap-2">
                <svg class="w-5 h-5"
                  viewBox="0 0 24 24"
                  fill="none">
                  <path d="M11.4 24H12.6V12.6H24V11.4H12.6V0H11.4V11.4H0V12.6H11.4V24Z"
                    fill="#F25022" />
                  <path d="M11.4 11.4H0V0H11.4V11.4Z"
                    fill="#F25022" />
                  <path d="M24 11.4H12.6V0H24V11.4Z"
                    fill="#7FBA00" />
                  <path d="M11.4 24H0V12.6H11.4V24Z"
                    fill="#00A4EF" />
                  <path d="M24 24H12.6V12.6H24V24Z"
                    fill="#FFB900" />
                </svg>
              </div>
              <span class="!text-[14px] font-[400] text-slate-700">Đăng nhập với Microsoft</span>
            </msg-button-shared>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
