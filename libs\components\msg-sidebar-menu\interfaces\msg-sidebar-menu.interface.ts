import { TYPE_MSG_ICON_NAME } from "../../msg-icon/interfaces/msg-icon.interface";

export interface IMsgMenuDto {
  id: string;
  name?: string;
  isCategory?: boolean;
  icon?: TYPE_MSG_ICON_NAME;
  iconCustom?: string;
  path?: string;
  subPaths?: string[];
  active?: boolean;
  expanded?: boolean;
  children?: IMsgMenuDto[];
  level?: number;
  parents?: string[]
}

export interface IMsgControlSidebarMenuDto {
  toggleCollapsed: () => void;
}
