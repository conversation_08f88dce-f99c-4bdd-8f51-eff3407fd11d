/* eslint-disable @typescript-eslint/no-explicit-any */
import { MsgCrypto } from "../utils";
import { MsgIndexDbDefine } from "./defines/msg-indexdb.define";
import { TYPE_DB_KEY } from "./interfaces/msg-indexdb.interface";

export class MsgBrowserIndexDbService {
  private static readonly _CACHE_TTL = 5000;
  private static readonly _BATCH_SIZE = 100;
  private _db: IDBDatabase | null = null;
  private _dbName: string;
  private _storeName: string;
  private _version: number;
  private _mode: IDBTransactionMode;
  private _dbReady: Promise<void>;
  private _cache = new Map<string, {
    value: any;
    timestamp: number;
  }>();

  public constructor(
    dbName = 'Msg',
    storeName = 'MsgStore',
    version = 1,
    mode: IDBTransactionMode = 'readwrite'
  ) {
    this._dbName = dbName;
    this._storeName = storeName;
    this._version = version;
    this._mode = mode;
    this._dbReady = this._initDb();
  }

  private _encrypt(data: any): string {
    const payload = {
      __prefix: '##<<myPrefix>>',
      __type: typeof data,
      __length: JSON.stringify(data).length,
      data
    };

    return MsgCrypto.encrypt(payload);
  }

  private _decrypt(data: any): any {
    const decrypted = MsgCrypto.decrypt(data);
    const parsed = typeof decrypted === 'string' ? JSON.parse(decrypted) : decrypted;
    if (parsed.__prefix !== '##<<myPrefix>>') {
      throw new Error('Invalid data prefix');
    }

    return parsed.data;
  }

  private _getKey(key: string): TYPE_DB_KEY {
    return MsgIndexDbDefine[key as keyof typeof MsgIndexDbDefine] as TYPE_DB_KEY;
  }

  private _closeConnection(): void {
    if (this._db) {
      this._db.close();
      this._db = null;
    }
  }

  private async _reconnectDb(): Promise<void> {
    try {
      console.warn('Attempting to reconnect to database...');
      this._closeConnection();
      this._dbReady = this._initDb();
      await this._dbReady;
      console.info('Reconnected to database successfully');
    } catch (error) {
      console.error('Error reconnecting to database:', error);
      await new Promise(resolve => setTimeout(resolve, 1000));
      throw error;
    }
  }

  private _getCached(key: string): any | undefined {
    const cached = this._cache.get(key);
    if (!cached) { return undefined; }

    if (Date.now() - cached.timestamp > MsgBrowserIndexDbService._CACHE_TTL) {
      this._cache.delete(key);
      return undefined;
    }

    return cached.value;
  }

  private _setCache(key: string, value: any): void {
    this._cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  private _clearCache(): void {
    this._cache.clear();
  }

  private async _withRetry<T>(
    operation: () => Promise<T>,
    retries = 3
  ): Promise<T> {
    let lastError;

    for (let i = 0; i < retries; i++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        if (error instanceof Error && error.name === 'InvalidStateError') {
          await this._reconnectDb();
        }
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 100));
      }
    }

    throw lastError;
  }

  private async _batchOperation<T>(
    operation: (store: IDBObjectStore) => IDBRequest,
    items: T[]
  ): Promise<void> {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += MsgBrowserIndexDbService._BATCH_SIZE) {
      const batch = items.slice(i, i + MsgBrowserIndexDbService._BATCH_SIZE);
      batches.push(batch);
    }

    for (const batch of batches) {
      await new Promise<void>((resolve, reject) => {
        if (!this._db) {
          reject(new Error('Database not initialized'));
          return;
        }

        const transaction = this._db.transaction([this._storeName], 'readwrite');
        const store = transaction.objectStore(this._storeName);

        let completed = 0;

        batch.forEach(() => {
          const request = operation(store);
          request.onsuccess = () => {
            completed++;
            if (completed === batch.length) {
              resolve();
            }
          };
          request.onerror = () => reject(request.error);
        });

        transaction.onerror = () => reject(transaction.error);
      });
    }
  }

  private async _initDb(): Promise<void> {
    if (this._db) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this._dbName, this._version);
        request.onupgradeneeded = (event) => {
          const db = (event.target as IDBOpenDBRequest).result;
          if (!db.objectStoreNames.contains(this._storeName)) {
            db.createObjectStore(this._storeName);
          }
        };

        request.onsuccess = (event) => {
          this._db = (event.target as IDBOpenDBRequest).result;
          this._db.onerror = (event) => {
            console.error('Database error:', (event.target as IDBRequest).error);
          };

          this._db.onclose = () => {
            console.warn('Database was unexpectedly closed');
            this._db = null;
          };

          resolve();
        };

        request.onerror = (event) => {
          console.error('IndexedDB error:', (event.target as IDBOpenDBRequest).error);
          reject((event.target as IDBOpenDBRequest).error);
        };
      } catch (error) {
        console.error('Error opening database:', error);
        reject(error);
      }
    });
  }

  public async get<T>(key: TYPE_DB_KEY, defaultValue?: T, isEncode = true): Promise<T | undefined> {
    const actualKey = this._getKey(key);
    const cached = this._getCached(actualKey);
    if (cached !== undefined) { return cached; }

    return this._withRetry(async () => {
      await this._dbReady;

      if (!this._db) {
        console.warn('Database not initialized in get(), attempting to reconnect...');
        try {
          await this._reconnectDb();
          if (!this._db) {
            console.error('Failed to initialize database after reconnect in get()');
            return defaultValue;
          }
        } catch (error) {
          console.error('Error reconnecting in get():', error);
          return defaultValue;
        }
      }

      const _key = isEncode ? this._encrypt(actualKey) : actualKey;
      const value = await new Promise<T | undefined>((resolve) => {
        try {
          if (!this._db) {
            console.warn('Database connection lost during get() operation');
            resolve(defaultValue);
            return;
          }

          const transaction = this._db.transaction([this._storeName], this._mode);
          const store = transaction.objectStore(this._storeName);
          const request = store.get(_key);

          request.onsuccess = async () => {
            try {
              const result = request.result !== undefined ? request.result : defaultValue;
              const decryptedResult = isEncode ? (result ? this._decrypt(result) as T : defaultValue) : result;
              this._setCache(actualKey, decryptedResult);
              resolve(decryptedResult);
            } catch (error) {
              console.error(`Error decrypting result for ${actualKey}:`, error);
              resolve(defaultValue);
            }
          };

          request.onerror = () => {
            console.error(`Error getting item ${actualKey}:`, request.error);
            resolve(defaultValue);
          };

          transaction.onerror = () => {
            console.error(`Transaction error in get() for ${actualKey}:`, transaction.error);
            resolve(defaultValue);
          };
        } catch (error) {
          console.error(`Unexpected error in get() for ${actualKey}:`, error);
          resolve(defaultValue);
        }
      });

      return value;
    });
  }

  public async getAll(isEncode = true): Promise<Record<TYPE_DB_KEY, any>> {
    return this._withRetry(async () => {
      await this._dbReady;
      if (!this._db) {
        console.warn('Database not initialized in getAll(), attempting to reconnect...');
        try {
          await this._reconnectDb();

          if (!this._db) {
            console.error('Failed to initialize database after reconnect in getAll()');
            return {} as Record<TYPE_DB_KEY, any>;
          }
        } catch (error) {
          console.error('Error reconnecting in getAll():', error);
          return {} as Record<TYPE_DB_KEY, any>;
        }
      }

      const result = await new Promise<Record<TYPE_DB_KEY, any>>((resolve) => {
        try {
          if (!this._db) {
            console.warn('Database connection lost during getAll() operation');
            resolve({} as Record<TYPE_DB_KEY, any>);
            return;
          }

          const transaction = this._db.transaction([this._storeName], this._mode);
          const store = transaction.objectStore(this._storeName);
          const request = store.getAll();
          const keyRequest = store.getAllKeys();
          const results: Record<string, any> = {};

          keyRequest.onsuccess = () => {
            try {
              const keys = keyRequest.result;
              const values = request.result;
              keys.forEach(async (key, index) => {
                if (typeof key === 'string') {
                  try {
                    const _key: string = isEncode ? this._decrypt(key) : key;
                    const _value: string = isEncode ? (values[index] ? this._decrypt(values[index]) : '') : values[index];
                    results[_key] = _value;
                    this._setCache(_key, _value);
                  } catch (decryptError) {
                    console.error(`Error decrypting key or value at index ${index}:`, decryptError);
                  }
                }
              });

              resolve(results);
            } catch (error) {
              console.error('Error processing results in getAll():', error);
              resolve({} as Record<TYPE_DB_KEY, any>);
            }
          };

          keyRequest.onerror = () => {
            console.error('Error getting all items:', keyRequest.error);
            resolve({} as Record<TYPE_DB_KEY, any>);
          };

          transaction.onerror = () => {
            console.error('Transaction error in getAll():', transaction.error);
            resolve({} as Record<TYPE_DB_KEY, any>);
          };
        } catch (error) {
          console.error('Unexpected error in getAll():', error);
          resolve({} as Record<TYPE_DB_KEY, any>);
        }
      });

      return result;
    });
  }

  public async set<T>(key: TYPE_DB_KEY, value: T, isEncode = true): Promise<void> {
    const actualKey = this._getKey(key);
    return this._withRetry(async () => {
      const cached = this._getCached(actualKey);
      if (cached === value) { return; }
      await this._dbReady;
      if (!this._db) {
        console.warn('Database not initialized in set(), attempting to reconnect...');
        await this._reconnectDb();

        if (!this._db) {
          console.error('Failed to initialize database after reconnect');
          throw new Error('Database not initialized and reconnect failed');
        }
      }

      const _key = isEncode ? this._encrypt(actualKey) : actualKey;
      const _value = isEncode ? this._encrypt(value) : value;
      await new Promise<void>((resolve, reject) => {
        try {
          if (!this._db) {
            reject(new Error('Database connection lost during operation'));
            return;
          }

          const transaction = this._db.transaction([this._storeName], 'readwrite');
          const store = transaction.objectStore(this._storeName);
          const putRequest = store.put(_value, _key);

          putRequest.onerror = () => {
            console.error(`Error in put request for ${actualKey}:`, putRequest.error);
            reject(putRequest.error);
          };

          transaction.oncomplete = () => {
            this._setCache(actualKey, value);
            resolve();
          };

          transaction.onerror = () => {
            console.error(`Error setting item ${actualKey}:`, transaction.error);
            reject(transaction.error);
          };
        } catch (error) {
          console.error(`Unexpected error in set() for ${actualKey}:`, error);
          reject(error);
        }
      });
    });
  }

  public async setMultiple(items: Partial<Record<TYPE_DB_KEY, any>>, isEncode = true): Promise<void> {
    return this._withRetry(async () => {
      await this._dbReady;
      if (!this._db) {
        console.warn('Database not initialized in setMultiple(), attempting to reconnect...');
        await this._reconnectDb();

        if (!this._db) {
          console.error('Failed to initialize database after reconnect');
          throw new Error('Database not initialized and reconnect failed');
        }
      }

      const entries = await Promise.all(Object.entries(items).map(async ([key, value]) => ({
        key: isEncode ? this._encrypt(this._getKey(key as TYPE_DB_KEY)) : this._getKey(key as TYPE_DB_KEY),
        value: isEncode ? this._encrypt(value) : value,
      })));

      try {
        await this._batchOperation(
          (store) => {
            const entry = entries.shift();
            return store.put(entry?.value, entry?.key);
          },
          entries
        );

        Object.entries(items).forEach(([key, value]) => {
          this._setCache(this._getKey(key as TYPE_DB_KEY), value);
        });


      } catch (error) {
        console.error('Error in setMultiple():', error);
        throw error;
      }
    });
  }

  public async delete(key: TYPE_DB_KEY | TYPE_DB_KEY[], isEncode = true): Promise<void> {
    return this._withRetry(async () => {
      await this._dbReady;
      if (!this._db) {
        console.warn('Database not initialized in delete(), attempting to reconnect...');
        await this._reconnectDb();

        if (!this._db) {
          console.error('Failed to initialize database after reconnect');
          throw new Error('Database not initialized and reconnect failed');
        }
      }

      const keys = Array.isArray(key) ? key : [key];
      await new Promise<void>((resolve, reject) => {
        try {
          if (!this._db) {
            reject(new Error('Database connection lost during operation'));
            return;
          }

          const transaction = this._db.transaction([this._storeName], 'readwrite');
          const store = transaction.objectStore(this._storeName);

          keys.forEach(async k => {
            const actualKey = this._getKey(k);
            const _key = isEncode ? this._encrypt(actualKey) : actualKey;
            store.delete(_key);
            this._cache.delete(actualKey);
          });

          transaction.oncomplete = () => {
            resolve();
          };

          transaction.onerror = () => {
            console.error(`Error deleting items:`, transaction.error);
            reject(transaction.error);
          };
        } catch (error) {
          console.error(`Unexpected error in delete():`, error);
          reject(error);
        }
      });
    });
  }

  public async clear(): Promise<void> {
    return this._withRetry(async () => {
      await this._dbReady;

      if (!this._db) {
        console.warn('Database not initialized in clear(), attempting to reconnect...');
        await this._reconnectDb();

        if (!this._db) {
          console.error('Failed to initialize database after reconnect');
          throw new Error('Database not initialized and reconnect failed');
        }
      }

      await new Promise<void>((resolve, reject) => {
        try {
          if (!this._db) {
            reject(new Error('Database connection lost during operation'));
            return;
          }

          const transaction = this._db.transaction([this._storeName], 'readwrite');
          const store = transaction.objectStore(this._storeName);
          const request = store.clear();

          request.onsuccess = () => {
            this._clearCache();
            resolve();
          };

          request.onerror = () => {
            console.error('Error clearing database:', request.error);
            reject(request.error);
          };
        } catch (error) {
          console.error('Unexpected error in clear():', error);
          reject(error);
        }
      });
    });
  }
}
