<div class="msg-skeleton-container"
  [class]="msgClass()">
  @if (msgType() === 'circle') {
    <div class="msg-skeleton-circle"
      [class]="skeletonClasses()"></div>
  }
  @else if (msgType() === 'bar') {
    @if (shouldShowMultipleRows()) {
      @for (row of autoRowsArray(); track row) {
        <div class="msg-skeleton-bar-row"
          [class.mb-px]="$index === autoRowsArray().length - 1 ? 0 : msgGap()"
          [class]="skeletonClasses()"
          [style.height]="row.height"></div>
      }
    } @else {
      <div class="msg-skeleton-bar"
        [class]="skeletonClasses()"></div>
    }
  }
</div>
