/* 1. Flexbox Centering */
.msg-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.msg-flex-col-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.msg-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.msg-flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.msg-flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 2. Position Centering */
.msg-absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.msg-fixed-center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 3. Margin Centering */
.msg-margin-center {
  margin: 0 auto;
}

/* 4. Flex Overflow Fixes  */
.msg-flex-overflow-hidden {
  min-width: 0;
  overflow: hidden;
}

.msg-flex-ellipsis {
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 5. Basic Grid Layouts */
.msg-grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.msg-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

/* 6. Responsive Grid  */
.msg-grid-responsive {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

/* 7. Sticky Elements */
.msg-sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.msg-sticky-footer {
  position: sticky;
  bottom: 0;
  z-index: 100;
}

/* 8. Common Layout Patterns */
.msg-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.msg-full-height {
  height: 100vh;
  min-height: 100vh;
}

.msg-full-width {
  width: 100%;
}

/* 9. Gap Utilities */
.msg-gap-sm {
  gap: 0.5rem;
}

.msg-gap-md {
  gap: 1rem;
}

.msg-gap-lg {
  gap: 1.5rem;
}
