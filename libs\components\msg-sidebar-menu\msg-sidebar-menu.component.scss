@use "responsive" as *;
@use "animations" as *;

$msg-sidebar-width: 230px;
$msg-sidebar-collapsed-width: 50px;


$color-white: #ffffff;
$color-gray-200: #e5e7eb;
$color-black-opacity-50: rgba(0, 0, 0, 0.5);
$shadow-uniform-sm: 0 1px 3px rgba(0, 0, 0, 0.1);


@mixin flex($direction: row, $justify: flex-start, $align: stretch, $gap: 0) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;

  @if $gap >0 {
    gap: #{$gap}px;
  }
}

@mixin position-absolute($top: auto, $right: auto, $bottom: auto, $left: auto) {
  position: absolute;
  top: $top;
  right: $right;
  bottom: $bottom;
  left: $left;
}

@mixin position-fixed($top: auto, $right: auto, $bottom: auto, $left: auto) {
  position: fixed;
  top: $top;
  right: $right;
  bottom: $bottom;
  left: $left;
}

@mixin position-sticky($top: auto) {
  position: sticky;
  top: $top;
}

@mixin size($width: auto, $height: auto) {
  width: $width;
  height: $height;
}

@mixin hardware-accelerate {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

@mixin smooth-transition($properties: transform, $duration: 0.3s) {
  transition: #{$properties} #{$duration} cubic-bezier(0.25, 0.8, 0.25, 1);
  will-change: $properties;
}

.msg-sidebar-container {
  position: relative;
  z-index: 100;
  box-shadow: var(--shadow-uniform-sm);
  background-color: var(--color-white);
  width: $msg-sidebar-width;
  cursor: default;
  @include flex(column);
  height: 100%;

  transform: translateZ(0);
  backface-visibility: hidden;

  @include respond-to('tablet') {
    will-change: width;
    contain: layout style;
    @include transition(width, 0.25s, cubic-bezier(0.4, 0, 0.2, 1));

    &:hover {
      cursor: pointer;
    }

    &.collapsed {
      width: 50px;
    }

    &:not(.animating) {
      will-change: auto;
    }

    .msg-sidebar-toggle-desktop {
      display: block;
    }

    .msg-sidebar-header {
      justify-content: space-between;

      &.collapsed {
        flex-direction: column;
        align-items: center;
      }
    }

    .msg-menu-item[isCategory="true"] {
      &.collapsed {
        display: none;
      }
    }
  }

  @include respond-to-max('tablet') {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    transition: transform .3s cubic-bezier(.78, .14, .15, .86);
    contain: layout style;

    &.collapsed {
      transform: translateX(-$msg-sidebar-width);
      margin-left: 0;
      border-right: none;
    }

    .msg-sidebar-content {
      z-index: 120;
      background-color: white;
      transform: translateZ(0);
    }

    .msg-sidebar-toggle-tablet {
      display: block;
      position: absolute;
      z-index: 110;
      right: -30px;
      top: 6px;

      .msg-toggle-btn {
        padding-left: 50px;
      }
    }
  }
}

.msg-sidebar-content {
  height: 100%;
  @include flex(column);
  transform: translateZ(0);

  &.collapsed {}
}

.msg-sidebar-header {
  min-height: 50px;
  @include position-sticky(0);
  width: 100%;
  z-index: 10;
  padding: 12px;
  @include flex(row, center, center, 12);
  border-bottom: 1px solid $color-gray-200;

  &.collapsed {}
}

.msg-logo {
  height: 32px;
  width: auto;
  object-fit: contain;
}

.msg-sidebar-toggle-desktop {
  display: none;
}

.msg-sidebar-toggle-tablet {
  display: none;
}

.msg-menu-list-container {
  overflow: auto;
  flex: 1;
  overflow-x: hidden;

  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;


  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }
}

.msg-menu-list {
  padding: 8px;
  @include flex(column);

  &.justify-center {
    justify-content: center;
  }
}

.msg-menu-item {
  &.collapsed {}

  &[isCategoryPadding="true"] {}
}

.msg-toggle-btn {
  transform: translateZ(0);
}

.msg-sidebar-overlay {
  @include position-fixed(0, auto, auto, 0);
  width: 100vw;
  height: 100vh;
  background-color: $color-black-opacity-50;
  z-index: 90;
  opacity: 0;
  visibility: hidden;
  transition: opacity .25s ease-out, visibility .25s ease-out;

  transform: translateZ(0);

  @include respond-to('tablet') {
    display: none !important;
  }

  &.show {
    opacity: 1;
    visibility: visible;
  }
}


@media (prefers-reduced-motion: reduce) {

  .msg-sidebar-container,
  .msg-sidebar-overlay {
    transition: none !important;
  }
}
