/* eslint-disable @typescript-eslint/no-explicit-any */

import { MsgTranslateService } from "@libs/services";
import { TYPE_NUMBER_FORMAT } from "./interfaces/msg-common.interface";


export const uuid = (): string => {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  const time = Date.now();

  return 'xxxx-xxxx-xxxx'.replace(/x/g, () => {
    const r = Math.random() * chars.length | 0;
    const t = (time + Math.random() * 1000) % chars.length | 0;
    const index = (r + t) % chars.length;
    return chars[index];
  });
};

/**
 * Mã hoá các ký tự đặc biệt trong HTML để tránh XSS attack
 */
export const sanitizeHtml = (html: string): string => {
  if (!html) { return ''; }

  return html
    // Mã hoá các thẻ script
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    // Mã hoá các event handlers
    .replace(/on\w+="[^"]*"/g, '')
    .replace(/on\w+='[^']*'/g, '')
    // Mã hoá các ký tự đặc biệt
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    // Mã hoá các thẻ iframe
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    // Mã hoá các thẻ object
    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
    // Mã hoá các thẻ embed
    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
    // Mã hoá các thẻ base
    .replace(/<base\b[^<]*(?:(?!<\/base>)<[^<]*)*<\/base>/gi, '')
    // Loại bỏ các thuộc tính javascript: trong href/src
    .replace(/javascript:[^"']*/g, '')
    // Loại bỏ các thuộc tính data: trong href/src
    .replace(/data:[^"']*/g, '');
};

export const getCpuInfo = (): string => {
  try {
    const cores = navigator.hardwareConcurrency || 1;
    const userAgent = navigator.userAgent;
    let cpuType = 'unknown';
    let cpuFound = false;

    if (userAgent.includes('Intel')) {
      cpuType = 'Intel';
      cpuFound = true;
    }

    if (!cpuFound && userAgent.includes('AMD')) {
      cpuType = 'AMD';
      cpuFound = true;
    }

    if (!cpuFound && userAgent.includes('Apple')) {
      cpuType = 'Apple';
      cpuFound = true;
    }

    if (!cpuFound && userAgent.includes('ARM')) {
      cpuType = 'ARM';
    }

    return `cpu-${cpuType}-${cores}`;
  } catch (e) {
    return 'cpu-error';
  }
};

export const formatMsgNumber = (value: any, divideBillion = false, numberFormat: TYPE_NUMBER_FORMAT = '1.0-2'): string => {
  if (value === null || value === undefined || Number.isNaN(Number(value))) {
    return '-';
  }

  let numericValue = Number(value);

  if (divideBillion) {
    numericValue /= 1_000_000_000;
  }

  const matched = numberFormat.match(/1\.(\d)-(\d)/);
  const minDigits = matched ? parseInt(matched[1], 10) : 0;
  const maxDigits = matched ? parseInt(matched[2], 10) : 0;

  const currentLang = MsgTranslateService.getCurrentLanguage?.() || 'vi';
  const localeLang: Record<string, string> = { vi: 'vi-VN', en: 'en-US' };
  const resolvedLocale = localeLang[currentLang] || 'vi-VN';

  const formatted = new Intl.NumberFormat(resolvedLocale, {
    minimumFractionDigits: minDigits,
    maximumFractionDigits: maxDigits,
  }).format(numericValue);

  return formatted;
};
