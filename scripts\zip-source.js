#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const readline = require('readline');
const { log } = require('./utils/console-utils');

const OUTPUT_DIR = path.join(process.cwd(), 'output');
const OUTPUT_SRC = path.join(OUTPUT_DIR, 'source.zip');
const ROOT = process.cwd();
const EXCLUDES = ['node_modules', '.angular', 'dist', 'package-lock.json', 'output'];

function shouldExclude(relPath) {
  return EXCLUDES.some(ex => relPath === ex || relPath.startsWith(ex + '/'));
}

function getFiles(dir, baseDir = '') {
  let results = [];
  const list = fs.readdirSync(dir);
  for (const file of list) {
    const relPath = path.join(baseDir, file).replace(/\\/g, '/');
    const absPath = path.join(dir, file);
    if (shouldExclude(relPath)) { continue; }
    const stat = fs.statSync(absPath);
    if (stat.isDirectory()) {
      const subFiles = getFiles(absPath, relPath);
      if (subFiles.length) { results = results.concat(subFiles); }
      continue;
    }
    if (stat.isFile()) {
      results.push(relPath);
    }
  }
  return results;
}

function prompt(question) {
  return new Promise(resolve => {
    const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
    rl.question(question, answer => {
      rl.close();
      resolve(answer);
    });
  });
}

async function selectDistProject() {
  const appsDir = path.join(ROOT, 'dist', 'apps');
  if (!fs.existsSync(appsDir)) {
    log.error('Không tìm thấy dist/apps!');
    process.exit(1);
  }
  const projects = fs.readdirSync(appsDir)
    .filter(f => fs.statSync(path.join(appsDir, f)).isDirectory());
  if (!projects.length) {
    log.error('Không có project nào trong dist/apps để nén!');
    process.exit(1);
  }
  log.title('Chọn project dist muốn nén:');
  projects.forEach((p, i) => log.info(`${i + 1}. ${p}`));
  const answer = await prompt('Nhập số thứ tự project: ');
  const idx = parseInt(answer.trim(), 10) - 1;
  if (isNaN(idx) || idx < 0 || idx >= projects.length) {
    log.error('Lựa chọn không hợp lệ!');
    process.exit(1);
  }
  return projects[idx];
}

function ensureOutputDir() {
  if (!fs.existsSync(OUTPUT_DIR)) { fs.mkdirSync(OUTPUT_DIR); }
}

(async () => {
  ensureOutputDir();
  log.title('Chọn loại source muốn nén:');
  log.info('1. Source code (loại trừ node_modules, .angular, dist, package-lock.json, output)');
  log.info('2. Build/dist (nén project trong dist/apps)');
  const answer = await prompt('Nhập số (1 hoặc 2): ');
  if (answer.trim() === '2') {
    const project = await selectDistProject();
    const projectDir = path.join(ROOT, 'dist', 'apps', project);
    const outputZip = path.join(OUTPUT_DIR, `source-${project}.zip`);
    if (fs.existsSync(outputZip)) { fs.unlinkSync(outputZip); }
    log.info(`Đang nén zip project ${project}...`);
    await zipFolder(projectDir, outputZip, project);
    return;
  }
  const files = getFiles(ROOT);
  if (!files.length) {
    log.error('Không tìm thấy file nào để nén!');
    process.exit(1);
  }
  if (fs.existsSync(OUTPUT_SRC)) { fs.unlinkSync(OUTPUT_SRC); }
  log.info('Đang nén zip...');
  await zipFiles(files, OUTPUT_SRC, ROOT);
})();

async function zipFiles(files, outputZip, baseDir) {
  const output = fs.createWriteStream(outputZip);
  const archive = archiver('zip', { zlib: { level: 9 } });

  output.on('close', () => {
    log.success(`Đã tạo ${outputZip} (${archive.pointer()} bytes)`);
  });
  archive.on('error', err => { throw err; });
  archive.pipe(output);

  for (const file of files) {
    archive.file(path.join(baseDir, file.replace(/^\/*/, '')), { name: file });
  }

  await archive.finalize();
}

async function zipFolder(folderPath, outputZip, rootName) {
  const output = fs.createWriteStream(outputZip);
  const archive = archiver('zip', { zlib: { level: 9 } });

  output.on('close', () => {
    log.success(`Đã tạo ${outputZip} (${archive.pointer()} bytes)`);
  });
  archive.on('error', err => { throw err; });
  archive.pipe(output);

  archive.directory(folderPath, rootName);

  await archive.finalize();
}
