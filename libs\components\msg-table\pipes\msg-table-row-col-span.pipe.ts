
import { Pipe, PipeTransform } from '@angular/core';
import { IMsgTableColumn } from '../interfaces';

@Pipe({
  name: 'msgTableRowColSpan',
  standalone: true
})
export class MsgTableRowColSpanPipe implements PipeTransform {
  transform<T>(value: IMsgTableColumn['rowSpan'] | IMsgTableColumn['colSpan'], item: T, index: number, data: T[]): number {
    if (!value) {
      return 1;
    }

    return typeof value === 'function' ? value(item, index, data) : value;
  }
}
