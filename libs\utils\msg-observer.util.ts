/* eslint-disable @typescript-eslint/no-explicit-any */
import { fromEventPattern, Observable, Subscription } from 'rxjs';
import { debounceTime, throttleTime } from 'rxjs/operators';
import { IMsgObserverResult, IMsgRateLimitOptions } from './interfaces/msg-observer.interface';

const applyRateLimit = <T>(
  source$: Observable<T>,
  options?: IMsgRateLimitOptions
): Observable<T> => {
  if (options?.debounce) {
    return source$.pipe(debounceTime(options.debounce));
  }
  if (options?.throttle) {
    return source$.pipe(throttleTime(options.throttle));
  }
  return source$;
};

const createObserver$ = <T>(
  setupObserver: (handler: (data: T) => void) => () => void,
  options?: IMsgRateLimitOptions
): IMsgObserverResult<T> => {
  const base$ = fromEventPattern<T>(setupObserver);
  const observable$ = applyRateLimit(base$, options);
  let subscription: Subscription | null = null;

  return {
    observable$: new Observable<T>(subscriber => {
      subscription = observable$.subscribe(subscriber);
      return () => {
        subscription?.unsubscribe();
        subscription = null;
      };
    }),
    unsubscribe: () => {
      subscription?.unsubscribe();
      subscription = null;
    }
  };
};

// ===== RESIZE OBSERVER =====
/**
 * 📐 RESIZE OBSERVER - Theo dõi thay đổi kích thước element
 *
 * @param element - Element cần theo dõi resize
 * @param options - Rate limiting options
 * @returns Observable emit ResizeObserverEntry[] khi element resize
 *
 * 💡 USE CASES:
 * - Responsive components tự động adjust layout
 * - Chart/graph tự động resize theo container
 * - Lazy loading based on element visibility
 * - Performance monitoring
 *  EXAMPLE:
 *  const resize$ = createResizeObserver$(element);
 *  resize$.subscribe((entries) => {
 *    entries.forEach((entry) => {
 *      console.log('Element resized:', entry);
 *    });
 *  });
 */
export const createResizeObserver$ = (
  element: Element,
  options?: IMsgRateLimitOptions
): IMsgObserverResult<ResizeObserverEntry[]> => {
  return createObserver$<ResizeObserverEntry[]>(
    (handler) => {
      const observer = new ResizeObserver(handler);
      observer.observe(element);
      return () => observer.disconnect();
    },
    options
  );
};

// ===== MUTATION OBSERVER =====
/**
 * 🔄 MUTATION OBSERVER - Theo dõi thay đổi DOM tree
 *
 * @param element - Element root cần theo dõi
 * @param config - Cấu hình loại mutations cần observe
 * @param options - Rate limiting options
 * @returns Observable emit MutationRecord[] khi DOM thay đổi
 *
 * 💡 USE CASES:
 * - Virtual scrolling khi DOM content thay đổi
 * - Auto-save khi user chỉnh sửa content
 * - Sync state với DOM changes
 * - Debug DOM modifications
 * - Content change detection
 *  EXAMPLE:
 *  const mutation$ = createMutationObserver$(element, { childList: true, subtree: true });
 *  mutation$.subscribe((mutations) => {
 *    mutations.forEach((mutation) => {
 *      console.log('DOM changed:', mutation);
 *    });
 *  });
 */
export const createMutationObserver$ = (
  element: Element,
  config: MutationObserverInit = { childList: true, subtree: true },
  options?: IMsgRateLimitOptions
): IMsgObserverResult<MutationRecord[]> => {
  return createObserver$<MutationRecord[]>(
    (handler) => {
      const observer = new MutationObserver(handler);
      observer.observe(element, config);
      return () => observer.disconnect();
    },
    options
  );
};

// ===== INTERSECTION OBSERVER =====
/**
 * 👁️ INTERSECTION OBSERVER - Theo dõi element vào/ra viewport
 *
 * @param element - Element cần theo dõi intersection
 * @param observerOptions - Cấu hình IntersectionObserver (root, threshold, etc.)
 * @param rateOptions - Rate limiting options
 * @returns Observable emit IntersectionObserverEntry[] khi intersection thay đổi
 *
 * 💡 USE CASES:
 * - Lazy loading images/components
 * - Infinite scrolling
 * - Animation triggers khi element visible
 * - Analytics tracking (viewability)
 * - Auto-pause video khi ra khỏi viewport
 *  EXAMPLE:
 *  const intersection$ = createIntersectionObserver$(element, { threshold: 0.5 });
 *  intersection$.subscribe((entries) => {
 *    entries.forEach((entry) => {
 *      if (entry.isIntersecting) {
 *        // Element is visible
 *      }
 *    });
 *  });
 */
export const createIntersectionObserver$ = (
  element: Element,
  observerOptions?: IntersectionObserverInit,
  rateOptions?: IMsgRateLimitOptions
): IMsgObserverResult<IntersectionObserverEntry[]> => {
  return createObserver$<IntersectionObserverEntry[]>(
    (handler) => {
      const observer = new IntersectionObserver(handler, observerOptions);
      observer.observe(element);
      return () => observer.disconnect();
    },
    rateOptions
  );
};

// ===== PERFORMANCE OBSERVER =====
/**
 * ⚡ PERFORMANCE OBSERVER - Theo dõi performance metrics
 *
 * @param entryTypes - Loại performance entries cần observe
 * @param options - Rate limiting options
 * @returns Observable emit PerformanceObserverEntryList với performance data
 *
 * 📊 ENTRY TYPES phổ biến:
 * - 'navigation': Page load performance
 * - 'resource': Resource loading times
 * - 'paint': First paint, first contentful paint
 * - 'largest-contentful-paint': LCP metric
 * - 'layout-shift': CLS metric
 * - 'long-task': Tasks > 50ms
 *
 * 💡 USE CASES:
 * - Real-time performance monitoring
 * - User experience analytics
 * - Performance budgets enforcement
 * - Automatic performance alerts
 *  EXAMPLE:
 *  const performance$ = createPerformanceObserver$(['long-task']);
 *  performance$.subscribe((entryList) => {
 *    entryList.getEntries().forEach((entry) => {
 *      console.log('Long task duration:', entry.duration);
 *    });
 *  });
 */
export const createPerformanceObserver$ = (
  entryTypes: string[],
  options?: IMsgRateLimitOptions
): IMsgObserverResult<PerformanceObserverEntryList> => {
  return createObserver$<PerformanceObserverEntryList>(
    (handler) => {
      const observer = new PerformanceObserver(handler);
      observer.observe({ entryTypes });
      return () => observer.disconnect();
    },
    options
  );
};

// ===== IDLE CALLBACK OBSERVER =====
/**
 * 🎯 IDLE CALLBACK OBSERVER - Theo dõi khi browser idle (rảnh rỗi)
 *
 * @param idleOptions - Cấu hình IdleRequestOptions (timeout)
 * @param rateOptions - Rate limiting options
 * @returns Observable emit IdleDeadline khi browser idle
 *
 * 💡 IdleDeadline properties:
 * - timeRemaining(): number - Thời gian còn lại của idle period (ms)
 * - didTimeout: boolean - Có bị timeout không
 *
 * 🚀 USE CASES:
 * - Background tasks khi user không tương tác
 * - Non-critical data processing
 * - Prefetching/preloading resources
 * - Analytics data sending
 * - DOM cleanup operations
 * - Cache optimization
 * - Image lazy loading processing
 *  EXAMPLE:
 *  const idle$ = createIdleObserver$({ timeout: 1000 });
 *  idle$.subscribe((deadline) => {
 *    if (deadline.timeRemaining() > 0) {
 *      // Do some background work
 *    }
 *  });
 */
export const createIdleObserver$ = (
  idleOptions?: IdleRequestOptions,
  rateOptions?: IMsgRateLimitOptions
): IMsgObserverResult<IdleDeadline> => {
  return createObserver$<IdleDeadline>(
    (handler) => {
      let isRunning = true;
      let currentHandle: number | null = null;

      const scheduleNext = () => {
        if (!isRunning) { return; }

        // Fallback cho browsers không support requestIdleCallback
        if (typeof requestIdleCallback === 'undefined') {
          currentHandle = setTimeout(() => {
            if (isRunning) {
              // Mock IdleDeadline object
              const mockDeadline: IdleDeadline = {
                timeRemaining: () => 16.67, // ~60fps frame
                didTimeout: false
              };
              handler(mockDeadline);
              scheduleNext();
            }
          }, 16) as any; // Fallback to 60fps
          return;
        }

        currentHandle = requestIdleCallback((deadline) => {
          if (isRunning) {
            handler(deadline);
            scheduleNext(); // Schedule next idle callback
          }
        }, idleOptions);
      };

      // Start the idle callback loop
      scheduleNext();

      // Return cleanup function
      return () => {
        isRunning = false;
        if (currentHandle !== null) {
          if (typeof cancelIdleCallback !== 'undefined') {
            cancelIdleCallback(currentHandle);
          } else {
            clearTimeout(currentHandle);
          }
          currentHandle = null;
        }
      };
    },
    rateOptions
  );
};


// ===== SIMPLE INTERVAL OBSERVER =====
/**
 * ⏰ SIMPLE INTERVAL OBSERVER - Đơn giản với callback, interval, timeout
 *
 * @param callback - Function được gọi mỗi interval, nhận (elapsed, unsubscribe) => void
 * @param intervalMs - Thời gian interval (ms), default: 200ms
 * @param timeoutMs - Timeout tối đa (ms), default: 2000ms
 * @returns { observable$, unsubscribe } - Observable và function unsubscribe
 *
 * 💡 USE CASES:
 * - Simple polling với logic tự quyết định khi nào dừng
 * - Timer/countdown functionality
 * - DOM polling với custom logic
 * - Retry mechanisms với timeout
 */
export const createIntervalObserver$ = (
  callback: (elapsed: number, unsubscribe: () => void) => void,
  intervalMs = 200,
  timeoutMs = 2000
): IMsgObserverResult<void> => {
  return createObserver$<void>(
    (handler) => {
      let elapsed = 0;
      let intervalId: number | null = null;
      let isRunning = true;

      const unsubscribe = () => {
        if (!isRunning) { return; }
        isRunning = false;
        if (intervalId !== null) {
          clearTimeout(intervalId);
          intervalId = null;
        }

        handler(); // Emit để complete observable
      };

      const poll = () => {
        if (!isRunning) { return; }

        elapsed += intervalMs;

        try {
          // Call user callback với elapsed time và unsubscribe function
          callback(elapsed, unsubscribe);
        } catch (error) {
          console.error('IntervalObserver callback error:', error);
          unsubscribe();
          return;
        }

        // Auto timeout
        if (elapsed >= timeoutMs) {
          console.log(`IntervalObserver timeout after ${timeoutMs}ms`);
          unsubscribe();
          return;
        }

        // Schedule next poll if still running
        if (isRunning) {
          intervalId = setTimeout(poll, intervalMs) as any;
        }
      };

      // Start polling
      poll();

      // Return cleanup function
      return unsubscribe;
    }
  );
};
