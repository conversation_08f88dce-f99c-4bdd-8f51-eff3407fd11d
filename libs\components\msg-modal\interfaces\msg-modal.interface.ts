import { ComponentRef } from "@angular/core";
import { MsgModalSharedComponent } from "../msg-modal.component";

export type TYPE_MODAL = 'info' | 'warn' | 'success' | 'danger';

export interface IMsgModalOptionsDto {
  type?: TYPE_MODAL;
  title?: string;
  classContainer?: string;
  content?: string;
  textOk?: string;
  textCancel?: string;
  showCancel?: boolean;
  showOk?: boolean;
  closeOnOverlayClick?: boolean;
}

export interface IMsgModalFunctionControl {
  show: () => void;
  remove: (compRef?: ComponentRef<MsgModalSharedComponent>) => void;
  setLoading: (loading: boolean) => void;
}

export interface IMsgModalServiceControl extends IMsgModalFunctionControl {
  instance: MsgModalSharedComponent;
}
