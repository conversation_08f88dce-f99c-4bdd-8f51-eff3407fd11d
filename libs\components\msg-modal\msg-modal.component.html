<!-- Overlay -->
<div class="msg-modal-overlay"
  (click)="handleOverlayClick($event)"></div>

<!-- Modal Container -->
<div #modalElement
  class="msg-modal-container"
  [ngClass]="modalClasses()">

  <!-- Header for component mode -->
  @if (! msgServiceMode()) {
    <ng-content select="[msg-modal-header]">
      <div class="msg-modal-header">
        <div class="msg-modal-title"
          nz-tooltip
          [nzTooltipTitle]="((msgTitleElement | msgTruncate | async)) ? msgTitle() : ''"
          [nzTooltipTrigger]="'hover'"
          [nzTooltipPlacement]="'top'">
          <h2 #msgTitleElement
            [style.max-width.px]="msgTitleWidth()"
            [class]="'truncate'">{{ msgTitle() | translate }}</h2>
        </div>
        <div class="msg-modal-actions">
          <msg-button-shared [msgType]="'text'"
            [msgClass]="'msg-modal-close'"
            [msgIconRight]="'X'"
            [msgIconSize]="20"
            (msgClick)="handlerCloseModal($event)">
          </msg-button-shared>
        </div>
      </div>
    </ng-content>
  }

  <!-- Content -->
  <div class="msg-modal-content">
    <ng-content select="[msg-modal-content]">
      @if (msgServiceMode()) {
        <!-- Service mode content -->
        <div class="msg-modal-service-content">
          @if (modalIcon()) {
            <div class="msg-modal-icon"
              [ngClass]="modalIconClass()">
              <msg-icon-shared [msgName]="modalIcon()!"
                [msgSize]="48"
                [msgStroke]="2" />
            </div>
          }
          <div class="msg-modal-message">
            <h3 class="!m-0">{{ msgTitle() | translate }}</h3>
            <p class="!mt-1">{{ msgContent() | translate }}</p>
          </div>
        </div>
      } @else {
        <!-- Default placeholder content -->
        <div class="msg-modal-content-placeholder">
          <h3 class="!m-0">Modal Content</h3>
          <p class="!mt-1">Please add your content here using content projection.</p>
        </div>
      }
    </ng-content>
  </div>

  <!-- Footer -->
  <ng-content select="[msg-modal-footer]">
    <div class="msg-modal-footer"
      [attr.msg-service-mode]="msgServiceMode()">
      <div class="msg-modal-footer-actions">
        @if (msgShowCancel()) {
          <msg-button-shared [msgText]="msgTextCancel() | translate"
            [msgDisabled]="msgLoading()"
            [msgType]="'outline-secondary'"
            [msgClass]="'min-w-[80px] min-h-[35px]'"
            (msgClick)="handlerCloseModal($event)" />
        }
        @if (msgShowOk()) {
          <msg-button-shared [msgText]="msgTextOk() | translate"
            [msgLoading]="msgLoading()"
            [msgType]="'success'"
            [msgClass]="'min-w-[80px] min-h-[35px]'"
            (msgClick)="handlerSaveChanges($event)" />
        }
      </div>
    </div>
  </ng-content>

</div>
