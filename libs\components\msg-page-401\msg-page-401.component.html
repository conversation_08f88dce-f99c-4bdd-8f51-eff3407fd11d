<div class="w-full flex flex-col h-[100dvh]">
  <div class="sticky top-0 z-[20] shrink-0">
    <div class="h-[45px] bg-white flex items-center justify-between md:px-[16px] text-dark font-semibold text-[13px] border-b border-gray-300 uppercase z-[1000]">
    </div>
  </div>
  <div class="flex justify-center items-center min-h-full place-items-center px-6 py-24 sm:py-32 lg:px-8">
    <div class="text-center">
      <div class="flex items-center justify-center mb-[16px]">
        <img ngSrc="assets/images/msg-error-401.svg"
          priority
          width="400"
          height="400">
      </div>
      <p class="mt-6 text-sm font-medium text-pretty text-gray-500">{{ 'i18n_page_401_no_permission' | translate }}</p>
      <div class="mt-5 flex items-center justify-center gap-x-6">
        <msg-button-shared [msgText]="'i18n_page_back_home'"
          (msgClick)="onBackHomeClick()" />
      </div>
    </div>
  </div>
</div>
