import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class MsgThemeService {
  private readonly _THEME_KEY = 'currentTheme';
  private readonly _DARK_THEME = 'dark';
  private readonly _LIGHT_THEME = 'light';

  constructor() {
    const savedTheme = localStorage.getItem(this._THEME_KEY);
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    this._setTheme(savedTheme || (prefersDark ? this._DARK_THEME : this._LIGHT_THEME));
  }

  protected get currentTheme(): string {
    return document.documentElement.classList.contains(this._DARK_THEME)
      ? this._DARK_THEME
      : this._LIGHT_THEME;
  }

  protected toggleTheme(): void {
    this._setTheme(this.currentTheme === this._DARK_THEME ? this._LIGHT_THEME : this._DARK_THEME);
  }

  private _setTheme(theme: string): void {
    if (theme === this._DARK_THEME) {
      document.documentElement.classList.add(this._DARK_THEME);
    } else {
      document.documentElement.classList.remove(this._DARK_THEME);
    }

    localStorage.setItem(this._THEME_KEY, theme);
  }
}
