{"name": "msg-crm", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "msg", "sourceRoot": "apps/msg-crm/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/msg-crm", "index": "apps/msg-crm/src/index.html", "browser": "apps/msg-crm/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/msg-crm/tsconfig.app.json", "inlineStyleLanguage": "scss", "stylePreprocessorOptions": {"includePaths": ["libs/assets/scss"]}, "assets": [{"glob": "**/*", "input": "apps/msg-crm/public", "output": "/"}, {"glob": "**/*", "input": "apps/msg-crm/src/assets", "output": "/msg-crm/assets"}, {"glob": "**/*", "input": "libs/assets", "output": "/assets"}], "styles": ["node_modules/ng-zorro-antd/ng-zorro-antd.min.css", "apps/msg-crm/src/styles.scss", "libs/assets/css/_tailwind.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "16kb", "maximumError": "32kb"}], "outputHashing": "all", "fileReplacements": [{"replace": "apps/msg-crm/src/envs/env.ts", "with": "apps/msg-crm/src/envs/env.prod.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "apps/msg-crm/src/envs/env.ts", "with": "apps/msg-crm/src/envs/env.dev.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"continuous": true, "executor": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "msg-crm:build:production"}, "development": {"buildTarget": "msg-crm:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "msg-crm:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "msg-crm:build", "staticFilePath": "dist/apps/msg-crm/browser", "spa": true}}}}