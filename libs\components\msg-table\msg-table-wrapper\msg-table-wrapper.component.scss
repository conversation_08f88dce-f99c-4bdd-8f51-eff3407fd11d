input[type="checkbox"] {
  appearance: none;
  position: relative;
  border: 2px solid #d1d5db;
  border-radius: 2px;
  background-color: #f9fafb;
  cursor: pointer;

  &:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
  }

  &:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 9px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: translate(-50%, -60%) rotate(45deg);
  }

  &:indeterminate {
    background-color: #3b82f6;
    border-color: #3b82f6;
  }

  &:indeterminate::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 10px;
    height: 2px;
    background-color: white;
    transform: translate(-50%, -50%);
  }

  &:hover:not(:disabled) {
    border-color: #93c5fd;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }
}

.msg-expand-button {
  position: relative;
  border: none;
  background: transparent;
  transition: all 0.2s ease-in-out;
  cursor: pointer;

  &:hover {
    background-color: #f3f4f6;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }

  &:active {
    transform: scale(0.95);
    background-color: #e5e7eb;
  }
}

.msg-expand-icon {
  transition: all 0.2s ease-in-out;

  .msg-expand-button:hover & {
    color: #3b82f6 !important;
  }
}

.msg-checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.msg-table-expand-container {
  height: 0px !important;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background-color: var(--color-green-500);
  }
}

.msg-expanded-content {
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0);
  animation: msgSlideDown 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
  backface-visibility: hidden;
  perspective: 1000px;
  contain: layout style paint;
  position: relative;
  isolation: isolate;
  border-top: 1px solid var(--color-green-500);
  border-bottom: 1px solid var(--color-green-500);

  ::ng-deep table {
    tbody tr:not(.msg-tr-expand) {

      td.msg-sticky-left,
      td.msg-sticky-right {
        background-color: var(--msg-table-bg-color, white) !important;

        &:hover {
          background-color: var(--msg-table-hover-bg-color, #f3f4f6) !important;
        }
      }
    }

    thead {

      th.msg-sticky-left,
      th.msg-sticky-right {
        background-color: var(--color-neutral-100, #f5f5f5) !important;

        &:hover {
          background-color: var(--color-neutral-200, #e5e5e5) !important;
        }
      }
    }

    tfoot {

      td.msg-sticky-left,
      td.msg-sticky-right {
        background-color: var(--msg-table-bg-color, white) !important;

        &:hover {
          background-color: var(--msg-table-hover-bg-color, #f3f4f6) !important;
        }
      }
    }

    tbody tr:not(.msg-tr-expand):hover {

      td.msg-sticky-left,
      td.msg-sticky-right {
        background-color: var(--msg-table-hover-bg-color, #f3f4f6) !important;
      }
    }
  }
}

@keyframes msgSlideDown {
  from {
    opacity: 0;
    transform: translate3d(0, -8px, 0) scale(0.98);
    visibility: visible;
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
    visibility: visible;
  }
}

::ng-deep .msg-table-tbody {
  .msg-table-tbody-tr {
    td {
      transition: none !important;
    }
  }

  .msg-table-expand-container {
    padding: 0 !important;
    vertical-align: top;

    .msg-expanded-content {
      margin: 0px;
    }
  }
}
