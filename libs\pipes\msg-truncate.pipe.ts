import { Pipe, PipeTransform } from '@angular/core';
import { Observable } from 'rxjs';

@Pipe({
  name: 'msgTruncate'
})
export class MsgTruncatePipe implements PipeTransform {
  transform(element: HTMLElement | null, _keyFetch?: string | number | boolean): Observable<boolean> {
    _keyFetch;
    if (!element) {
      return new Observable(observer => observer.next(false));
    }

    return new Observable<boolean>(observer => {
      const observerCallback = ([entry]: IntersectionObserverEntry[]) => {
        if (!entry.isIntersecting) {
          return;
        }

        const isTruncated = this._checkTruncation(element);
        observer.next(isTruncated);
        observer.complete();
        intersectionObserver.disconnect();
      };

      const intersectionObserver = new IntersectionObserver(observerCallback, { threshold: 0 });
      intersectionObserver.observe(element);
      return () => intersectionObserver.disconnect();
    });
  }

  private _checkTruncation(element: HTMLElement): boolean {
    const computedStyle = window.getComputedStyle(element);
    const hasTruncateStyles =
      computedStyle.textOverflow === 'ellipsis' &&
      (computedStyle.overflow === 'hidden' || computedStyle.overflowX === 'hidden') &&
      computedStyle.whiteSpace === 'nowrap';

    if (!hasTruncateStyles) {
      return false;
    }

    return element.scrollWidth > element.offsetWidth;
  }
}
