import { CommonModule } from "@angular/common";
import { AfterViewInit, Component, computed, ElementRef, input, OnDestroy, signal } from "@angular/core";
import { IMsgSkeletonDto } from "./interfaces/msg-skeleton.interface";

@Component({
  selector: 'msg-skeleton-shared',
  templateUrl: './msg-skeleton.component.html',
  styleUrls: ['./msg-skeleton.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class MsgSkeletonSharedComponent implements AfterViewInit, OnDestroy {
  public msgType = input<IMsgSkeletonDto['msgType']>('bar');
  public msgWidth = input<string>('100%');
  public msgNumberRow = input<number | undefined>(undefined);
  public msgSize = input<IMsgSkeletonDto['msgSize']>('md');
  public msgClass = input<string>('');
  public msgSkeletonClass = input<string>('');
  public msgGap = input<number>(8);

  private _calculatedRows = signal<number>(1);
  private _parentElement: HTMLElement | null = null;
  private _originalOverflow = '';

  constructor(private _elementRef: ElementRef) { }

  ngAfterViewInit() {
    if (this.msgType() === 'bar') {
      this._calculateBarRows();
    }

    this._setParentOverflowHidden();
  }

  ngOnDestroy() {
    this._restoreParentOverflow();
  }

  private _setParentOverflowHidden() {
    this._parentElement = this._elementRef.nativeElement.parentElement;
    if (this._parentElement) {
      this._originalOverflow = this._parentElement.style.overflow || '';
      this._parentElement.style.overflow = 'hidden';
    }
  }

  private _restoreParentOverflow() {
    if (this._parentElement) {
      this._parentElement.style.overflow = this._originalOverflow;
    }
  }

  private _calculateBarRows() {
    const parentElement = this._elementRef.nativeElement.parentElement;
    if (!parentElement) {
      return;
    }

    if (this.msgNumberRow()) {
      this._calculatedRows.set(this.msgNumberRow() || 1);
      return;
    }

    this._calculatedRows.set(1);
  }

  protected skeletonClasses = computed(() => {
    const baseClass = 'msg-skeleton';
    const typeClass = `msg-skeleton-${this.msgType()}`;
    const sizeClass = this.msgType() === 'circle' ? `msg-skeleton-${this.msgType()}-size-${this.msgSize()}` : '';
    const skeletonCustomClass = this.msgSkeletonClass();
    return `${baseClass} ${typeClass} ${sizeClass} ${skeletonCustomClass}`.trim();
  });

  protected shouldShowMultipleRows = computed(() => {
    return this.msgType() === 'bar' && this.msgNumberRow();
  });

  protected autoRowsArray = computed<Array<{ index: number, height: string }>>(() => {
    if (this.msgType() !== 'bar') {
      return [{ index: 0, height: '0px' }];
    }

    const rowCount = this._calculatedRows();
    const gapBetweenRows = this.msgGap();
    const totalGapSpace = (rowCount - 1) * gapBetweenRows;
    const rowHeight = `calc((100% - ${totalGapSpace}px) / ${rowCount})`;

    return Array.from({ length: rowCount }, (_, i) => ({
      index: i,
      height: rowHeight
    }));
  });
}
