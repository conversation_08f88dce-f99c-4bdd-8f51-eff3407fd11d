/* eslint-disable @typescript-eslint/no-explicit-any */
import { TemplateRef } from "@angular/core";
import { SafeHtml } from "@angular/platform-browser";

type TYPE_MSG_TABLE_COLUMN = 'checkbox' | 'expand';
export interface IMsgTableColumn<T = any> {
  label?: string;
  value?: string | ((item: T, index: number, data: T[]) => string | number | TemplateRef<T>);
  width?: number;
  rowSpan?: number | ((item: T, index: number, data: T[]) => number);
  colSpan?: number | ((item: T, index: number, data: T[]) => number);
  stickyLeft?: boolean;
  stickyRight?: boolean;
  disabled?: boolean | ((item: T, index: number, data: T[]) => boolean);
  style?: string | ((item: T, index: number, data: T[]) => string);
  class?: string | ((item: T, index: number, data: T[]) => string);
  styleHeader?: string | ((item: T, index: number, data: T[]) => string);
  classHeader?: string | ((item: T, index: number, data: T[]) => string);
  children?: IMsgTableColumn<T>[];
  type?: TYPE_MSG_TABLE_COLUMN;
  attr?: (item: T, index: number, data: T[]) => Record<string, any>;
  sortKey?: string;
  sourOrder?: 'ascend' | 'descend';
}

export interface IMsgTableHeader<T = any> extends IMsgTableColumn<T> {
  sortKey?: string;
  checked?: boolean | ((item: T, index: number, data: T[]) => boolean);
  expanded?: boolean | ((item: T, index: number, data: T[]) => boolean);
  checkAllState?: boolean | 'indeterminate';
  onExpandChange?: (item: T, expanded: boolean, index: number) => void;
  expandedTemplate?: (item: T, index: number) => TemplateRef<T> | string;
  onCheckAll?: (checked: boolean, items: T[]) => void;
  onCheckChange?: (item: T, checked: boolean, index: number) => void;
  onSortChange?: (sortKey: string, order: 'ascend' | 'descend', items: T[]) => void;
}

export interface IMsgTemplateTableCell<T> {
  type: 'template' | 'html' | 'text';
  value: TemplateRef<T> | SafeHtml | string;
}

export interface IMsgTablePagination {
  pageIndex: number;
  pageSize: number;
  total: number;
}

export interface IMsgTableExpandMap {
  content: string | null;
  template: TemplateRef<any> | null;
  index: number;
  item: any;
}

export interface IMsgTableConfig<T = any> {
  header: IMsgTableHeader<T>[];
  footer?: IMsgTableColumn<T>[][] | ((data: T[]) => TemplateRef<T> | string);
  fieldKey?: string;
  showHeader?: boolean;
  rowClass?: string | ((item: T, index: number, data: T[]) => string);
  rowStyle?: string | ((item: T, index: number, data: any[]) => string);
  pagination?: {
    isServerSide?: boolean;
    onChange?: (pagination: IMsgTablePagination) => void;
    pageSizeOptions?: number[];
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    showPagination?: boolean;
    total?: number;
    sizePagination?: 'default' | 'small';
  };
}

export interface IMsgTableFunctionControl {
  onUpdate: (item: any) => void;
  onDelete: (item: any) => void;
  getPagination: () => IMsgTablePagination;
}
