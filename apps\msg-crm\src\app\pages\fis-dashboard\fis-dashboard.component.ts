import { ChangeDetectionStrategy, Component, computed, OnInit, signal } from '@angular/core';
import { MsgTableModule } from '@libs/components/msg-table';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzSelectModule } from 'ng-zorro-antd/select';

@Component({
  selector: 'msg-fis-dashboard',
  templateUrl: './fis-dashboard.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    NzCardModule,
    NzDatePickerModule,
    NzSelectModule,
    MsgTableModule
  ]
})
export class MsgFisDashboardComponent implements OnInit {
  public table1Data = signal([
    { id: 1, name: 'A1', value: 100, type: 'Loại 1', date: '2024-06-01', status: '<PERSON>ang xử lý', note: '<PERSON><PERSON> chú A1' },
    { id: 2, name: 'A2', value: 200, type: 'Loại 2', date: '2024-06-02', status: '<PERSON>àn thành', note: '<PERSON><PERSON> chú A2' },
    { id: 3, name: 'A3', value: 300, type: 'Loại 1', date: '2024-06-03', status: 'Lỗi', note: 'Ghi chú A3' },
    { id: 4, name: 'A4', value: 400, type: 'Loại 2', date: '2024-06-04', status: 'Đang xử lý', note: 'Ghi chú A4' },
    { id: 5, name: 'A5', value: 500, type: 'Loại 1', date: '2024-06-05', status: 'Hoàn thành', note: 'Ghi chú A5' },
    { id: 6, name: 'A6', value: 600, type: 'Loại 2', date: '2024-06-06', status: 'Lỗi', note: 'Ghi chú A6' },
    { id: 7, name: 'A7', value: 700, type: 'Loại 1', date: '2024-06-07', status: 'Đang xử lý', note: 'Ghi chú A7' },
    { id: 8, name: 'A8', value: 800, type: 'Loại 2', date: '2024-06-08', status: 'Hoàn thành', note: 'Ghi chú A8' },
    { id: 9, name: 'A9', value: 900, type: 'Loại 1', date: '2024-06-09', status: 'Lỗi', note: 'Ghi chú A9' },
    { id: 10, name: 'A10', value: 1000, type: 'Loại 2', date: '2024-06-10', status: 'Đang xử lý', note: 'Ghi chú A10' },
  ]);
  public table2Data = signal([
    { id: 1, name: 'B1', value: 400, type: 'Loại 2', date: '2024-06-04', status: 'Đang xử lý', note: 'Ghi chú B1' },
    { id: 2, name: 'B2', value: 500, type: 'Loại 1', date: '2024-06-05', status: 'Hoàn thành', note: 'Ghi chú B2' },
    { id: 3, name: 'B3', value: 600, type: 'Loại 2', date: '2024-06-06', status: 'Lỗi', note: 'Ghi chú B3' },
    { id: 4, name: 'B4', value: 700, type: 'Loại 1', date: '2024-06-07', status: 'Đang xử lý', note: 'Ghi chú B4' },
    { id: 5, name: 'B5', value: 800, type: 'Loại 2', date: '2024-06-08', status: 'Hoàn thành', note: 'Ghi chú B5' },
    { id: 6, name: 'B6', value: 900, type: 'Loại 1', date: '2024-06-09', status: 'Lỗi', note: 'Ghi chú B6' },
    { id: 7, name: 'B7', value: 1000, type: 'Loại 2', date: '2024-06-10', status: 'Đang xử lý', note: 'Ghi chú B7' },
    { id: 8, name: 'B8', value: 1100, type: 'Loại 1', date: '2024-06-11', status: 'Hoàn thành', note: 'Ghi chú B8' },
    { id: 9, name: 'B9', value: 1200, type: 'Loại 2', date: '2024-06-12', status: 'Lỗi', note: 'Ghi chú B9' },
    { id: 10, name: 'B10', value: 1300, type: 'Loại 1', date: '2024-06-13', status: 'Đang xử lý', note: 'Ghi chú B10' },
  ]);
  public table3Data = signal(Array.from({ length: 20 }, (_, i) => ({
    id: i + 1,
    name: `C${i + 1}`,
    value: 1000 + i * 10,
    type: i % 2 === 0 ? 'Loại 1' : 'Loại 2',
    date: `2024-06-${(i % 30 + 1).toString().padStart(2, '0')}`,
    status: i % 3 === 0 ? 'Đang xử lý' : (i % 3 === 1 ? 'Hoàn thành' : 'Lỗi'),
    note: `Ghi chú C${i + 1}`
  })));
  public table4Data = signal(Array.from({ length: 25 }, (_, i) => ({
    id: i + 1,
    name: `D${i + 1}`,
    value: 2000 + i * 10,
    type: i % 2 === 0 ? 'Loại 2' : 'Loại 1',
    date: `2024-07-${(i % 30 + 1).toString().padStart(2, '0')}`,
    status: i % 3 === 0 ? 'Đang xử lý' : (i % 3 === 1 ? 'Hoàn thành' : 'Lỗi'),
    note: `Ghi chú D${i + 1}`
  })));

  public tableConfig = computed(() => ([
    {
      fieldKey: 'id',
      header: [
        { label: 'ID', value: (item: any) => item.id, width: 60, class: 'text-center text-gray-500', classHeader: '!bg-gray-200 !text-gray-700', stickyLeft: true },
        { label: 'Tên', value: (item: any) => item.name, width: 120, class: 'text-purple-700 font-semibold', classHeader: '!bg-purple-100 !text-purple-700', stickyLeft: true },
        { label: 'Giá trị', value: (item: any) => item.value, width: 100, class: (item: any) => item.value > 1050 ? 'text-orange-600 font-bold' : 'text-green-600', classHeader: '!bg-orange-100 !text-orange-700' },
        { label: 'Loại', value: (item: any) => item.type, width: 100, class: (item: any) => item.type === 'Loại 1' ? 'text-blue-600' : 'text-pink-600', classHeader: '!bg-blue-100 !text-blue-700' },
        { label: 'Ngày', value: (item: any) => item.date, width: 110, class: 'text-gray-700', classHeader: '!bg-gray-100 !text-gray-700' },
        { label: 'Trạng thái', value: (item: any) => item.status, width: 120, class: (item: any) => item.status === 'Hoàn thành' ? 'text-green-700 font-bold' : (item.status === 'Lỗi' ? 'text-red-600 font-bold' : 'text-yellow-700'), classHeader: '!bg-green-100 !text-green-700' },
        { label: 'Ghi chú', value: (item: any) => item.note, width: 160, class: 'italic text-gray-500', classHeader: '!bg-gray-50 !text-gray-500' },
      ],
      pagination: { showPagination: false },
      scroll: { x: 800, y: null }
    },
    {
      fieldKey: 'id',
      header: [
        { label: 'ID', value: (item: any) => item.id, width: 60, class: 'text-center text-gray-500', classHeader: '!bg-gray-200 !text-gray-700', stickyLeft: true },
        { label: 'Tên', value: (item: any) => item.name, width: 120, class: 'text-blue-700 font-semibold', classHeader: '!bg-blue-100 !text-blue-700', stickyLeft: true },
        { label: 'Giá trị', value: (item: any) => item.value, width: 100, class: (item: any) => item.value > 2050 ? 'text-orange-600 font-bold' : 'text-green-600', classHeader: '!bg-orange-100 !text-orange-700' },
        { label: 'Loại', value: (item: any) => item.type, width: 100, class: (item: any) => item.type === 'Loại 1' ? 'text-blue-600' : 'text-pink-600', classHeader: '!bg-blue-100 !text-blue-700' },
        { label: 'Ngày', value: (item: any) => item.date, width: 110, class: 'text-gray-700', classHeader: '!bg-gray-100 !text-gray-700' },
        { label: 'Trạng thái', value: (item: any) => item.status, width: 120, class: (item: any) => item.status === 'Hoàn thành' ? 'text-green-700 font-bold' : (item.status === 'Lỗi' ? 'text-red-600 font-bold' : 'text-yellow-700'), classHeader: '!bg-green-100 !text-green-700' },
        { label: 'Ghi chú', value: (item: any) => item.note, width: 160, class: 'italic text-gray-500', classHeader: '!bg-gray-50 !text-gray-500' },
      ],
      pagination: { showPagination: false },
      scroll: { x: 800, y: null }
    },
    {
      fieldKey: 'id',
      header: [
        { label: 'ID', value: (item: any) => item.id, width: 60, class: 'text-center text-gray-500', classHeader: '!bg-gray-200 !text-gray-700', stickyLeft: true },
        { label: 'Tên', value: (item: any) => item.name, width: 120, class: 'text-pink-700 font-semibold', classHeader: '!bg-pink-100 !text-pink-700', stickyLeft: true },
        { label: 'Giá trị', value: (item: any) => item.value, width: 100, class: (item: any) => item.value > 1100 ? 'text-orange-600 font-bold' : 'text-green-600', classHeader: '!bg-orange-100 !text-orange-700' },
        { label: 'Loại', value: (item: any) => item.type, width: 100, class: (item: any) => item.type === 'Loại 1' ? 'text-blue-600' : 'text-pink-600', classHeader: '!bg-blue-100 !text-blue-700' },
        { label: 'Ngày', value: (item: any) => item.date, width: 110, class: 'text-gray-700', classHeader: '!bg-gray-100 !text-gray-700' },
        { label: 'Trạng thái', value: (item: any) => item.status, width: 120, class: (item: any) => item.status === 'Hoàn thành' ? 'text-green-700 font-bold' : (item.status === 'Lỗi' ? 'text-red-600 font-bold' : 'text-yellow-700'), classHeader: '!bg-green-100 !text-green-700' },
        { label: 'Ghi chú', value: (item: any) => item.note, width: 160, class: 'italic text-gray-500', classHeader: '!bg-gray-50 !text-gray-500' },
      ],
      pagination: { showPagination: false },
      scroll: { x: 900, y: 500 }
    },
    {
      fieldKey: 'id',
      header: [
        { label: 'ID', value: (item: any) => item.id, width: 60, class: 'text-center text-gray-500', classHeader: '!bg-gray-200 !text-gray-700', stickyLeft: true },
        { label: 'Tên', value: (item: any) => item.name, width: 120, class: 'text-green-700 font-semibold', classHeader: '!bg-green-100 !text-green-700', stickyLeft: true },
        { label: 'Giá trị', value: (item: any) => item.value, width: 100, class: (item: any) => item.value > 2150 ? 'text-orange-600 font-bold' : 'text-green-600', classHeader: '!bg-orange-100 !text-orange-700' },
        { label: 'Loại', value: (item: any) => item.type, width: 100, class: (item: any) => item.type === 'Loại 1' ? 'text-blue-600' : 'text-pink-600', classHeader: '!bg-blue-100 !text-blue-700' },
        { label: 'Ngày', value: (item: any) => item.date, width: 110, class: 'text-gray-700', classHeader: '!bg-gray-100 !text-gray-700' },
        { label: 'Trạng thái', value: (item: any) => item.status, width: 120, class: (item: any) => item.status === 'Hoàn thành' ? 'text-green-700 font-bold' : (item.status === 'Lỗi' ? 'text-red-600 font-bold' : 'text-yellow-700'), classHeader: '!bg-green-100 !text-green-700' },
        { label: 'Ghi chú', value: (item: any) => item.note, width: 160, class: 'italic text-gray-500', classHeader: '!bg-gray-50 !text-gray-500' },
      ],
      pagination: { showPagination: false },
      scroll: { x: 900, y: 500 }
    }
  ]));

  ngOnInit(): void { }
}
