
import { Pipe, PipeTransform } from '@angular/core';
import { IMsgTableConfig } from '../interfaces';

@Pipe({
  name: 'msgTableRowStyle',
  standalone: true
})
export class MsgTableRowStylePipe implements PipeTransform {
  transform<T>(value: IMsgTableConfig['rowStyle'] | IMsgTableConfig['rowClass'], item: T, index: number, data: T[]): string {
    if (!value) {
      return '';
    }
    return typeof value === 'string' ? value : value(item, index, data);
  }
}
