<div class="msg-tabs flex flex-col h-full select-none"
  [class.msg-tabs-vertical]="msgTabType() === 'vertical'"
  [class.msg-tabs-horizontal]="msgTabType() === 'horizontal'"
  [class.msg-tabs-border]="msgTabStyle() === 'border'">
  <ul #tabsContainer
    class="msg-tabs-nav shrink-0">
    @for (tab of computedTabs(); track tab.key) {
      <li class="msg-tab-item"
        [class.active]="tab.active"
        [class.disabled]="tab.disabled"
        [class.hovered]="hoveredTabIndex() === $index"
        (click)="handleTabClick(tab, $index)"
        (mouseenter)="handleTabHover($index)"
        (mouseleave)="handleTabLeave()">
        @if (tab.icon) {
          <msg-icon-shared [msgName]="tab.icon"
            [msgSize]="16"
            [msgStroke]="2" />
        }
        <span class="msg-tab-label">{{ tab.name }}</span>
        @if (tab.countBadge) {
          <span class="msg-tab-badge">{{ tab.countBadge }}</span>
        }
      </li>
    }
    <div #tabIndicator
      class="msg-tab-indicator"></div>
  </ul>
  <div class="msg-tabs-content">
    <div class="tab-content"
      [class.slide-left]="msgTabType() === 'horizontal' && slideDirection() === 'left'"
      [class.slide-right]="msgTabType() === 'horizontal' && slideDirection() === 'right'"
      [class.slide-top]="msgTabType() === 'vertical' && slideDirection() === 'left'"
      [class.slide-bottom]="msgTabType() === 'vertical' && slideDirection() === 'right'">
      <ng-content />
    </div>
  </div>
</div>
