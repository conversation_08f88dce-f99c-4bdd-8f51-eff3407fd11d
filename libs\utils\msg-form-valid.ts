import { AbstractControl, FormArray, FormGroup } from "@angular/forms";

export const markFormTouchedAndEmit = (control: AbstractControl): void => {
  control.markAsDirty();
  control.markAsTouched();
  control.updateValueAndValidity({ emitEvent: true });

  if (control instanceof FormGroup || control instanceof FormArray) {
    Object.values(control.controls).forEach(childControl => {
      markFormTouchedAndEmit(childControl);
    });
  }
};
