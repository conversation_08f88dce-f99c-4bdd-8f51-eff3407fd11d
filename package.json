{"name": "@angular-workspace/source", "version": "0.0.0", "license": "MIT", "scripts": {"start": "node scripts/smart-runner.js start", "build": "node scripts/smart-runner.js build", "list": "node scripts/smart-runner.js list", "remove": "rm -rf node_modules .husky", "remove:cache": "rm -rf node_modules/.cache .angular/cache", "start:msg-crm": "nx serve msg-crm", "lint": "nx run-many --target=lint --all", "lint:fix": "nx run-many --target=lint --all --fix", "lint:msg-crm": "nx lint msg-crm", "lint:libs": "nx lint @angular-workspace/components @angular-workspace/services @angular-workspace/directives @angular-workspace/pipes", "prepare": "husky", "host": "nx serve msg-crm --host=0.0.0.0 --port=4200 --disable-host-check", "zip": "node scripts/zip-source.js", "zip:dist": "node scripts/zip-source.js --dist"}, "private": true, "dependencies": {"@angular/common": "~19.2.0", "@angular/compiler": "~19.2.0", "@angular/core": "~19.2.0", "@angular/forms": "~19.2.0", "@angular/platform-browser": "~19.2.0", "@angular/platform-browser-dynamic": "~19.2.0", "@angular/router": "~19.2.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "crypto-js": "^4.2.0", "lucide-angular": "^0.513.0", "overlayscrollbars-ngx": "^0.5.2", "rxjs": "~7.8.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "~19.2.0", "@angular-devkit/core": "~19.2.0", "@angular-devkit/schematics": "~19.2.0", "@angular/cli": "~19.2.0", "@angular/compiler-cli": "~19.2.0", "@angular/language-service": "~19.2.0", "@eslint/js": "^9.8.0", "@nx/angular": "21.1.3", "@nx/eslint": "21.1.3", "@nx/eslint-plugin": "21.1.3", "@nx/js": "21.1.3", "@nx/web": "21.1.3", "@nx/workspace": "21.1.3", "@schematics/angular": "~19.2.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@tailwindcss/postcss": "^4.1.8", "@types/crypto-js": "^4.2.2", "@typescript-eslint/utils": "^8.19.0", "angular-eslint": "^19.2.0", "archiver": "^7.0.1", "autoprefixer": "^10.4.0", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-rxjs-x": "^0.7.5", "husky": "^9.1.7", "lint-staged": "^16.1.0", "ng-zorro-antd": "^19.3.1", "nx": "21.1.3", "postcss": "^8.5.4", "prettier": "^2.6.2", "tailwindcss": "^4.1.8", "tslib": "^2.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0"}, "lint-staged": {"*.{ts,js,html}": ["eslint --fix"]}}