import { Component, computed, input } from "@angular/core";
import { TranslatePipe } from "@ngx-translate/core";
import { IMsgSpinnerDto } from "./interfaces/msg-spinner.interface";

@Component({
  selector: 'msg-spinner-shared',
  templateUrl: './msg-spinner.component.html',
  styleUrls: ['./msg-spinner.component.scss'],
  standalone: true,
  imports: [
    TranslatePipe
  ]
})

export class MsgSpinnerSharedComponent {
  public msgSize = input<IMsgSpinnerDto['size']>('medium');
  public msgShowMessage = input<boolean>(true);
  public msgMessage = input<string>('i18n_loading_data');
  public msgLoading = input<boolean>(true);
  public msgClass = input<string>('');

  public spinnerClasses = computed(() => {
    const baseClass = 'msg-spinner-loader';
    const sizeClass = `msg-spinner-${this.msgSize()}`;
    return `${baseClass} ${sizeClass}`;
  });

  public containerClasses = computed(() => {
    const baseClass = 'msg-spinner';
    const hiddenClass = this.msgLoading() ? '' : 'msg-spinner-hidden';
    const customClass = this.msgClass();
    return `${baseClass} ${hiddenClass} ${customClass}`.trim();
  });

  public shouldShowMessage = computed(() =>
    this.msgLoading() && this.msgMessage()
  );
}
