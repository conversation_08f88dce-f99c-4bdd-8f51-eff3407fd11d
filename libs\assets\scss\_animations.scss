@mixin transition($property: all, $duration: var(--ease-duration, 0.3s), $easing: var(--ease-in-out)) {
  transition: $property $duration $easing;
}

@mixin fade-in($duration: var(--ease-duration, 0.3s), $easing: var(--ease-out)) {
  opacity: 0;
  @include transition(opacity, $duration, $easing);

  &.active,
  &.show,
  &:hover {
    opacity: 1;
  }
}

@mixin fade-out($duration: var(--ease-duration, 0.3s), $easing: var(--ease-out)) {
  opacity: 1;
  @include transition(opacity, $duration, $easing);

  &.hide,
  &.inactive {
    opacity: 0;
  }
}

@mixin scale-hover($scale: 1.05, $duration: var(--ease-duration, 0.3s), $easing: var(--ease-in-out)) {
  @include transition(transform, $duration, $easing);

  &:hover {
    transform: scale($scale);
  }
}

@mixin scale-active($scale: 0.95, $duration: var(--ease-duration-fast, 0.15s), $easing: var(--ease-in-out)) {
  @include transition(transform, $duration, $easing);

  &:active {
    transform: scale($scale);
  }
}

@mixin slide-in-y($distance: 20px, $duration: var(--ease-duration, 0.3s), $easing: var(--ease-out)) {
  transform: translateY($distance);
  opacity: 0;
  @include transition(all, $duration, $easing);

  &.active,
  &.show {
    transform: translateY(0);
    opacity: 1;
  }
}

@mixin rotate($degrees: 90deg, $duration: var(--ease-duration, 0.3s), $easing: var(--ease-in-out)) {
  @include transition(transform, $duration, $easing);

  &.rotated,
  &.active {
    transform: rotate($degrees);
  }
}

@mixin grid-expand($duration: var(--ease-duration, 0.1s), $easing: var(--ease-in-out)) {
  display: grid;
  grid-template-rows: 0fr;
  @include transition(grid-template-rows, $duration, $easing);
  overflow: hidden;

  >* {
    min-height: 0;
    overflow: hidden;
  }

  &.expanded,
  &.active {
    grid-template-rows: 1fr;
  }
}

@mixin slide-from($direction: 'right', $distance: 100%, $duration: var(--ease-duration, 0.3s), $easing: var(--ease-out)) {
  @if $direction =='right' {
    transform: translateX($distance) scale(0.95);
  }

  @else if $direction =='left' {
    transform: translateX(-$distance) scale(0.95);
  }

  @else if $direction =='top' {
    transform: translateY(-$distance) scale(0.95);
  }

  @else if $direction =='bottom' {
    transform: translateY($distance) scale(0.95);
  }

  opacity: 0;
  animation: slide-from-#{$direction} $duration cubic-bezier(0.16, 1, 0.3, 1);
}

@mixin slide-to($direction: 'right', $distance: 100%, $duration: var(--ease-duration-fast, 0.15s), $easing: var(--ease-in)) {
  &.hide {
    animation: slide-to-#{$direction} $duration cubic-bezier(0.4, 0, 0.6, 1) forwards;
  }
}

.msg-transition {
  @include transition();

  &-fast {
    @include transition(all, var(--ease-duration-fast, 0.15s));
  }

  &-slow {
    @include transition(all, var(--ease-duration-slow, 0.75s));
  }
}

.msg-fade {
  @include fade-in();

  &-out {
    @include fade-out();
  }
}

.msg-scale {
  &-hover {
    @include scale-hover();
  }

  &-active {
    @include scale-active();
  }
}

.msg-slide {
  &-in-up {
    @include slide-in-y(-20px);
  }

  &-in-down {
    @include slide-in-y(20px);
  }
}

.msg-rotate {
  @include rotate();

  &-180 {
    @include rotate(180deg);
  }
}

.msg-expand {
  @include grid-expand();
}

// Utility states for components
.expanded {
  grid-template-rows: 1fr;
}

.rotated {
  transform: rotate(90deg);
}

// Essential Keyframes - chỉ những animation thực sự cần thiết
@keyframes msg-slide-from-right {
  0% {
    transform: translateX(100%) scale(0.95);
    opacity: 0;
  }

  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes msg-slide-to-right {
  0% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }

  100% {
    transform: translateX(100%) scale(0.95);
    opacity: 0;
  }
}

@keyframes msg-slide-from-left {
  0% {
    transform: translateX(-100%) scale(0.95);
    opacity: 0;
  }

  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes msg-slide-to-left {
  0% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }

  100% {
    transform: translateX(-100%) scale(0.95);
    opacity: 0;
  }
}

@keyframes msg-slide-from-top {
  0% {
    transform: translateY(-100%) scale(0.95);
    opacity: 0;
  }

  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes msg-slide-to-top {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }

  100% {
    transform: translateY(-100%) scale(0.95);
    opacity: 0;
  }
}

@keyframes msg-slide-from-bottom {
  0% {
    transform: translateY(100%) scale(0.95);
    opacity: 0;
  }

  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes msg-slide-to-bottom {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }

  100% {
    transform: translateY(100%) scale(0.95);
    opacity: 0;
  }
}

@keyframes msg-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes msg-pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

@keyframes msg-shake {

  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-5px);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(5px);
  }
}

.msg-animate {
  &-spin {
    will-change: transform;
    animation: msg-spin 1s linear infinite;
  }

  &-pulse {
    animation: msg-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  &-shake {
    animation: msg-shake 0.5s ease-in-out;
  }

  &-slide-from-right {
    animation: msg-slide-from-right 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  }

  &-slide-to-right {
    animation: msg-slide-to-right 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
  }

  &-slide-from-left {
    animation: msg-slide-from-left 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  }

  &-slide-to-left {
    animation: msg-slide-to-left 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
  }

  &-slide-from-top {
    animation: msg-slide-from-top 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  }

  &-slide-to-top {
    animation: msg-slide-to-top 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
  }

  &-slide-from-bottom {
    animation: msg-slide-from-bottom 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  }

  &-slide-to-bottom {
    animation: msg-slide-to-bottom 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
  }
}
