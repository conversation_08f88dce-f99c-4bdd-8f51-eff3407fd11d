import { Directive, inject, input, signal } from "@angular/core";
import { <PERSON><PERSON>tractControl, FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { TYPE_FORM } from "../../interfaces/form.interface";
import { MsgNotificationService } from "../../services/msg-notification.service";
import { MsgTranslateService } from "../../services/msg-translate.service";
import { markFormTouchedAndEmit } from "../../utils";
import { IMsgLoginDto } from "./interfaces/msg-login.interface";

@Directive({})
export class MsgLoginAbstractComponent {
  public msgLogo = input<string>('/assets/images/msg-logo-msg-connect.png');
  public msgTitle = input<string>('i18n_msg_connect');
  public msgDescription = input<string>('i18n_msg_description');
  protected msgShowPassword = signal<boolean>(false);
  protected msgLoading = signal<boolean>(false);

  protected msgFormLogin!: FormGroup<TYPE_FORM<IMsgLoginDto>>;
  protected fb = inject(FormBuilder);
  protected router = inject(Router);
  protected notificationService = inject(MsgNotificationService);
  protected translate = inject(MsgTranslateService);

  constructor() {
    this.initForm();
  }

  protected initForm(): void {
    this.msgFormLogin = this.fb.group({
      username: ['', [Validators.required, this._emailValidator]],
      password: ['', [Validators.required]],
      remember: [false]
    });

  }

  private _emailValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@matsaigon\.com$/;
    if (control.value && !emailRegex.test(control.value)) {
      return { 'validMailMsg': true };
    }

    return null;
  }

  protected toggleShowPassword(event: MouseEvent): void {
    event.preventDefault();
    this.msgShowPassword.set(!this.msgShowPassword());
  }

  protected handlerLogin(): void {
    markFormTouchedAndEmit(this.msgFormLogin);
    if (this.msgFormLogin.invalid) {
      return;
    }

    this.msgLoading.set(true);
    setTimeout(() => {
      this.notificationService.success(this.translate.instant('i18n_login_success'));
      this.router.navigateByUrl('/dashboard');
      this.msgLoading.set(false);
    }, 2000);
  }

  protected handlerForgotPassword(): void {
    if (this.msgLoading()) {
      return;
    }
    this.router.navigateByUrl('/reset-password');
  }
}
