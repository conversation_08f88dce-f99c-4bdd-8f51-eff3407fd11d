import { AsyncPipe } from "@angular/common";
import { ChangeDetectionStrategy, Component, input } from "@angular/core";
import { TranslatePipe } from "@ngx-translate/core";
import { NzToolTipModule } from "ng-zorro-antd/tooltip";
import { MsgTruncatePipe } from "../../pipes/msg-truncate.pipe";

@Component({
  selector: "msg-header-title-shared",
  templateUrl: "./msg-header-title.component.html",
  styleUrls: ["./msg-header-title.component.scss"],
  standalone: true,
  imports: [
    TranslatePipe,
    NzToolTipModule,
    MsgTruncatePipe,
    AsyncPipe
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})

export class MsgHeaderTitleSharedComponent {
  public msgTitle = input<string>('');
  public msgClass = input<string>('');
}
