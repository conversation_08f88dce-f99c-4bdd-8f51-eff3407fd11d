import { ComponentRef, inject, Injectable, Injector } from '@angular/core';
import { IMsgModalOptionsDto, IMsgModalServiceControl } from '../components/msg-modal/interfaces/msg-modal.interface';
import { MsgModalSharedComponent } from '../components/msg-modal/msg-modal.component';
import { MsgDynamicComponentService } from './msg-dynamic-component.service';

@Injectable({
  providedIn: 'root'
})
export class MsgModalService {
  private _dynamicComponentService = inject(MsgDynamicComponentService);
  private _injector = inject(Injector);

  public info(options: Omit<IMsgModalOptionsDto, 'type'>): IMsgModalServiceControl {
    return this._createModal({ ...options, type: 'info' });
  }

  public success(options: Omit<IMsgModalOptionsDto, 'type'>): IMsgModalServiceControl {
    return this._createModal({ ...options, type: 'success' });
  }

  public warn(options: Omit<IMsgModalOptionsDto, 'type'>): IMsgModalServiceControl {
    return this._createModal({ ...options, type: 'warn' });
  }

  public danger(options: Omit<IMsgModalOptionsDto, 'type'>): IMsgModalServiceControl {
    return this._createModal({ ...options, type: 'danger' });
  }

  public show(options: IMsgModalOptionsDto): IMsgModalServiceControl {
    return this._createModal(options);
  }

  private _createModal(options: IMsgModalOptionsDto): IMsgModalServiceControl {
    const injector = Injector.create({
      providers: [{ provide: 'MODAL_OPTIONS', useValue: options }],
      parent: this._injector
    });

    const modalRef = this._dynamicComponentService.addToBody(MsgModalSharedComponent, injector);
    const instance = modalRef.instance;

    modalRef.setInput('msgType', options.type);
    modalRef.setInput('msgTitle', options.title || 'i18n_msg_modal_title');
    modalRef.setInput('msgContent', options.content || 'i18n_msg_modal_content');
    modalRef.setInput('msgTextOk', options.textOk || 'i18n_msg_modal_apply');
    modalRef.setInput('msgTextCancel', options.textCancel || 'i18n_msg_modal_cancel');
    modalRef.setInput('msgShowOk', options.showOk !== undefined ? options.showOk : true);
    modalRef.setInput('msgShowCancel', options.showCancel !== undefined ? options.showCancel : true);
    modalRef.setInput('msgCloseOnOverlayClick', options.closeOnOverlayClick !== undefined ? options.closeOnOverlayClick : true);
    modalRef.setInput('msgClassContainer', options.classContainer || 'msg-modal');

    const sub = instance.msgAfterClose.subscribe(() => {
      this._dynamicComponentService.removeComponent(modalRef);
      modalRef.destroy();
      sub.unsubscribe();
    });

    modalRef.changeDetectorRef.detectChanges();
    return {
      show: () => instance.show(),
      remove: (compRef?: ComponentRef<MsgModalSharedComponent>) => instance.remove(compRef),
      setLoading: (loading: boolean) => instance.setLoading(loading),
      instance,
    };
  }
}
