import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MsgButtonSharedComponent } from '../../msg-button/msg-button.component';
import { MsgIconSharedComponent } from '../../msg-icon/msg-icon.component';
import { MsgLoginAbstractComponent } from '../msg-login-abstract.component';
@Component({
  selector: 'msg-login-template-three-shared',
  templateUrl: './msg-template-three.component.html',
  styleUrls: ['./msg-template-three.component.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    MsgIconSharedComponent,
    MsgButtonSharedComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class MsgLoginTemplateThreeSharedComponent extends MsgLoginAbstractComponent {
  constructor() {
    super();
    this.initForm();
  }
}
