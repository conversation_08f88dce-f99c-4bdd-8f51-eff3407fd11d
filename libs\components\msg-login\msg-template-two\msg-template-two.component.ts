import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MsgButtonSharedComponent } from '../../msg-button/msg-button.component';
import { MsgIconSharedComponent } from '../../msg-icon/msg-icon.component';
import { MsgLoginAbstractComponent } from '../msg-login-abstract.component';
@Component({
  selector: 'msg-login-template-two-shared',
  templateUrl: './msg-template-two.component.html',
  styleUrls: ['./msg-template-two.component.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MsgIconSharedComponent,
    MsgButtonSharedComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class MsgLoginTemplateTwoSharedComponent extends MsgLoginAbstractComponent {
  constructor() {
    super();
    this.initForm();
  }
}
