#!/usr/bin/env node

const fs = require('fs');
const { colors, log } = require('./utils/console-utils');
const commitMsgFilePath = process.argv[2];
if (!commitMsgFilePath) {
  log.error('Commit message file path not provided');
  process.exit(1);
}

let commitMessage;
try {
  commitMessage = fs.readFileSync(commitMsgFilePath, 'utf8').trim();
} catch (error) {
  log.error(`Could not read commit message file: ${error.message}`);
  process.exit(1);
}

const commitRegex =
  /^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert|init)(\(.+\))?: .{1,100}/;

if (!commitRegex.test(commitMessage)) {
  console.log(
    `${colors.red}${colors.bright}✗ Commit message format không hợp lệ!${colors.reset}`
  );
  log.spacer();

  console.log(`${colors.blue}${colors.bright}▶ Format đúng:${colors.reset}`);
  console.log(
    `${colors.white}<type>[optional scope]: <description>${colors.reset}`
  );
  log.spacer();

  console.log(
    `${colors.blue}${colors.bright}▶ Các loại commit được phép:${colors.reset}`
  );
  console.log(`${colors.green}- feat:${colors.reset} Tính năng mới`);
  console.log(`${colors.green}- fix:${colors.reset} Sửa lỗi`);
  console.log(
    `${colors.green}- docs:${colors.reset} Chỉ thay đổi documentation`
  );
  console.log(
    `${colors.green}- style:${colors.reset} Thay đổi không ảnh hưởng đến ý nghĩa code (whitespace, formatting, etc)`
  );
  console.log(
    `${colors.green}- refactor:${colors.reset} Code change không phải bug fix hay thêm feature`
  );
  console.log(`${colors.green}- test:${colors.reset} Thêm hoặc sửa tests`);
  console.log(
    `${colors.green}- chore:${colors.reset} Thay đổi build process hoặc auxiliary tools`
  );
  console.log(
    `${colors.green}- perf:${colors.reset} Code change cải thiện performance`
  );
  console.log(
    `${colors.green}- ci:${colors.reset} Thay đổi CI configuration files và scripts`
  );
  console.log(
    `${colors.green}- build:${colors.reset} Thay đổi ảnh hưởng đến build system hoặc external dependencies`
  );
  console.log(`${colors.green}- revert:${colors.reset} Revert commit trước đó`);
  console.log(`${colors.green}- init:${colors.reset} Commit đầu tiên`);
  log.spacer();

  console.log(`${colors.cyan}${colors.bright}✓ Ví dụ hợp lệ:${colors.reset}`);
  console.log(`${colors.gray}- feat: add user authentication`);
  console.log(`${colors.gray}- fix: resolve login validation issue`);
  console.log(`${colors.gray}- chore(deps): update Angular to v19`);
  console.log(
    `${colors.gray}- docs: update README with installation steps${colors.reset}`
  );
  log.spacer();

  console.log(
    `${colors.red}${colors.bright}✗ Commit message của bạn:${colors.reset} "${colors.yellow}${commitMessage}${colors.reset}"`
  );
  process.exit(1);
}

log.success('Commit message format hợp lệ!');
process.exit(0);
