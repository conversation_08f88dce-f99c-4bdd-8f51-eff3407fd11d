import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { TYPE_MSG_ICON_NAME, TYPE_MSG_ICON_SIZE, TYPE_MSG_ICON_SIZE_INPUT } from './interfaces/msg-icon.interface';
import { MsgIconModule } from './msg-icon.module';

@Component({
  selector: 'msg-icon-shared',
  templateUrl: './msg-icon.component.html',
  styleUrls: ['./msg-icon.component.scss'],
  standalone: true,
  imports: [MsgIconModule],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class MsgIconSharedComponent {
  public msgName = input.required<TYPE_MSG_ICON_NAME>();
  public msgClass = input<string>('');
  public msgStroke = input<number>(2);
  public msgSize = input<TYPE_MSG_ICON_SIZE, TYPE_MSG_ICON_SIZE_INPUT>(16, {
    transform: (value: TYPE_MSG_ICON_SIZE_INPUT): TYPE_MSG_ICON_SIZE => {
      return typeof value === 'string' ? parseInt(value, 10) as TYPE_MSG_ICON_SIZE : value;
    }
  });
}
