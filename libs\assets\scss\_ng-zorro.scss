$primary: var(--color-green-600);
$primary-100: var(--color-green-100);

:root {
  --ant-primary-color: $primary !important;
  --antd-wave-shadow-color: $primary !important;
}

.ant-btn-primary,
.ant-btn-primary:active,
.ant-btn-primary:focus,
.ant-btn-primary:hover {
  background-color: $primary !important;
  border-color: $primary !important;
}

.ant-switch-checked {
  background: $primary !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: $primary !important;
  border-color: $primary !important;
}

.ant-radio-checked .ant-radio-inner {
  border-color: $primary !important;
}

.ant-radio-inner:after {
  background-color: $primary !important;
}

/* Table Overrides */
// ::ng-deep .ant-table-tbody>tr>td,
// ::ng-deep .ant-table-thead>tr>th {
//   height: 30px;
//   line-height: 30px;
//   padding-top: 0 !important;
//   padding-bottom: 0 !important;
// }

a {
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

/* Select Overrides */
.ant-select-focused:not(.ant-select-disabled) .ant-select-selector,
.ant-select-focused:not(.ant-select-disabled) .ant-select-selector:hover {
  border-color: $primary !important;
  box-shadow: 0 0 0 2px $primary-100 !important;
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: $primary-100 !important;
}

.ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: $primary !important;
}

/* Date Picker Overrides */
nz-date-picker,
nz-select-search {
  height: 32px !important;

  input {
    font-size: 13px !important;
    font-weight: 400 !important;
  }
}

.ant-picker-header-view button:hover {
  color: $primary !important;
}

nz-select>nz-select-top-control>nz-select-item,
nz-select>nz-select-top-control {
  font-size: 13px !important;
  font-weight: 400 !important;
}

nz-date-picker>div>input::placeholder {
  font-size: 13px !important;
  font-weight: 400 !important;
}

.ant-picker-focused {
  box-shadow: 0 0 0 2px $primary-100 !important;
}

.ant-picker-focused,
.ant-picker:hover {
  border-color: $primary !important;
}

.ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
  border-color: $primary !important;
}

.ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner,
.ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
.ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
  background: $primary !important;
}

/* Pagination Overrides */
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-link-icon,
.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-link-icon {
  color: $primary !important;
}

.ant-pagination-next:hover .ant-pagination-item-link,
.ant-pagination-prev:hover .ant-pagination-item-link {
  color: $primary !important;
  border-color: $primary !important;
}

.ant-pagination-item {

  &-active,
  &:hover {
    border-color: $primary !important;
  }

  a:hover {
    color: $primary !important;
  }

}

.ant-pagination-item-active {
  a {
    color: $primary !important;

    &:hover {
      color: $primary !important;
    }
  }
}

/* Popover Overrides */
.ant-popover-arrow {
  display: none !important;
}

.msg-popover-content,
.ant-popover {
  padding: 0 !important;

  .ant-popover-inner {
    border-radius: 5px;
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
  }

  .ant-popover-content {
    transform: none !important;
  }
}

.ant-popover-inner-content {
  padding: 0 !important;
}

.msg-popover-content {

  &.ant-popover-placement-top,
  &.ant-popover-placement-topLeft,
  &.ant-popover-placement-topRight {
    margin-bottom: var(--msg-popover-placement-top);
  }

  &.ant-popover-placement-bottom,
  &.ant-popover-placement-bottomLeft,
  &.ant-popover-placement-bottomRight {
    margin-top: var(--msg-popover-placement-bottom);
  }

  &.ant-popover-placement-left,
  &.ant-popover-placement-leftTop,
  &.ant-popover-placement-leftBottom {
    margin-right: var(--msg-popover-placement-left);
  }

  &.ant-popover-placement-right,
  &.ant-popover-placement-rightTop,
  &.ant-popover-placement-rightBottom {
    margin-left: var(--msg-popover-placement-right);
  }

  .ant-popover-inner {
    box-shadow: var(--shadow-uniform-sm) !important
  }
}

/** Overlay Container */
.cdk-overlay-pane>* {
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
}

.cdk-overlay-container {
  z-index: 99999 !important;
}

/** Tooltip Overrides */
.ant-tooltip-inner {
  font-size: 11px;
  line-height: 20px;
}

.ant-input {
  font-size: 12px;
}
