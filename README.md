# Angular Workspace with Smart Runner

<a alt="Nx logo" href="https://nx.dev" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/nrwl/nx/master/images/nx-logo.png" width="45"></a>

✨ Angular monorepo workspace với Nx, ESLint rules nghiêm ngặt và Husky automation ✨

## 🚀 Khởi chạy nhanh

### Lần đầu sử dụng:
```bash
# Xem danh sách projects có thể chạy
npm run dev

# Chạy project cụ thể (tự động check dependencies và setup Husky)
npm run dev <project-name>

# Ví dụ:
npm run dev msg-crm
```

### Scripts chính:
- `npm run dev` - Hiển thị danh sách projects
- `npm run dev <project>` - Chạy project (auto setup dependencies + Husky)
- `npm run start <project>` - <PERSON><PERSON> cho npm run dev
- `npm run setup` - Chỉ setup <PERSON><PERSON> hooks
- `npm run lint` - Lint toàn bộ workspace
- `npm run lint:fix` - Lint và auto fix

## ⚡ Tính năng tự động

Smart Runner sẽ tự động:
1. ✅ Check và cài đặt node_modules nếu chưa có
2. ✅ Setup Husky hooks nếu chưa có
3. ✅ Chạy project được yêu cầu

## 🐶 Husky Git Hooks

### Pre-commit Hook
- Tự động lint các file đã thay đổi
- Format code với Prettier
- Chặn commit nếu có lỗi

### Commit Message Hook
- Bắt buộc format: `<type>: <description>`
- Types: `feat`, `fix`, `chore`, `docs`, `style`, `refactor`, `test`, `perf`, `ci`, `build`, `revert`, `init`

#### Ví dụ commit message hợp lệ:
```bash
git commit -m "feat: add user authentication"
git commit -m "fix(auth): resolve login validation"
git commit -m "chore: update dependencies"
```

## 📋 ESLint Rules

Workspace này có các ESLint rules nghiêm ngặt:
- ✅ Component/Directive/Pipe selector phải có prefix `msg-`
- ✅ Class Component/Service/Pipe/Directive phải có prefix `Msg`
- ✅ Bắt buộc access modifier (`public`, `private`, `protected`)
- ✅ Private members phải có prefix `_`
- ✅ Không được dùng if-else, phải dùng early return
- ✅ Bắt buộc ngoặc nhọn cho if statements
- ✅ Bắt buộc dấu chấm phẩy
- ✅ RxJS best practices với eslint-plugin-rxjs-x

## Run tasks

To run the dev server for your app, use:

```sh
npm run dev msg-crm
# or traditional way:
npx nx serve msg-crm
```

To create a production bundle:

```sh
npx nx build msg-crm
```

To see all available targets to run for a project, run:

```sh
npx nx show project msg-crm
```

These targets are either [inferred automatically](https://nx.dev/concepts/inferred-tasks?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) or defined in the `project.json` or `package.json` files.

[More about running tasks in the docs &raquo;](https://nx.dev/features/run-tasks?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)

## Add new projects

While you could add new projects to your workspace manually, you might want to leverage [Nx plugins](https://nx.dev/concepts/nx-plugins?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) and their [code generation](https://nx.dev/features/generate-code?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) feature.

Use the plugin's generator to create new projects.

To generate a new application, use:

```sh
npx nx g @nx/angular:app demo
```

To generate a new library, use:

```sh
npx nx g @nx/angular:lib mylib
```

You can use `npx nx list` to get a list of installed plugins. Then, run `npx nx list <plugin-name>` to learn about more specific capabilities of a particular plugin. Alternatively, [install Nx Console](https://nx.dev/getting-started/editor-setup?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) to browse plugins and generators in your IDE.

[Learn more about Nx plugins &raquo;](https://nx.dev/concepts/nx-plugins?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects) | [Browse the plugin registry &raquo;](https://nx.dev/plugin-registry?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)


[Learn more about Nx on CI](https://nx.dev/ci/intro/ci-with-nx#ready-get-started-with-your-provider?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)

## Install Nx Console

Nx Console is an editor extension that enriches your developer experience. It lets you run tasks, generate code, and improves code autocompletion in your IDE. It is available for VSCode and IntelliJ.

[Install Nx Console &raquo;](https://nx.dev/getting-started/editor-setup?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)

## Useful links

Learn more:

- [Learn more about this workspace setup](https://nx.dev/getting-started/tutorials/angular-monorepo-tutorial?utm_source=nx_project&amp;utm_medium=readme&amp;utm_campaign=nx_projects)
- [Learn about Nx on CI](https://nx.dev/ci/intro/ci-with-nx?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)
- [Releasing Packages with Nx release](https://nx.dev/features/manage-releases?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)
- [What are Nx plugins?](https://nx.dev/concepts/nx-plugins?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)

And join the Nx community:
- [Discord](https://go.nx.dev/community)
- [Follow us on X](https://twitter.com/nxdevtools) or [LinkedIn](https://www.linkedin.com/company/nrwl)
- [Our Youtube channel](https://www.youtube.com/@nxdevtools)
- [Our blog](https://nx.dev/blog?utm_source=nx_project&utm_medium=readme&utm_campaign=nx_projects)
