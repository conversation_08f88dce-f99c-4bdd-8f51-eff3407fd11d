import { ComponentRef, inject, Injectable } from '@angular/core';
import { IMsgNotificationDto, TYPE_NOTIFICATION } from '../components/msg-notification/interfaces/msg-notification.interface';
import { MsgNotificationSharedComponent } from '../components/msg-notification/msg-notification.component';
import { MSG_NOTIFICATION_CONFIG } from '../providers/msg-notification.provider';
import { uuid } from '../utils/msg-common.util';
import { MsgDynamicComponentService } from './msg-dynamic-component.service';

@Injectable({
  providedIn: 'root'
})
export class MsgNotificationService {
  private _dynamicService = inject(MsgDynamicComponentService);
  private _config = inject(MSG_NOTIFICATION_CONFIG);
  private _notificationComponents: ComponentRef<MsgNotificationSharedComponent>[] = [];
  private _containerElement: HTMLElement | null = null;

  public info(message: string, duration?: number): void {
    this._show('info', message, duration);
  }

  public warn(message: string, duration?: number): void {
    this._show('warning', message, duration);
  }

  public danger(message: string, duration?: number): void {
    this._show('danger', message, duration);
  }

  public success(message: string, duration?: number): void {
    this._show('success', message, duration);
  }

  private _ensureContainer(position?: string): HTMLElement {
    const finalPosition = position || this._config.position || 'top-right';
    if (!this._containerElement) {
      this._containerElement = document.createElement('div');
      document.body.appendChild(this._containerElement);
    }

    this._applyPositionClasses(this._containerElement, finalPosition);
    return this._containerElement;
  }

  private _applyPositionClasses(element: HTMLElement, position: string): void {
    const baseClasses = 'fixed z-[9999] pointer-events-none flex flex-col gap-2';
    let positionClasses = '';

    switch (position) {
      case 'top-left':
        positionClasses = 'top-5 left-5 items-start';
        break;
      case 'top-right':
        positionClasses = 'top-5 right-5 items-end';
        break;
      case 'top-center':
        positionClasses = 'top-5 left-1/2 -translate-x-1/2 items-center';
        break;
      case 'bottom-left':
        positionClasses = 'bottom-5 left-5 items-start';
        break;
      case 'bottom-right':
        positionClasses = 'bottom-5 right-5 items-end';
        break;
      case 'bottom-center':
        positionClasses = 'bottom-5 left-1/2 -translate-x-1/2 items-center';
        break;
      default:
        positionClasses = 'top-5 right-5 items-end';
    }

    element.className = `${baseClasses} ${positionClasses}`;
  }

  private _removeNotification(componentRef: ComponentRef<MsgNotificationSharedComponent>): void {
    const index = this._notificationComponents.indexOf(componentRef);
    if (index > -1) {
      this._notificationComponents.splice(index, 1);
      this._dynamicService.removeComponent(componentRef);
    }

    if (this._notificationComponents.length === 0 && this._containerElement) {
      document.body.removeChild(this._containerElement);
      this._containerElement = null;
    }
  }

  private _isDuplicate(newData: IMsgNotificationDto): boolean {
    if (!this._config.preventDuplicate) {
      return false;
    }

    return this._notificationComponents.some(comp => {
      const existingData = comp.instance.notificationData();
      return existingData.message === newData.message &&
        existingData.type === newData.type;
    });
  }

  private _show(type: TYPE_NOTIFICATION, message: string, duration?: number): void {
    const data: IMsgNotificationDto = {
      id: uuid(),
      type,
      message,
      duration: duration !== undefined ? duration : this._config.duration
    };

    if (this._isDuplicate(data)) {
      return;
    }

    const finalPosition = this._config.position || 'bottom-right';
    if (this._notificationComponents.length >= (this._config.maxVisible || 6)) {
      let oldestComponent: ComponentRef<MsgNotificationSharedComponent> | undefined;

      if (finalPosition.includes('bottom')) {
        oldestComponent = this._notificationComponents.shift();
      }
      if (finalPosition.includes('top')) {
        oldestComponent = this._notificationComponents.pop();
      }
      if (oldestComponent) {
        this._dynamicService.removeComponent(oldestComponent);
      }
    }

    const container = this._ensureContainer(finalPosition);
    const componentRef = this._dynamicService.addToElement(MsgNotificationSharedComponent, container);

    componentRef.setInput('notificationData', data);
    componentRef.setInput('msgOnRemove', () => this._removeNotification(componentRef));
    componentRef.setInput('msgPosition', finalPosition);
    componentRef.setInput('msgShowProcessBar', this._config.showProgressBar ?? true);

    if (finalPosition.includes('bottom')) {
      container.appendChild(componentRef.location.nativeElement);
      this._notificationComponents.push(componentRef);
      return;
    }

    container.insertBefore(componentRef.location.nativeElement, container.firstChild);
    this._notificationComponents.unshift(componentRef);
  }
}
