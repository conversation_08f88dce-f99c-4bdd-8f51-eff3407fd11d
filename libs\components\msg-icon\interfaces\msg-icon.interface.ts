import { TYPE_ARRAY_ELEMENT, TYPE_STRINGIFY_UNION } from "../../../interfaces/common.interface";
import { MSG_ICONS } from "../msg-icon.module";

export const MSG_ICON_SIZES = [14, 16, 18, 20, 22, 24, 32, 48, 64, 128] as const;
export type TYPE_MSG_ICON_NAME = keyof typeof MSG_ICONS;
export type TYPE_MSG_ICON_SIZE = TYPE_ARRAY_ELEMENT<typeof MSG_ICON_SIZES>;
export type TYPE_MSG_ICON_SIZE_INPUT = TYPE_MSG_ICON_SIZE | TYPE_STRINGIFY_UNION<TYPE_MSG_ICON_SIZE>;
