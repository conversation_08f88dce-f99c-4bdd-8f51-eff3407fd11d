import nx from '@nx/eslint-plugin';
import angular from 'angular-eslint';
import rxjs from 'eslint-plugin-rxjs-x';

export default [
  ...nx.configs['flat/base'],
  ...nx.configs['flat/typescript'],
  ...nx.configs['flat/javascript'],
  ...angular.configs.tsRecommended,
  {
    ignores: ['**/dist'],
  },
  {
    files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
    plugins: {
      'rxjs-x': rxjs
    },
    languageOptions: {
      parserOptions: {
        project: './tsconfig.base.json',
        tsconfigRootDir: import.meta.dirname,
      },
    },
    rules: {
      '@nx/enforce-module-boundaries': [
        'error',
        {
          enforceBuildableLibDependency: true,
          allow: ['^.*/eslint(\\.base)?\\.config\\.[cm]?js$'],
          depConstraints: [
            {
              sourceTag: '*',
              onlyDependOnLibsWithTags: ['*'],
            },
          ],
        },
      ],
      "@typescript-eslint/no-unused-vars": ["warn", {
        "vars": "all",
        "args": "after-used",
        "ignoreRestSiblings": false,
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_"
      }],
      "no-unused-vars": ["warn", {
        "vars": "all",
        "args": "after-used",
        "caughtErrors": "all",
        "ignoreRestSiblings": false,
        "reportUsedIgnorePattern": false
      }],
      "no-empty-function": "off",
      "no-prototype-builtins": "off",
      "@typescript-eslint/no-empty-function": "off",
      "no-unused-expressions": "off",
      "@typescript-eslint/no-unused-expressions": "off",
      "@angular-eslint/no-empty-lifecycle-method": ["off"],
      "@angular-eslint/pipe-prefix": [
        "error",
        {
          "prefixes": [
            "msg"
          ]
        }
      ],
      '@angular-eslint/directive-selector': [
        'error',
        {
          type: 'attribute',
          prefix: 'msg',
          style: 'kebab-case',
        },
      ],
      '@angular-eslint/component-selector': [
        'error',
        {
          type: 'element',
          prefix: 'msg',
          style: 'kebab-case',
        },
      ],
      // Rule 2: Bắt buộc access modifier cho class members
      '@typescript-eslint/explicit-member-accessibility': [
        'error',
        {
          accessibility: 'explicit',
          overrides: {
            accessors: 'explicit',
            constructors: 'off', // Skip constructor
            methods: 'explicit',
            properties: 'explicit',
            parameterProperties: 'explicit'
          },
          ignoredMethodNames: [
            // Angular lifecycle methods
            "transform",
            'ngOnInit',
            'ngOnDestroy',
            'ngOnChanges',
            'ngAfterViewInit',
            'ngAfterViewChecked',
            'ngAfterContentInit',
            'ngAfterContentChecked',
            'ngDoCheck'
          ]
        }
      ],
      // Rule 2: Private members phải có tiền tố '_'
      '@typescript-eslint/naming-convention': [
        'error',
        {
          selector: 'memberLike',
          modifiers: ['private'],
          format: ['camelCase', 'UPPER_CASE'],
          leadingUnderscore: 'require'
        },
        {
          selector: 'memberLike',
          modifiers: ['protected', 'public'],
          format: ['camelCase', 'UPPER_CASE'],
          leadingUnderscore: 'forbid'
        },
        // Rule: Class naming cho Angular Components/Directives/Pipes/Services
        {
          selector: 'class',
          filter: {
            regex: '.*(Component|Directive|Pipe|Service)$',
            match: true
          },
          format: ['PascalCase'],
          prefix: ['Msg']
        }
      ],
      // Rule 4: Không được dùng if-else, phải dùng early return
      'no-else-return': ['error', { allowElseIf: false }],
      // Rule 5: Bắt buộc có ngoặc nhọn cho if statement
      'curly': ['error', 'all'],
      // Rule 6: Bắt buộc có dấu chấm phẩy
      'semi': ['error', 'always'],

      // === RxJS Rules ===
      // Tránh việc subscribe nested (callback hell)
      'rxjs-x/no-nested-subscribe': 'error',

      // Tránh dùng async/await với Observable
      'rxjs-x/no-async-subscribe': 'warn',

      // Tránh subscribe trong subscribe (turned off - allowing handlers in subscribe)
      'rxjs-x/no-subscribe-handlers': 'off',

      // Bắt buộc xử lý error trong subscribe
      'rxjs-x/no-ignored-error': 'off',

      // Bắt buộc import operators từ đúng path
      'rxjs-x/no-internal': 'error',

      // Tránh exposed subjects trực tiếp
      'rxjs-x/no-exposed-subjects': 'warn',
    },
  },
  {
    files: [
      '**/*.ts',
      '**/*.tsx',
      '**/*.cts',
      '**/*.mts',
      '**/*.js',
      '**/*.jsx',
      '**/*.cjs',
      '**/*.mjs',
    ],
    // Override or add rules here
    rules: {},
  },
];
