@use 'responsive' as *;

.msg-alert-version {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);

  &-enter {
    animation: msgAlertSlideIn 0.4s forwards;
  }

  &-leave {
    animation: msgAlertSlideOut 0.4s forwards;
  }

  @include respond-to-max('mobile') {
    max-width: 320px;
    min-width: 320px;
  }

}

@keyframes msgAlertSlideIn {
  from {
    transform: translateX(500px);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes msgAlertSlideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(500px);
    opacity: 0;
  }
}
