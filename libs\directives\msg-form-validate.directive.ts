import { AfterViewInit, ComponentRef, Directive, ElementRef, inject, <PERSON><PERSON><PERSON><PERSON>, Renderer2, ViewContainerRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { MsgValidateSharedComponent } from '../components/msg-validate/msg-validate.component';

@Directive({
  selector: '[msg-form-validate], [msgFormValidate]',
  standalone: true
})
export class MsgFormValidateDirective implements AfterViewInit, OnDestroy {
  private _ngControl = inject(NgControl);
  private _el = inject(ElementRef);
  private _renderer = inject(Renderer2);
  private _viewContainer = inject(ViewContainerRef);
  private _componentRef: ComponentRef<MsgValidateSharedComponent> | null = null;

  constructor() { }

  ngAfterViewInit() {
    this._createValidationComponent();
  }

  private _createValidationComponent(): void {
    if (!this._ngControl?.control) {
      return;
    }

    this._componentRef = this._viewContainer.createComponent(MsgValidateSharedComponent);
    this._componentRef.setInput('control', this._ngControl.control);
    const parentElement = this._renderer.parentNode(this._el.nativeElement);
    if (parentElement) {
      this._renderer.appendChild(parentElement, this._componentRef.location.nativeElement);
    }
  }

  ngOnDestroy(): void {
    if (this._componentRef) {
      this._componentRef.destroy();
      this._componentRef = null;
    }
  }
}
