import { Component, inject, signal } from '@angular/core';
import { Router } from '@angular/router';
import { MsgBellNotificationsSharedComponent } from '@libs/components/msg-bell-notifications';
import { IMsgBellNotificationsDto } from '@libs/components/msg-bell-notifications/interfaces';
import { MsgIconSharedComponent } from '@libs/components/msg-icon';
import { MsgTranslateService } from '@libs/services';
import { TranslatePipe } from '@ngx-translate/core';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { timer } from 'rxjs';
import { RoutesConstant } from '../../../shared/constants/routes.define';

@Component({
  selector: 'msg-main-header',
  standalone: true,
  imports: [
    NzAvatarModule,
    NzMenuModule,
    NzPopoverDirective,
    NzDividerModule,
    MsgIconSharedComponent,
    MsgBellNotificationsSharedComponent,
    NzSelectModule,
    TranslatePipe
  ],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class MsgHeaderComponent {
  protected msgLogo = signal<string>('/assets/images/msg-logo-msg-connect.png');
  protected isPopoverVisible = signal<boolean>(false);
  protected currentLanguage = signal<string>('vi');
  protected languages = signal<Array<{ label: string; value: string }>>([
    { label: 'i18n_language_vietnamese', value: 'vi' },
    { label: 'i18n_language_english', value: 'en' }
  ]);
  protected msgLoading = signal<boolean>(true);
  protected msgListNotifications = signal<Array<IMsgBellNotificationsDto>>([]);

  protected isLanguageSelectOpen = signal<boolean>(false);
  private _router = inject(Router);
  private _translateService = inject(MsgTranslateService);

  protected handlerTogglePopover(event: MouseEvent): void {
    event.stopPropagation();
    this.isPopoverVisible.update(value => !value);
  }

  protected handleVisibleChange(visible: boolean): void {
    if (!visible) {
      this.isPopoverVisible.set(false);
    }
  }

  protected handlerLogOut(event: MouseEvent): void {
    event.stopPropagation();
    this._router.navigate([RoutesConstant.LOGIN]).then(() => {
      // Do Something after logout
    });
  }

  protected handlerChangeLanguage(event: string): void {
    this._translateService.changeLanguage(event);
    this.currentLanguage.set(event);
  }

  protected handlerFetchNotifications(): void {
    this.msgLoading.set(true);
    timer(500).subscribe(() => {
      const randomNotiTitle = [
        'New order received',
        'Payment successful',
        'New message received',
        'New comment received',
        'New task assigned',
        'New event scheduled',
        'New user registered',
      ];
      const notifications = Array.from({ length: 10 }).map((_, index) => ({
        id: index.toString(),
        title: randomNotiTitle[index % randomNotiTitle.length],
        message: `Message ${index}`,
        createdAt: new Date(),
      }));
      this.msgListNotifications.set(notifications);
      this.msgLoading.set(false);
    });

  }
}
