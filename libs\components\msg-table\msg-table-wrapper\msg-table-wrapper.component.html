<ng-template #defaultTemplate
  let-value>{{ value }}</ng-template>

<msg-table-shared #tableRef
  [msgData]="msgData() || []"
  [msgDataFooter]="msgDataFooter()"
  [msgLoading]="msgLoading()"
  [msgPagination]="msgConfig()?.pagination"
  (msgPageChange)="onPageChange($event)"
  (msgFunctionControl)="onFunctionControl($event)"
  class="w-full">
  @if (msgConfig()?.showHeader ?? true) {
    <thead>
      @for (headerRow of headerRows(); track $index) {
        <tr>
          @for (col of headerRow; track col.label || $index) {
            <th [msgWidth]="col.width"
              [msgLeft]="col.stickyLeft || false"
              [msgRight]="col.stickyRight || false"
              [msgStyle]="col.styleHeader | msgTableCellStyle:null:0:[]"
              [msgClass]="col.classHeader | msgTableCellStyle:null:0:[]"
              [msgRow]="col.rowSpan | msgTableRowColSpan:null:0:[]"
              [msgCol]="col.colSpan | msgTableRowColSpan:null:0:[]">
              @if ((col?.type || '') === 'checkbox') {
                <div class="msg-checkbox-container">
                  <input type="checkbox"
                    [checked]="checkboxHeaderStates()"
                    (change)="onCheckAllChange(col, $event)"
                    class="w-5 h-5">
                </div>
              } @else {
                <!-- @let thLabel = col?.label || ''; -->
                <!-- <div nz-tooltip
                #thRef
                [nzTooltipTitle]="((thRef | msgTruncate | async)) ?  thLabel : ''"
                [nzTooltipTrigger]="'hover'"
                [nzTooltipPlacement]="'top'">
                {{ thLabel }}
              </div> -->
                {{ col?.label || '' }}
              }
            </th>
          }
        </tr>
      }
    </thead>
  }
  <tbody>
    @let tableData = table().paginatedData;
    @for (item of table().paginatedData; track item[fieldKey()] || $index; let i = $index) {
      <tr [msgRowClass]="msgConfig().rowClass | msgTableRowStyle:item:i:tableData"
        [msgRowStyle]="msgConfig().rowStyle | msgTableRowStyle:item:i:tableData">
        @for (col of leafColumns(); track col.label || $index) {
          <td #tdRef
            [msgAttr]="col.attr | msgTableAttr:item:i:tableData"
            [msgWidth]="col.width"
            [msgLeft]="col.stickyLeft || false"
            [msgRight]="col.stickyRight || false"
            [msgRow]="col.rowSpan | msgTableRowColSpan:item:i:tableData"
            [msgCol]="col.colSpan | msgTableRowColSpan:item:i:tableData"
            [msgStyle]="col.style | msgTableCellStyle:item:i:tableData"
            [msgClass]="col.class | msgTableCellStyle:item:i:tableData">
            @if (col.type === 'checkbox') {
              <div class="msg-checkbox-container">
                <input type="checkbox"
                  [checked]="item?.checked"
                  (change)="onCheckboxChange(col, item, $event, i)"
                  class="w-5 h-5">
              </div>
            } @else if (col.type === 'expand') {
              <div class="flex items-center justify-center">
                <msg-button-shared [msgSize]="'small'"
                  [msgIconRight]="item?.expand ? 'Minus' : 'Plus'"
                  [msgType]="'outline-secondary'"
                  [msgIconSize]="14"
                  [msgClass]="'!w-[18px] !h-[18px] !min-w-[18px] !min-h-[18px]'"
                  (msgClick)="onExpandClick(col, item, i, tdRef)" />
              </div>
            } @else {
              @let colData = col | msgTableTemplate:item:defaultTemplate:i:tableData:'template';
                @let colDataHtml = col | msgTableTemplate:item:defaultTemplate:i:tableData:'html';
                @let colDataText = col | msgTableTemplate:item:defaultTemplate:i:tableData:'text';
                @if (colData) {
                <ng-container [ngTemplateOutlet]="colData"
                  [ngTemplateOutletContext]="{ $implicit: item }" />
              } @else if (colDataHtml) {
                <div [innerHTML]="colDataHtml"></div>
              } @else {
                <ng-container [ngTemplateOutlet]="defaultTemplate"
                  [ngTemplateOutletContext]="{ $implicit: colDataText }" />
              }
            }
          </td>
        }
      </tr>
      @if (showExpanded().has(i)) {
        <tr class="msg-tr-expand relative">
          <td [attr.colspan]="leafColumns().length"
            class="msg-table-expand-container">
            <div class="msg-expanded-content"
              msgTableExpandedWidth
              [msgTableWrapper]="tableWrapper()">
              @if (showExpanded().get(i)?.template) {
                <ng-container [ngTemplateOutlet]="showExpanded().get(i)!.template"
                  [ngTemplateOutletContext]="{ $implicit: showExpanded().get(i)?.item, index: i }" />
              } @else if (showExpanded().get(i)?.content) {
                <div class="msg-expanded-content h-[50px] flex items-center justify-start pl-4"
                  [innerHTML]="showExpanded().get(i)?.content"></div>
              } @else {
                <div class="msg-expanded-content h-[50px] flex items-center justify-start pl-4">
                  {{ 'i18n_no_data' | translate }}
                </div>
              }
            </div>
          </td>
        </tr>
      }
    }
  </tbody>
  @if (footerRows().length > 0) {
    <tfoot>
      @for (footerRow of footerRows(); track $index; let i = $index) {
        <tr>
          @for (col of footerRow; track col.value || $index) {
            <td [msgWidth]="col.width"
              [msgLeft]="col.stickyLeft || false"
              [msgRight]="col.stickyRight || false"
              [msgRow]="col.rowSpan | msgTableRowColSpan:col:i:msgDataFooter()"
              [msgCol]="col.colSpan | msgTableRowColSpan:col:i:msgDataFooter()"
              [msgStyle]="col.style | msgTableCellStyle:col:i:msgDataFooter()"
              [msgClass]="col.class | msgTableCellStyle:col:i:msgDataFooter()">
              @let colData = col | msgTableTemplate:col:defaultTemplate:i:msgDataFooter():'template';
              @let colDataHtml = col | msgTableTemplate:col:defaultTemplate:i:msgDataFooter():'html';
              @let colDataText = col | msgTableTemplate:col:defaultTemplate:i:msgDataFooter():'text';
              @if (colData) {
                <ng-container [ngTemplateOutlet]="colData"
                  [ngTemplateOutletContext]="{ $implicit: col }" />
              } @else if (colDataHtml) {
                <div [innerHTML]="colDataHtml"></div>
              } @else {
                <ng-container [ngTemplateOutlet]="defaultTemplate"
                  [ngTemplateOutletContext]="{ $implicit: colDataText }" />
              }
            </td>
          }
        </tr>
      }
    </tfoot>
  }
</msg-table-shared>
