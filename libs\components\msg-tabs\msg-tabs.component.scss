@use "responsive" as *;

.msg-tabs {
  display: flex;
  flex-direction: column;
  width: 100%;
  user-select: none;
  -webkit-tap-highlight-color: transparent;

  // Vertical layout
  &.msg-tabs-vertical {
    flex-direction: row;

    .msg-tabs-nav {
      flex-direction: column;
      border-right: 1px solid var(--color-gray-200);
      position: relative;
    }

    .msg-tab-item {
      justify-content: flex-start;
      padding: 8px 16px;
      position: relative;
      flex-shrink: 0;
    }

    .msg-tab-indicator {
      position: absolute;
      width: 2.5px;
      right: -1px;
      top: 0;
      background-color: var(--color-green-500);
      transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        width 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        height 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      pointer-events: none;
    }
  }

  // Horizontal layout
  &.msg-tabs-horizontal {
    .msg-tabs-nav {
      flex-direction: row;
      border-bottom: 1px solid var(--color-gray-200);
      position: relative;
    }

    .msg-tab-item {
      justify-content: center;
      padding: 8px 16px;
      position: relative;
      flex-shrink: 0;
    }

    .msg-tab-indicator {
      position: absolute;
      height: 2.5px;
      bottom: -1px;
      left: 0;
      background-color: var(--color-green-500);
      transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        width 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        height 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      pointer-events: none;
    }

    .msg-tabs-content {
      margin-top: 8px;
      margin-left: unset;
    }
  }

  &.msg-tabs-vertical {
    .msg-tabs-content {
      margin-left: 8px;
      margin-top: unset;
    }
  }

  // Border style
  &.msg-tabs-border {
    .msg-tab-item {
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &.active {
        background-color: var(--color-green-50);
        color: var(--color-green-600);
      }
    }
  }
}

.msg-tabs-nav {
  display: flex;
  position: relative;
  margin: 0;
  padding: 0;
  list-style: none;
  user-select: none;
}

.msg-tab-item {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: var(--color-gray-500);
  transition: color 0.2s ease;
  white-space: nowrap;
  user-select: none;
  outline: none;
  font-weight: 500;
  touch-action: manipulation;

  &:hover {
    color: var(--color-green-400);
  }

  &.active {
    color: var(--color-green-500);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  &.hovered:not(.active) {
    color: var(--color-green-400);
  }
}

.msg-tab-label {
  font-size: 13px;
  white-space: nowrap;
  position: relative;
  user-select: none;
}

.msg-tab-badge {
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 9999px;
  background-color: var(--color-green-100);
  color: var(--color-green-600);
  white-space: nowrap;
  user-select: none;
}

.msg-tabs-content {
  flex: 1;
  overflow: hidden;
  position: relative;

  .tab-content {
    height: 100%;
    width: 100%;
    position: relative;

    &.slide-left {
      animation: slide-left 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &.slide-right {
      animation: slide-right 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &.slide-top {
      animation: slide-top 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &.slide-bottom {
      animation: slide-bottom 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
}

@keyframes slide-left {
  from {
    opacity: 0;
    transform: translateX(30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-right {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-top {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-bottom {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@include respond-to-max('tablet') {
  .msg-tabs-nav {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
  }
}
