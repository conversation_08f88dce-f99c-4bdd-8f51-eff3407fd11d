<div class="w-full flex flex-col h-[100dvh]">
  <div class="sticky top-0 z-[20] shrink-0">
    <div class="pl-[40px] h-[45px] bg-white flex items-center justify-between md:px-[16px] text-dark font-semibold text-[13px] border-b border-gray-300 uppercase z-[1000]">
      <div class="truncate max-w-[250px] sm:max-w-[500px]">Fis Dashboard</div>
    </div>
    <div class="w-full">
      <div class="w-full bg-white py-2 px-[16px] shadow-sm">
        <div class="flex flex-col sm:flex-row sm:flex-wrap sm:justify-end items-end gap-2">
          <div class="flex items-center w-full sm:w-auto gap-2">
            <nz-date-picker nzNoAnimation
              nzFormat="dd/MM/yyyy"
              class="w-full sm:w-[150px] min-w-[150px]" />
            <nz-select nzShowSearch
              class="w-full sm:w-[150px] min-w-[150px]"
              nzAllowClear
              nzPlaceHolder="Chọn site">
              <nz-option nzValue="all"
                nzLabel="Tất cả" />
              <nz-option nzValue="site1"
                nzLabel="Site 1" />
              <nz-option nzValue="site2"
                nzLabel="Site 2" />
            </nz-select>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Scroll tổng cho cả grid 4 table -->
  <div class="flex-1 overflow-y-auto">
    <div class="grid grid-cols-1 md:grid-cols-2 grid-rows-4 md:grid-rows-2 gap-4 p-4 bg-gray-50 min-h-full">
      <div class="h-[500px] bg-white rounded shadow-md p-2 flex flex-col min-w-0">
        <div class="font-semibold mb-2">Table 1 (fit)</div>
        <div class="min-w-0 overflow-x-auto flex-1">
          <msg-table-wrapper-shared [msgConfig]="tableConfig()[0]"
            [msgData]="table1Data()" />
        </div>
      </div>
      <div class="h-[500px] bg-white rounded shadow-md p-2 flex flex-col min-w-0">
        <div class="font-semibold mb-2">Table 2 (fit)</div>
        <div class="min-w-0 overflow-x-auto flex-1">
          <msg-table-wrapper-shared [msgConfig]="tableConfig()[1]"
            [msgData]="table2Data()" />
        </div>
      </div>
      <div class="h-[500px] bg-white rounded shadow-md p-2 flex flex-col min-w-0">
        <div class="font-semibold mb-2">Table 3 (scroll)</div>
        <div class="min-w-0 overflow-x-auto flex-1">
          <msg-table-wrapper-shared [msgConfig]="tableConfig()[2]"
            [msgData]="table3Data()" />
        </div>
      </div>
      <div class="h-[500px] bg-white rounded shadow-md p-2 flex flex-col min-w-0">
        <div class="font-semibold mb-2">Table 4 (scroll)</div>
        <div class="min-w-0 overflow-x-auto flex-1">
          <msg-table-wrapper-shared [msgConfig]="tableConfig()[3]"
            [msgData]="table4Data()" />
        </div>
      </div>
    </div>
  </div>
</div>
