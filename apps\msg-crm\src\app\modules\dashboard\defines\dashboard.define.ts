
import { TemplateRef } from '@angular/core';
import { IMsgTableConfig } from '@libs/components/msg-table/interfaces';
import { formatMsgNumber } from '@libs/utils';

export interface IDashboardData {
  stt: number;
  items: string;
  budget: number;
  actual: number;
  'Eye Hospital': number;
  'General Hospital': number;
  'MSG HO': number;
  'MSG LTR': number;
  'MSG NGT': number;
  'MSG CT': number;
  'MSG NT': number;
  'MSG DKNT': number;
  'MSG DKPR': number;
  'MSG VI': number;
  'MSG DKHT': number;
  'MSG LA': number;
  'MSG ND': number;
  'MSG SH': number;
  'MSG DKHA': number;
  'MSG QN': number;
  'MSG TN': number;
  'MSG HL': number;
  'MSG CTE': number;
  'MSG DN': number;
  'MSG DKTK': number;
  'MSG CMTT': number;
  children?: any[];
}

export const dashboardDefine = (): IDashboardData[] => [
  {
    "stt": 1,
    "items": "Gross Revenue",
    "budget": 298397444307.4272,
    "actual": 259895109799.4424,
    "Eye Hospital": 203206069903.6023,
    "General Hospital": 34513574855.1466,
    "MSG HO": 0,
    "MSG LTR": 26529739045.6868,
    "MSG NGT": 24903589263.855,
    "MSG CT": 24312658801,
    "MSG NT": 14155854766.24,
    "MSG DKNT": 106364873.42,
    "MSG DKPR": 15280114856.875,
    "MSG VI": 19894177899,
    "MSG DKHT": 6829931722.9446,
    "MSG LA": 16290550746.9147,
    "MSG ND": 19411659747.9625,
    "MSG SH": 10428718516.342,
    "MSG DKHA": 8712334475.2695,
    "MSG QN": 5561253267.72,
    "MSG TN": 6118202531.3,
    "MSG HL": 6293824109.6667,
    "MSG CTE": 33970815.67,
    "MSG DN": 0,
    "MSG DKTK": 10414760649.5821,
    "MSG CMTT": 22441938669.3,
    children: [
      {
        "stt": 1,
        "items": "Drugs and Consumables",
        "budget": 102430522466.3539,
        "actual": 89713939950.6687,
        "Eye Hospital": 72391455717.7066,
        "General Hospital": 9997109748.092,
        "MSG HO": 0,
        "MSG LTR": 7831725517.9139,
        "MSG NGT": 8455760199.0026,
        "MSG CT": 8950619880,
        "MSG NT": 5015248343.1708,
        "MSG DKNT": -345714,
        "MSG DKPR": 4650965437.75,
        "MSG VI": 7528395502.5123,
        "MSG DKHT": 2960150124.6,
        "MSG LA": 6499864467.0494,
        "MSG ND": 7699133045.63,
        "MSG SH": 3517964662.5649,
        "MSG DKHA": 2043042434.4507,
        "MSG QN": 2271211364.5891,
        "MSG TN": 2639772655.72,
        "MSG HL": 2242664879.4,
        "MSG CTE": 8929943,
        "MSG DN": 0,
        "MSG DKTK": 3303447589.8913,
        "MSG CMTT": 6770015132.5536
      }
    ]
  },
  {
    "stt": 2,
    "items": "Drugs and Consumables",
    "budget": 102430522466.3539,
    "actual": 89713939950.6687,
    "Eye Hospital": 72391455717.7066,
    "General Hospital": 9997109748.092,
    "MSG HO": 0,
    "MSG LTR": 7831725517.9139,
    "MSG NGT": 8455760199.0026,
    "MSG CT": 8950619880,
    "MSG NT": 5015248343.1708,
    "MSG DKNT": -345714,
    "MSG DKPR": 4650965437.75,
    "MSG VI": 7528395502.5123,
    "MSG DKHT": 2960150124.6,
    "MSG LA": 6499864467.0494,
    "MSG ND": 7699133045.63,
    "MSG SH": 3517964662.5649,
    "MSG DKHA": 2043042434.4507,
    "MSG QN": 2271211364.5891,
    "MSG TN": 2639772655.72,
    "MSG HL": 2242664879.4,
    "MSG CTE": 8929943,
    "MSG DN": 0,
    "MSG DKTK": 3303447589.8913,
    "MSG CMTT": 6770015132.5536
  },
  {
    "stt": 3,
    "items": "Gross Contribution",
    "budget": 195891830647.0914,
    "actual": 170106078654.7921,
    "Eye Hospital": 130744216191.5379,
    "General Hospital": 24516465107.0546,
    "MSG HO": 0,
    "MSG LTR": 18698013527.773,
    "MSG NGT": 16447829064.8524,
    "MSG CT": 15362038921,
    "MSG NT": 9140606423.0692,
    "MSG DKNT": 106710587.42,
    "MSG DKPR": 10629149419.125,
    "MSG VI": 12381202671.6877,
    "MSG DKHT": 3869781598.3446,
    "MSG LA": 9790686279.8653,
    "MSG ND": 11712526702.3325,
    "MSG SH": 6892261400.2271,
    "MSG DKHA": 6669292040.8188,
    "MSG QN": 3290041903.1309,
    "MSG TN": 3478429875.58,
    "MSG HL": 4051159230.2667,
    "MSG CTE": 25040872.67,
    "MSG DN": 0,
    "MSG DKTK": 7111313059.6908,
    "MSG CMTT": 15604597720.7385
  },
  {
    "stt": 4,
    "items": "Manpower",
    "budget": 132462842624.0414,
    "actual": 132462842624.0414,
    "Eye Hospital": 41348107033.9914,
    "General Hospital": 19163572286.4535,
    "MSG HO": 11439483983.1516,
    "MSG LTR": 3974566351.3373,
    "MSG NGT": 3656311516.1566,
    "MSG CT": 3963961393,
    "MSG NT": 2796898126,
    "MSG DKNT": 5184537625.2635,
    "MSG DKPR": 5452823407.4326,
    "MSG VI": 3757784260.4287,
    "MSG DKHT": 2203542058,
    "MSG LA": 2904877964.5358,
    "MSG ND": 2841959309.041,
    "MSG SH": 2142366719.6915,
    "MSG DKHA": 4422959029.7574,
    "MSG QN": 1619004087.7776,
    "MSG TN": 1746993879,
    "MSG HL": 1540328443.8128,
    "MSG CTE": 4191721358.8783,
    "MSG DN": 0,
    "MSG DKTK": 4103252224,
    "MSG CMTT": 4007791566.3318
  },
  {
    "stt": 5,
    "items": "Overheads",
    "budget": 18535195354.6957,
    "actual": 18535195354.6957,
    "Eye Hospital": 6122348593.061,
    "General Hospital": 2960180510.4719,
    "MSG HO": 370137147.6299,
    "MSG LTR": 724356915.6917,
    "MSG NGT": 700321180.0888,
    "MSG CT": 632677957,
    "MSG NT": 233800229,
    "MSG DKNT": 968268105.3189,
    "MSG DKPR": 831734629.8544,
    "MSG VI": 573295484.3039,
    "MSG DKHT": 242637640.6667,
    "MSG LA": 483593577.1222,
    "MSG ND": 205636662.6618,
    "MSG SH": 465415666,
    "MSG DKHA": 651114173.0399,
    "MSG QN": 206088454.3089,
    "MSG TN": 205038035.8314,
    "MSG HL": 295578265.7013,
    "MSG CTE": 758582745.3404,
    "MSG DN": 0,
    "MSG DKTK": 509063602.2587,
    "MSG CMTT": 395325779.3439
  },
  {
    "stt": 6,
    "items": "S&M Expenses",
    "budget": 21497132118.8318,
    "actual": 21497132118.8318,
    "Eye Hospital": 18775660823.8329,
    "General Hospital": 943808458.728,
    "MSG HO": 290000000,
    "MSG LTR": 3304343013.5488,
    "MSG NGT": 2552383820.6181,
    "MSG CT": 946838097,
    "MSG NT": 885065681.7854,
    "MSG DKNT": 166975174.3333,
    "MSG DKPR": 341385416.6667,
    "MSG VI": 1294784439.0051,
    "MSG DKHT": 292150000,
    "MSG LA": 1680792426.2354,
    "MSG ND": 1973268411.6703,
    "MSG SH": 1241398605.9207,
    "MSG DKHA": 186567201.0613,
    "MSG QN": 268825776.9784,
    "MSG TN": 260136666.6667,
    "MSG HL": 588308077.9098,
    "MSG CTE": 724064444,
    "MSG DN": 0,
    "MSG DKTK": 248880666.6667,
    "MSG CMTT": 2763301362.4942
  },
  {
    "stt": 7,
    "items": "G&A",
    "budget": 22507671715.0654,
    "actual": 22507671715.0654,
    "Eye Hospital": 9285995915.1635,
    "General Hospital": 1217788477.962,
    "MSG HO": 1500102928.8144,
    "MSG LTR": 728268586.5984,
    "MSG NGT": 1033834763.3275,
    "MSG CT": 401296398,
    "MSG NT": 275180741.5397,
    "MSG DKNT": 374576226.9389,
    "MSG DKPR": 415099373.09,
    "MSG VI": 761234990.66,
    "MSG DKHT": 378409412.9473,
    "MSG LA": 1471604321.2626,
    "MSG ND": 865033698.0162,
    "MSG SH": 620031282.1203,
    "MSG DKHA": 216143879.9583,
    "MSG QN": 411345827.703,
    "MSG TN": 429406396.8765,
    "MSG HL": 433188475.7399,
    "MSG CTE": 781403744.5702,
    "MSG DN": 0,
    "MSG DKTK": 211968997.9748,
    "MSG CMTT": 695757275.8019
  },
  {
    "stt": 8,
    "items": "Non-operating income",
    "budget": 11809582339.6069,
    "actual": 11809582339.6069,
    "Eye Hospital": 8800086632.2046,
    "General Hospital": 1212540017.0739,
    "MSG HO": 907148243.9129,
    "MSG LTR": 743183358.5083,
    "MSG NGT": 696985545.4545,
    "MSG CT": 1460403502.3891,
    "MSG NT": 656592695.6364,
    "MSG DKNT": 325511618,
    "MSG DKPR": 388568456.1927,
    "MSG VI": 890595936.0909,
    "MSG DKHT": 598745445.1818,
    "MSG LA": 455666553.3554,
    "MSG ND": 334858818.1818,
    "MSG SH": 356336359.0909,
    "MSG DKHA": 107834074.5479,
    "MSG QN": 540598690.9091,
    "MSG TN": 683379066,
    "MSG HL": 341068856,
    "MSG CTE": 663106227.2727,
    "MSG DN": 0,
    "MSG DKTK": 390625868.3333,
    "MSG CMTT": 378565578.1337
  },
  {
    "stt": 9,
    "items": "EBITDA",
    "budget": 163541149745.1516,
    "actual": 142542139039.4722,
    "Eye Hospital": 69804856523.2099,
    "General Hospital": 7812500904.3677,
    "MSG HO": -12692575815.683,
    "MSG LTR": 10709662019.1051,
    "MSG NGT": 9201963330.1159,
    "MSG CT": 10877668578.3891,
    "MSG NT": 5606254340.3805,
    "MSG DKNT": 106710587.42,
    "MSG DKPR": 3976675048.274,
    "MSG VI": 6884699433.3809,
    "MSG DKHT": 1351787931.9124,
    "MSG LA": 3705484544.0647,
    "MSG ND": 6161487439.125,
    "MSG SH": 2779385485.5855,
    "MSG DKHA": 1300341831.5498,
    "MSG QN": 1325376447.2721,
    "MSG TN": 1520233963.2054,
    "MSG HL": 1534824823.1029,
    "MSG CTE": 25040872.67,
    "MSG DN": 0,
    "MSG DKTK": 2428773437.1239,
    "MSG CMTT": 8120987314.9004
  },
  {
    "stt": 10,
    "items": "Depreciation",
    "budget": 22507388075.9421,
    "actual": 22507388075.9421,
    "Eye Hospital": 7746283066.0051,
    "General Hospital": 1827076026.8224,
    "MSG HO": 3360669890.2871,
    "MSG LTR": 761502460.3761,
    "MSG NGT": 544253077.5564,
    "MSG CT": 711750131,
    "MSG NT": 365448110.6368,
    "MSG DKNT": 456976133.6448,
    "MSG DKPR": 661977686.2431,
    "MSG VI": 807862636.1796,
    "MSG DKHT": 384991189.6667,
    "MSG LA": 405862890.8223,
    "MSG ND": 770687372.6024,
    "MSG SH": 491162354.3333,
    "MSG DKHA": 308207878.1821,
    "MSG QN": 183836314.3056,
    "MSG TN": 216709879.0276,
    "MSG HL": 293575197.9198,
    "MSG CTE": 1003374525.1111,
    "MSG DN": 0,
    "MSG DKTK": 399914328.7524,
    "MSG CMTT": 805266926.4674
  },
  {
    "stt": 11,
    "items": "Interest",
    "budget": 276513520.0706,
    "actual": 276513520.0706,
    "Eye Hospital": 3756760.0353,
    "General Hospital": 7000000,
    "MSG HO": 255000000,
    "MSG LTR": 1156111.0551,
    "MSG NGT": 0,
    "MSG CT": 0,
    "MSG NT": 44452054.6986,
    "MSG DKNT": 20000000,
    "MSG DKPR": 2000000,
    "MSG VI": 0,
    "MSG DKHT": 33000000,
    "MSG LA": 0,
    "MSG ND": 0,
    "MSG SH": 3500000,
    "MSG DKHA": 0,
    "MSG QN": -67500000,
    "MSG TN": 0,
    "MSG HL": 0,
    "MSG CTE": 0,
    "MSG DN": 0,
    "MSG DKTK": -15000000,
    "MSG CMTT": -10851405.7184
  },
  {
    "stt": 12,
    "items": "PBT",
    "budget": 141233608522.614,
    "actual": 123171965801.1132,
    "Eye Hospital": 63065704742.3514,
    "General Hospital": 6419401011.1902,
    "MSG HO": -15798245705.97,
    "MSG LTR": 9949315669.7842,
    "MSG NGT": 8657710252.5595,
    "MSG CT": 10165918447.3891,
    "MSG NT": 5285258284.4422,
    "MSG DKNT": 106710587.42,
    "MSG DKPR": 3316697362.0309,
    "MSG VI": 6076836797.2013,
    "MSG DKHT": 999796742.2457,
    "MSG LA": 3299621653.2424,
    "MSG ND": 5390800066.5226,
    "MSG SH": 2291723131.2521,
    "MSG DKHA": 982133953.3677,
    "MSG QN": 1074040132.9666,
    "MSG TN": 1303524084.1779,
    "MSG HL": 1241249625.1832,
    "MSG CTE": 25040872.67,
    "MSG DN": 0,
    "MSG DKTK": 2013859108.3716,
    "MSG CMTT": 7304868982.7146
  },
  {
    "stt": 13,
    "items": "PAT",
    "budget": 121343549299.5233,
    "actual": 104107718004.0451,
    "Eye Hospital": 53083637319.2032,
    "General Hospital": 5752580926.0776,
    "MSG HO": -13564718486.5165,
    "MSG LTR": 8031936359.6517,
    "MSG NGT": 6926168202.0476,
    "MSG CT": 9149326602.3891,
    "MSG NT": 4606691270.653,
    "MSG DKNT": 106710587.42,
    "MSG DKPR": 3150862493.9294,
    "MSG VI": 5481815878.5899,
    "MSG DKHT": 813037393.7966,
    "MSG LA": 2707681010.6972,
    "MSG ND": 4312640053.218,
    "MSG SH": 1833378504.9217,
    "MSG DKHA": 883920558.0309,
    "MSG QN": 1074040132.9666,
    "MSG TN": 1303524084.1779,
    "MSG HL": 974461767.2522,
    "MSG CTE": 25040872.67,
    "MSG DN": 0,
    "MSG DKTK": 1611087286.6973,
    "MSG CMTT": 5843895186.1717
  },
  {
    "stt": 14,
    "items": "% D&C",
    "budget": 34.3269,
    "actual": 34.5193,
    "Eye Hospital": 35.6247,
    "General Hospital": 85.2819,
    "MSG HO": 0,
    "MSG LTR": 29.5206,
    "MSG NGT": 33.954,
    "MSG CT": 36.8146,
    "MSG NT": 35.4288,
    "MSG DKNT": -0.325,
    "MSG DKPR": 30.438,
    "MSG VI": 37.8422,
    "MSG DKHT": 43.3408,
    "MSG LA": 39.8996,
    "MSG ND": 39.6624,
    "MSG SH": 33.7334,
    "MSG DKHA": 23.45,
    "MSG QN": 40.8399,
    "MSG TN": 43.1462,
    "MSG HL": 35.6328,
    "MSG CTE": 26.2871,
    "MSG DN": 0,
    "MSG DKTK": 31.7189,
    "MSG CMTT": 30.1668
  },
  {
    "stt": 15,
    "items": "% Gross Contribution",
    "budget": 65.648,
    "actual": 65.4518,
    "Eye Hospital": 64.3407,
    "General Hospital": 314.7181,
    "MSG HO": 0,
    "MSG LTR": 70.4794,
    "MSG NGT": 66.046,
    "MSG CT": 63.1854,
    "MSG NT": 64.5712,
    "MSG DKNT": 100.325,
    "MSG DKPR": 69.562,
    "MSG VI": 62.2353,
    "MSG DKHT": 56.6592,
    "MSG LA": 60.1004,
    "MSG ND": 60.3376,
    "MSG SH": 66.0892,
    "MSG DKHA": 76.55,
    "MSG QN": 59.1601,
    "MSG TN": 56.8538,
    "MSG HL": 64.3672,
    "MSG CTE": 73.7129,
    "MSG DN": 0,
    "MSG DKTK": 68.2811,
    "MSG CMTT": 69.5332
  },
  {
    "stt": 16,
    "items": "% Manpower",
    "budget": 44.3914,
    "actual": 50.9678,
    "Eye Hospital": 20.3479,
    "General Hospital": 165.2996,
    "MSG HO": 0,
    "MSG LTR": 14.9816,
    "MSG NGT": 14.6819,
    "MSG CT": 16.3041,
    "MSG NT": 19.7579,
    "MSG DKNT": 39.4489,
    "MSG DKPR": 35.6857,
    "MSG VI": 18.8889,
    "MSG DKHT": 32.263,
    "MSG LA": 17.8317,
    "MSG ND": 14.6405,
    "MSG SH": 20.543,
    "MSG DKHA": 50.7666,
    "MSG QN": 29.1122,
    "MSG TN": 28.554,
    "MSG HL": 24.4736,
    "MSG CTE": 20.101,
    "MSG DN": 0,
    "MSG DKTK": 39.3984,
    "MSG CMTT": 17.8585
  },
  {
    "stt": 17,
    "items": "% Overheads",
    "budget": 6.2116,
    "actual": 7.1318,
    "Eye Hospital": 3.0129,
    "General Hospital": 25.1721,
    "MSG HO": 0,
    "MSG LTR": 2.7304,
    "MSG NGT": 2.8121,
    "MSG CT": 2.6023,
    "MSG NT": 1.6516,
    "MSG DKNT": 7.3675,
    "MSG DKPR": 5.4432,
    "MSG VI": 2.8817,
    "MSG DKHT": 3.5526,
    "MSG LA": 2.9686,
    "MSG ND": 1.0593,
    "MSG SH": 4.4628,
    "MSG DKHA": 7.4735,
    "MSG QN": 3.7058,
    "MSG TN": 3.3513,
    "MSG HL": 4.6963,
    "MSG CTE": 3.6377,
    "MSG DN": 0,
    "MSG DKTK": 4.8879,
    "MSG CMTT": 1.7615
  },
  {
    "stt": 18,
    "items": "% S&M Expenses",
    "budget": 7.2042,
    "actual": 8.2715,
    "Eye Hospital": 9.2397,
    "General Hospital": 8.0358,
    "MSG HO": 0,
    "MSG LTR": 12.4552,
    "MSG NGT": 10.2491,
    "MSG CT": 3.8944,
    "MSG NT": 6.2523,
    "MSG DKNT": 1.2705,
    "MSG DKPR": 2.2342,
    "MSG VI": 6.5084,
    "MSG DKHT": 4.2775,
    "MSG LA": 10.3176,
    "MSG ND": 10.1654,
    "MSG SH": 11.9037,
    "MSG DKHA": 2.1414,
    "MSG QN": 4.8339,
    "MSG TN": 4.2518,
    "MSG HL": 9.3474,
    "MSG CTE": 3.4722,
    "MSG DN": 0,
    "MSG DKTK": 2.3897,
    "MSG CMTT": 12.3131
  },
  {
    "stt": 19,
    "items": "% G&A",
    "budget": 7.5429,
    "actual": 8.6603,
    "Eye Hospital": 4.5697,
    "General Hospital": 10.0829,
    "MSG HO": 0,
    "MSG LTR": 2.7451,
    "MSG NGT": 4.1513,
    "MSG CT": 1.6506,
    "MSG NT": 1.9439,
    "MSG DKNT": 2.8501,
    "MSG DKPR": 2.7166,
    "MSG VI": 3.8264,
    "MSG DKHT": 5.5405,
    "MSG LA": 9.0335,
    "MSG ND": 4.4563,
    "MSG SH": 5.9454,
    "MSG DKHA": 2.4809,
    "MSG QN": 7.3966,
    "MSG TN": 7.0185,
    "MSG HL": 6.8828,
    "MSG CTE": 3.7471,
    "MSG DN": 0,
    "MSG DKTK": 2.0353,
    "MSG CMTT": 3.1003
  },
  {
    "stt": 20,
    "items": "% Depreciation",
    "budget": 7.5428,
    "actual": 8.6602,
    "Eye Hospital": 3.812,
    "General Hospital": 15.1869,
    "MSG HO": 0,
    "MSG LTR": 2.8704,
    "MSG NGT": 2.1854,
    "MSG CT": 2.9275,
    "MSG NT": 2.5816,
    "MSG DKNT": 3.4771,
    "MSG DKPR": 4.3323,
    "MSG VI": 4.0608,
    "MSG DKHT": 5.6368,
    "MSG LA": 2.4914,
    "MSG ND": 3.9702,
    "MSG SH": 4.7097,
    "MSG DKHA": 3.5376,
    "MSG QN": 3.3057,
    "MSG TN": 3.5421,
    "MSG HL": 4.6645,
    "MSG CTE": 4.8116,
    "MSG DN": 0,
    "MSG DKTK": 3.8399,
    "MSG CMTT": 3.5882
  },
  {
    "stt": 21,
    "items": "% EBITDA",
    "budget": 54.8065,
    "actual": 54.846,
    "Eye Hospital": 34.3518,
    "General Hospital": 164.596,
    "MSG HO": 0,
    "MSG LTR": 40.3685,
    "MSG NGT": 36.9503,
    "MSG CT": 44.7408,
    "MSG NT": 39.6038,
    "MSG DKNT": 100.325,
    "MSG DKPR": 26.0252,
    "MSG VI": 34.6066,
    "MSG DKHT": 19.7921,
    "MSG LA": 22.7462,
    "MSG ND": 31.7412,
    "MSG SH": 26.6513,
    "MSG DKHA": 14.9253,
    "MSG QN": 23.8323,
    "MSG TN": 24.8477,
    "MSG HL": 24.3862,
    "MSG CTE": 73.7129,
    "MSG DN": 0,
    "MSG DKTK": 23.3205,
    "MSG CMTT": 36.1867
  },
  {
    "stt": 22,
    "items": "% PAT",
    "budget": 40.6651,
    "actual": 40.0576,
    "Eye Hospital": 26.1231,
    "General Hospital": 146.5606,
    "MSG HO": 0,
    "MSG LTR": 30.2752,
    "MSG NGT": 27.8119,
    "MSG CT": 37.6319,
    "MSG NT": 32.5427,
    "MSG DKNT": 100.325,
    "MSG DKPR": 20.6207,
    "MSG VI": 27.5549,
    "MSG DKHT": 11.904,
    "MSG LA": 16.6212,
    "MSG ND": 22.2168,
    "MSG SH": 17.5801,
    "MSG DKHA": 10.1456,
    "MSG QN": 19.3129,
    "MSG TN": 21.3057,
    "MSG HL": 15.4828,
    "MSG CTE": 73.7129,
    "MSG DN": 0,
    "MSG DKTK": 15.4693,
    "MSG CMTT": 26.0401
  }
];

export const getDashboardTableConfig = (actionTemplate: TemplateRef<HTMLElement>, expandTemplate: TemplateRef<HTMLElement>): IMsgTableConfig => ({
  fieldKey: 'stt',
  rowStyle: (item, index) => {
    // if ([0, 1, 2, 12, 13].includes(index)) {
    //   return 'background-color: var(--color-yellow-100);';
    // }

    return '';
  },
  header: [
    {
      label: '',
      type: 'checkbox',
      class: 'text-center',
      stickyLeft: true,
      expandedTemplate: () => expandTemplate,
      width: 40,
      rowSpan: (item, index, data) => {
        item;
        if (index === 0) {
          return 3;
        }

        if (index === 3) {
          return 4;
        }

        if (index === 7) {
          return 5;
        }

        return 2;
      }
    },
    {
      label: '',
      type: 'expand',
      class: 'text-center',
      stickyLeft: true,
      expandedTemplate: () => expandTemplate,
      width: 40,
      rowSpan: (item, index, data) => {
        item;
        if (index === 0) {
          return 3;
        }

        if (index === 3) {
          return 4;
        }

        if (index === 7) {
          return 5;
        }

        return 2;
      }
    },
    {
      label: 'STT',
      value: (item: IDashboardData) => item.stt.toString(),
      width: 60,
      class: 'text-center font-mono',
      stickyLeft: true,
      attr: (item) => {
        return {
          'msg-value': item.stt,
        };
      }
    },
    {
      label: 'Hạng mục',
      value: (item: IDashboardData) => item.items,
      width: 150,
      class: 'font-medium !break-normal !whitespace-normal',
      stickyLeft: true,
      rowSpan: (item, index) => {
        item;
        if (index === 0) {
          return 3;
        }

        if (index === 3) {
          return 4;
        }

        if (index === 7) {
          return 5;
        }

        return 2;
      },
    },
    {
      label: 'Ngân sách & Thực tế',
      // classHeader: '!bg-blue-300 !border-r-0 !text-center',
      // stickyLeft: true,
      children: [
        {
          label: 'Ngân sách',
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return `${formatMsgNumber(item.budget)}%`;
            }

            return formatMsgNumber(item.budget, true,);
          },
          // classHeader: '!bg-blue-300 !text-right',
          class: (item: IDashboardData) => {
            const baseClass = 'text-right font-medium';
            return item?.items?.startsWith('%') ?
              `${baseClass} text-blue-600` :
              `${baseClass} text-green-600`;
          },
          // stickyLeft: true,
          width: 100,
        },
        {
          label: 'Thực tế',
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return `${formatMsgNumber(item.actual)}%`;
            }

            return formatMsgNumber(item.actual, true,);
          },
          // classHeader: '!bg-green-300 !text-right',
          class: (item: IDashboardData) => {
            const baseClass = 'text-right font-medium';
            return item?.items?.startsWith('%') ?
              `${baseClass} text-purple-600` :
              `${baseClass} text-orange-600`;
          },
          // stickyLeft: true,
          width: 100,
        }
      ]
    },
    {
      label: 'Bệnh viện',
      // classHeader: '!bg-orange-300 !text-center',
      children: [
        {
          label: 'Eye Hospital',
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['Eye Hospital']);
            }

            return formatMsgNumber(item['Eye Hospital'], true,);
          },
          classHeader: '!text-right',
          class: 'text-right',
          width: 100,
        },
        {
          label: 'General Hospital',
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['General Hospital']);
            }
            return formatMsgNumber(item['General Hospital'], true,);
          },
          classHeader: '!text-right',
          class: 'text-right',
          width: 100,
        }
      ]
    },
    {
      label: 'MSG Regions',
      // classHeader: '!bg-green-300',
      children: [
        {
          label: 'MSG HO',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG HO']);
            }
            return formatMsgNumber(item['MSG HO'], true,);
          },
          class: '!text-right'
        },
        {
          label: 'MSG LTR',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG LTR']);
            }
            return formatMsgNumber(item['MSG LTR'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG NGT',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG NGT']);
            }
            return formatMsgNumber(item['MSG NGT'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG CT',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG CT']);
            }
            return formatMsgNumber(item['MSG CT'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG NT',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG NT']);
            }
            return formatMsgNumber(item['MSG NT'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG DKNT',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG DKNT']);
            }
            return formatMsgNumber(item['MSG DKNT'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG DKPR',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG DKPR']);
            }
            return formatMsgNumber(item['MSG DKPR'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG VI',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG VI']);
            }
            return formatMsgNumber(item['MSG VI'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG DKHT',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG DKHT']);
            }
            return formatMsgNumber(item['MSG DKHT'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG LA',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG LA']);
            }
            return formatMsgNumber(item['MSG LA'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG ND',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG ND']);
            }
            return formatMsgNumber(item['MSG ND'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG SH',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG SH']);
            }
            return formatMsgNumber(item['MSG SH'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG DKHA',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG DKHA']);
            }
            return formatMsgNumber(item['MSG DKHA'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG QN',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG QN']);
            }
            return formatMsgNumber(item['MSG QN'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG TN',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG TN']);
            }
            return formatMsgNumber(item['MSG TN'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG HL',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG HL']);
            }
            return formatMsgNumber(item['MSG HL'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG CTE',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG CTE']);
            }
            return formatMsgNumber(item['MSG CTE'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG DN',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG DN']);
            }
            return formatMsgNumber(item['MSG DN'], true,);
          },
          class: 'text-right'
        },
        {
          label: 'MSG DKTK',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG DKTK']);
            }
            return formatMsgNumber(item['MSG DKTK'], true,);
          },
          class: 'text-right',
        },
        {
          label: 'MSG CMTT',
          classHeader: '!text-right',
          width: 100,
          value: (item: IDashboardData) => {
            if (item.items.startsWith('%')) {
              return formatMsgNumber(item['MSG CMTT']);
            }

            return formatMsgNumber(item['MSG CMTT'], true,);
          },
          class: 'text-right',
        },
      ]
    },
    {
      label: 'Thao tác',
      value: () => actionTemplate,
      width: 100,
      class: 'text-center',
      stickyRight: true
    }
  ],
});

export const expandTemplate = (): IMsgTableConfig => {
  return {
    showHeader: false,
    header: [
      {
        label: '',
        width: 40,
      },
      {
        label: '',
        width: 40,
      },
      {
        label: 'STT',
        width: 60,
        class: 'text-center font-mono',
      },
      {
        label: 'Hạng mục',
        width: 150,
        class: 'font-medium',
      },
      {
        label: 'Ngân sách & Thực tế',
        class: '!bg-blue-300',
        children: [
          {
            label: 'Ngân sách',
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item.budget);
              }
              return formatMsgNumber(item.budget, true);
            },
            class: (item: IDashboardData) => {
              const baseClass = 'text-right font-medium';
              return item?.items?.startsWith('%') ?
                `${baseClass} text-blue-600` :
                `${baseClass} text-green-600`;
            },
            width: 100,
          },
          {
            label: 'Thực tế',
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item.actual);
              }
              return formatMsgNumber(item.actual, true);
            },
            class: (item: IDashboardData) => {
              const baseClass = 'text-right font-medium';
              return item?.items?.startsWith('%') ?
                `${baseClass} text-purple-600` :
                `${baseClass} text-orange-600`;
            },
            width: 100,
          }
        ]
      },
      {
        label: 'Bệnh viện',
        class: '!bg-orange-300',
        children: [
          {
            label: 'Eye Hospital',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['Eye Hospital']);
              }
              return formatMsgNumber(item['Eye Hospital'], true);
            },
            class: 'text-right'
          },
          {
            label: 'General Hospital',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['General Hospital']);
              }
              return formatMsgNumber(item['General Hospital'], true);
            },
            class: 'text-right'
          }
        ]
      },
      {
        label: 'MSG Regions',
        class: '!bg-green-300',
        children: [
          {
            label: 'MSG HO',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG HO']);
              }
              return formatMsgNumber(item['MSG HO'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG LTR',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG LTR']);
              }
              return formatMsgNumber(item['MSG LTR'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG NGT',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG NGT']);
              }
              return formatMsgNumber(item['MSG NGT'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG CT',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG CT']);
              }
              return formatMsgNumber(item['MSG CT'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG NT',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG NT']);
              }
              return formatMsgNumber(item['MSG NT'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG DKNT',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG DKNT']);
              }
              return formatMsgNumber(item['MSG DKNT'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG DKPR',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG DKPR']);
              }
              return formatMsgNumber(item['MSG DKPR'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG VI',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG VI']);
              }
              return formatMsgNumber(item['MSG VI'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG DKHT',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG DKHT']);
              }
              return formatMsgNumber(item['MSG DKHT'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG LA',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG LA']);
              }
              return formatMsgNumber(item['MSG LA'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG ND',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG ND']);
              }
              return formatMsgNumber(item['MSG ND'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG SH',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG SH']);
              }
              return formatMsgNumber(item['MSG SH'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG DKHA',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG DKHA']);
              }
              return formatMsgNumber(item['MSG DKHA'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG QN',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG QN']);
              }
              return formatMsgNumber(item['MSG QN'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG TN',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG TN']);
              }
              return formatMsgNumber(item['MSG TN'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG HL',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG HL']);
              }
              return formatMsgNumber(item['MSG HL'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG CTE',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG CTE']);
              }
              return formatMsgNumber(item['MSG CTE'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG DN',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG DN']);
              }
              return formatMsgNumber(item['MSG DN'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG DKTK',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG DKTK']);
              }
              return formatMsgNumber(item['MSG DKTK'], true);
            },
            class: 'text-right'
          },
          {
            label: 'MSG CMTT',
            width: 100,
            value: (item: IDashboardData) => {
              if (item.items.startsWith('%')) {
                return formatMsgNumber(item['MSG CMTT']);
              }
              return formatMsgNumber(item['MSG CMTT'], true);
            },
            class: 'text-right'
          },
          {
            label: '',
            width: 100
          }
        ]
      },
    ],
  };
};

export const listHospital = () => [
  'Eye Hospital',
  'General Hospital',
  'MSG HO',
  'MSG LTR',
  'MSG NGT',
  'MSG CT',
  'MSG NT',
  'MSG DKNT',
  'MSG DKPR',
  'MSG VI',
  'MSG DKHT',
  'MSG LA',
  'MSG ND',
  'MSG SH',
  'MSG DKHA',
  'MSG QN',
  'MSG TN',
  'MSG HL',
  'MSG CTE',
  'MSG DN',
  'MSG DKTK',
  'MSG CMTT'
];

export const tabDashboard = () => [
  {
    key: 'financial',
    name: 'Báo cáo tài chính',
    icon: 'BarChart3',
  },
  {
    key: 'summary',
    name: 'Tổng quan',
    icon: 'PieChart'
  },
  {
    key: 'hospital',
    name: 'Bệnh viện',
    icon: 'PieChart'
  }
];
