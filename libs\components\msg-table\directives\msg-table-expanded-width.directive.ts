import { AfterViewInit, Directive, ElementRef, inject, input, On<PERSON><PERSON>roy, Renderer2, signal } from "@angular/core";
import { Observable } from "rxjs";
import { MsgTranslateService } from "../../../services";
import { createResizeObserver$ } from "../../../utils";
import { IMsgObserverResult } from "../../../utils/interfaces/msg-observer.interface";

@Directive({
  standalone: true,
  selector: '[msgTableExpandedWidth], [msg-table-expanded-width]',
})

export class MsgTableExpandedWidthDirective implements AfterViewInit, OnDestroy {
  public msgTableWrapper = input<HTMLElement | undefined>(undefined);

  private _translateService = inject(MsgTranslateService);
  private _elementRef = inject(ElementRef);
  private _renderer = inject(Renderer2);
  private _resizeObserver = signal<IMsgObserverResult<ResizeObserverEntry[]>>({
    observable$: new Observable<ResizeObserverEntry[]>(),
    unsubscribe: () => { }
  });

  ngAfterViewInit(): void {
    const wrapper = this.msgTableWrapper();
    if (!wrapper) {
      return;
    }
    this._resizeObserver.set(createResizeObserver$(wrapper));
    this._resizeObserver().observable$.subscribe(() => {
      this._setExpandedWidth();
    });
  }

  private _setExpandedWidth() {
    const wrapper = this.msgTableWrapper();
    const textEmpty = this._translateService.instant('i18n_no_data');
    const tableElement = this._elementRef.nativeElement;
    const tableHasDivEmpty = tableElement?.querySelector('.msg-table-empty');
    const tableHasEmpty = tableHasDivEmpty || tableElement.textContent.includes(textEmpty);
    if (!wrapper || !tableHasEmpty) {
      return;
    }

    const clientWidth = wrapper.clientWidth;
    const styleList = [
      'position: sticky',
      `width: ${clientWidth}px`,
      'left: 0',
      'overflow: hidden',
    ];

    styleList.forEach(style => {
      this._renderer.setStyle(tableElement, style.split(':')[0], style.split(':')[1]);
    });
  }

  ngOnDestroy(): void {
    this._resizeObserver()?.unsubscribe();
  }
}
