<div class="w-full flex flex-col h-[100dvh]">
  <div class="sticky top-0 z-[20] shrink-0">
    <div class="pl-[40px] h-[45px] bg-white flex items-center justify-between md:px-[16px] text-dark font-semibold text-[13px] border-b border-gray-300 uppercase z-[1000]">
      <div class="truncate max-w-[250px] sm:max-w-[500px]">Quản lý người dùng</div>
    </div>
    <div class="w-full">
      <div class="w-full bg-white py-2 px-[16px] shadow-sm">
        <div class="flex flex-col sm:flex-row sm:flex-wrap sm:justify-end items-end gap-2">
          <div class="flex items-center w-full sm:w-auto gap-2">
            <nz-date-picker nzNoAnimation
              nzFormat="dd/MM/yyyy"
              class="w-full sm:w-[150px] min-w-[150px]" />
            <nz-select nzShowSearch
              class="w-full sm:w-[150px] min-w-[150px]"
              nzAllowClear
              nzPlaceHolder="Chọn site">
              <nz-option nzValue="all"
                nzLabel="Tất cả" />
              <nz-option nzValue="site1"
                nzLabel="Site 1" />
              <nz-option nzValue="site2"
                nzLabel="Site 2" />
            </nz-select>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Card thông số -->
  <div class="grid grid-cols-2 gap-4 p-2 bg-white mx-4 mt-4 rounded-[5px] shadow-md md:flex md:flex-row">
    <nz-card class="w-full md:w-1/4 text-center min-h-[100px] relative">
      @if (loadingCard()) {
        <msg-skeleton-shared [msgNumberRow]="1"
          [class]="'h-[100px] absolute top-0 left-0 right-0 bottom-0'" />
      } @else {
        <div class="text-[12px] text-gray-500">Tổng số user</div>
        <div class="text-[24px] font-bold text-blue-600">1,234</div>
      }
    </nz-card>
    <nz-card class="w-full md:w-1/4 text-center min-h-[100px] relative">
      @if (loadingCard()) {
        <msg-skeleton-shared [msgNumberRow]="1"
          [class]="'h-[100px] absolute top-0 left-0 right-0 bottom-0'" />
      } @else {
        <div class="text-[12px] text-gray-500">User mới hôm qua</div>
        <div class="text-[24px] font-bold text-green-600">56</div>
      }
    </nz-card>
    <nz-card class="w-full md:w-1/4 text-center min-h-[100px] relative">
      @if (loadingCard()) {
        <msg-skeleton-shared [msgNumberRow]="1"
          [class]="'h-[100px] absolute top-0 left-0 right-0 bottom-0'" />
      } @else {
        <div class="text-[12px] text-gray-500">Đang hoạt động</div>
        <div class="text-[24px] font-bold text-orange-600">789</div>
      }
    </nz-card>
    <nz-card class="w-full md:w-1/4 text-center min-h-[100px] relative">
      @if (loadingCard()) {
        <msg-skeleton-shared [msgNumberRow]="1"
          [class]="'h-[100px] absolute top-0 left-0 right-0 bottom-0'" />
      } @else {
        <div class="text-[12px] text-gray-500">User không hoạt động</div>
        <div class="text-[24px] font-bold text-red-600">123</div>
      }
    </nz-card>
  </div>

  <!-- Table báo cáo -->
  <div class="flex-1 min-h-0 m-[16px] bg-white rounded-[5px] shadow-md p-2">
    <msg-table-wrapper-shared [msgConfig]="userTableConfig()"
      [msgData]="userData()"
      [msgLoading]="loading()" />
  </div>
</div>
