/* eslint-disable @typescript-eslint/no-explicit-any */
import { Pipe, PipeTransform } from '@angular/core';
import { IMsgTableColumn } from '../interfaces';

@Pipe({
  name: 'msgTableAttr',
  standalone: true
})
export class MsgTableAttrPipe implements PipeTransform {
  transform<T>(value: IMsgTableColumn['attr'], item: T, index: number, data: T[]): Record<string, any> {
    if (!value) {
      return {};
    }

    return value(item, index, data) || {};
  }
}
