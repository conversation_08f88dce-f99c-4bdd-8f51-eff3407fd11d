import { Directive, ElementRef, inject, input, OnDestroy, OnInit, output } from '@angular/core';
import { OverlayScrollbars, PartialOptions } from 'overlayscrollbars';
@Directive({
  selector: '[msg-overlay-scrollbars], [msgOverlayScrollbars]',
  standalone: true,
})

export class MsgOverlayScrollDirective implements OnInit, OnDestroy {
  private readonly _defaultOptions: PartialOptions = {
    showNativeOverlaidScrollbars: true,
    scrollbars: {
      theme: 'os-theme-dark',
      visibility: 'auto',
      autoHide: 'leave',
      autoHideDelay: 500,
      autoHideSuspend: false,
      dragScroll: true,
      clickScroll: true,
      pointers: ['mouse', 'touch', 'pen'],
    },
  };
  private _msgInstance?: ReturnType<typeof OverlayScrollbars>;
  private _el = inject(ElementRef);

  public msgDefer = input<boolean>(true);
  public msgOptions = input<PartialOptions, PartialOptions>(this._defaultOptions, {
    transform: (value: PartialOptions) => ({ ...this._defaultOptions, ...value })
  });

  public readonly msgElement = output<ElementRef<HTMLElement>>();
  public readonly msgInitialized = output<OverlayScrollbars>();
  public readonly msgUpdated = output<OverlayScrollbars>();
  public readonly msgDestroyed = output<OverlayScrollbars>();
  public readonly msgScroll = output<Event>();

  constructor() { }

  ngOnInit() {
    if (this._el.nativeElement instanceof HTMLElement) {
      const init = () => {
        this._msgInstance = OverlayScrollbars(this._el.nativeElement, this.msgOptions(), {
          initialized: (instance) => this.msgInitialized.emit(instance),
          updated: (instance) => this.msgUpdated.emit(instance),
          destroyed: (instance) => this.msgDestroyed.emit(instance),
          scroll: (instance, event) => this.msgScroll.emit(event),
        });

        this.msgElement.emit(this._el);
      };

      if (!this.msgDefer()) {
        return init();
      }

      if (typeof requestIdleCallback !== 'undefined') {
        return requestIdleCallback(init);
      }

      requestAnimationFrame(init);
    }
  }

  ngOnDestroy() {
    this._msgInstance?.destroy();
  }
}
