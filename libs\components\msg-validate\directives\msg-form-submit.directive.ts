import { contentChildren, Directive, effect, ElementRef, inject, input, Renderer2, RendererStyleFlags2 } from '@angular/core';
import { MsgValidateSharedComponent } from '../msg-validate.component';

@Directive({
  selector: 'form[msg-form-submit], form[msgFormSubmit], form[msgSubmit]',
})
export class MsgFormSubmitDirective {
  public msgSubmit = input<boolean>(true);
  public msgLoading = input<boolean>(false);

  private _validateComponents = contentChildren(MsgValidateSharedComponent, { descendants: true });
  private _element = inject(ElementRef);
  private _renderer = inject(Renderer2);
  constructor() {
    effect(() => {
      const validateComponents = this._validateComponents();
      if (validateComponents.length > 0) {
        this._setMsgSubmit(validateComponents);
      }
    });

    effect(() => {
      const readonly = this.msgLoading();
      this._setReadonlyForElementHasAttribute(readonly);
    });
  }

  private _setMsgSubmit(validateComponents: readonly MsgValidateSharedComponent[]): void {
    validateComponents.forEach((component) => {
      component.setMsgSubmit(this.msgSubmit());
    });
  }

  private _setReadonlyForElementHasAttribute(readonly: boolean): void {
    const elements = this._element.nativeElement.querySelectorAll('input, textarea');
    elements.forEach((element) => {
      if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
        if (readonly === true) {
          this._renderer.setAttribute(element, 'readonly', '');
          this._renderer.setStyle(element, 'pointerEvents', 'none');
          this._renderer.setStyle(element, 'opacity', '0.7');
          this._renderer.setStyle(element, 'color', 'var(--color-gray-400)', RendererStyleFlags2.Important);
          return;

        }

        this._renderer.removeAttribute(element, 'readonly');
        this._renderer.removeStyle(element, 'pointerEvents');
        this._renderer.removeStyle(element, 'opacity');
        this._renderer.removeStyle(element, 'color');
      }
    });
  }
}
