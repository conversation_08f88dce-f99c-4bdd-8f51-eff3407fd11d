import { ChangeDetectionStrategy, Component, input, signal } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { timer } from 'rxjs';
import { MsgButtonSharedComponent } from '../msg-button/msg-button.component';
import { MsgIconSharedComponent } from '../msg-icon/msg-icon.component';
import { IMsgAlertVersionDto } from './interfaces/msg-alert-version.interface';

@Component({
  selector: 'msg-alert-version-shared',
  templateUrl: './msg-alert-version.component.html',
  styleUrls: ['./msg-alert-version.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    MsgButtonSharedComponent,
    TranslatePipe,
    MsgIconSharedComponent,
  ]
})

export class MsgAlertVersionSharedComponent {
  public msgConfig = input<IMsgAlertVersionDto>();
  protected msgEmoji = input<string>('/assets/images/msg-emoji.png');
  protected msgVisible = signal<boolean>(true);
  protected msgLeaving = signal<boolean>(false);

  constructor() { }

  protected onRemindLaterClick(): void {
    this.msgLeaving.set(true);
    timer(400).subscribe(() => this.msgVisible.set(false));
  }
}
