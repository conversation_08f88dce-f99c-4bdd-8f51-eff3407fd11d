<button nz-tooltip
  nz-button
  [type]="msgButtonSubmit() ? 'submit' : 'button'"
  [class]="'relative'"
  [nzType]="'primary'"
  (click)="handlerClick($event)"
  [class]="msgClass() + ' !text-shadow-none'"
  [nzSize]="msgSize()"
  [attr.msgSize]="msgSize()"
  [attr.msgDisableWave]="msgDisableWave()"
  [attr.msgType]="msgType()"
  [attr.msgDisabled]="msgDisabled() || msgLoading()"
  [attr.msgIconOnly]="(!msgText() && (msgIconLeft() || msgIconRight())) ? 'true' : 'false'"
  [nzTooltipTitle]="msgShowTooltip() ? msgTooltipConfig()?.content : null"
  [nzTooltipPlacement]="msgTooltipConfig()?.placement || 'bottom'"
  [nzTooltipTrigger]="msgTooltipConfig()?.trigger || 'hover'">
  @if (msgCount() !== null && msgCount() !== undefined) {
    <div class="msg-button-count-badge">
      <span class="msg-button-count-badge-text">{{
        msgCount()! > msgMaxCount() ? `${msgMaxCount()}+` : msgCount()
      }}</span>
    </div>
  }

  @if (msgText() && !msgIconLeft() && !msgIconRight()) {
    <div class="msg-button-flex-center">
      <span class="msg-button-font-medium">{{ msgText() | translate }}</span>
      @if (msgLoading()) {
        <msg-icon-shared msgName="LoaderCircle"
          [msgStroke]="msgStrokeIcon() || 3"
          [msgSize]="18"
          [msgClass]="msgColorIcon() + ' msg-animate-spin'" />
      }
    </div>
  } @else if (!msgText() && (msgIconLeft() || msgIconRight())) {
    @if (msgLoading()) {
      <div class="msg-button-flex-center">
        <msg-icon-shared msgName="LoaderCircle"
          [msgStroke]="msgStrokeIcon() || 3"
          [msgSize]="18"
          [msgClass]="msgColorIcon() + ' msg-animate-spin'" />
      </div>
    } @else {
      <div class="msg-button-flex-center">
        <msg-icon-shared [msgName]="msgIconRight() || msgIconLeft()!"
          [msgStroke]="msgStrokeIcon() || 2"
          [msgSize]="msgIconSize()"
          [msgClass]="msgColorIcon()" />
      </div>
    }
  } @else if (msgText() && (msgIconLeft() || msgIconRight())) {
    <div class="msg-button-flex-center">
      @if (msgIconLeft()) {
        <msg-icon-shared [msgName]="msgIconLeft()!"
          [msgSize]="msgIconSize()"
          [msgStroke]="msgStrokeIcon() || 2"
          [msgClass]="msgColorIcon()" />
      }
      <span class="msg-button-font-medium">{{ msgText() | translate }}</span>
      @if (msgLoading()) {
        <msg-icon-shared msgName="LoaderCircle"
          [msgStroke]="msgStrokeIcon() || 3"
          [msgSize]="18"
          [msgClass]="msgColorIcon() + ' msg-animate-spin'" />
      } @else if (msgIconRight()) {
        <msg-icon-shared [msgName]="msgIconRight()!"
          [msgSize]="msgIconSize()"
          [msgStroke]="msgStrokeIcon() || 2"
          [msgClass]="msgColorIcon()" />
      }
    </div>
  } @else {
    <div class="msg-button-flex-center">
      <ng-content />
    </div>
  }
</button>
