<!-- Overlay mask cho mobile/tablet - ĐỂ NGOÀI CONTAINER -->
<div class="msg-sidebar-overlay"
  [class.show]="!msgCollapsed()"
  (click)="handleOverlayClick($event)">
</div>

<div class="msg-sidebar-container"
  msg-click-outside
  [class.collapsed]="msgCollapsed()"
  (msgClickOutside)="handleClickOutside()">

  <!-- Content chỉ hiển thị khi expanded -->
  <div class="msg-sidebar-content"
    [class.collapsed]="msgCollapsed()">
    <!-- Header Menu -->
    <div [class.collapsed]="msgCollapsed()"
      class="msg-sidebar-header">
      <img [src]="msgCollapsed() && msgIsDesktop() ? msgLogoMobile(): msgLogo()"
        [alt]="'Logo'"
        class="msg-logo">
      <div class="msg-sidebar-toggle-desktop">
        <msg-button-shared class="msg-toggle-btn"
          [msgType]="'outline'"
          [msgIconRight]="msgCollapsed() ? 'AlignJustify' : 'ChevronsLeft'"
          [msgIconSize]="20"
          [msgClass]="'text-white !bg-white !border-white !shadow-none !p-0 !m-0'"
          (msgClick)="onToggleSidebar()" />
      </div>
    </div>

    <!-- Menu Items -->
    <div class="msg-menu-list-container">
      <div class="msg-menu-list"
        [class.justify-center]="msgCollapsed()">
        @for (menu of msgMenusProcess(); track menu.id) {
          <msg-menu-item [msgMenuItem]="menu"
            class="msg-menu-item"
            [class.mt-1]="!$first && !menu.isCategory"
            [class.collapsed]="msgCollapsed()"
            [attr.isCategoryPadding]="menu.isCategory && !$first && !msgCollapsed()"
            [attr.isCategory]="menu.isCategory"
            [msgCollapsed]="msgCollapsed()"
            [msgMenuChangeUpdateParent]="msgMenuChangeUpdateParent()"
            (msgMenuActive)="handlerMenuActive($event)" />
        }
      </div>
    </div>
  </div>

  <!-- Toggle Button -->
  <div class="msg-sidebar-toggle-tablet">
    <msg-button-shared class="msg-toggle-btn"
      [msgType]="'outline'"
      [msgIconRight]="msgCollapsed() ? 'AlignJustify' : 'ChevronsLeft'"
      [msgIconSize]="20"
      [msgClass]="'text-white !bg-white !border-white'"
      (msgClick)="onToggleSidebar()" />
  </div>

</div>
