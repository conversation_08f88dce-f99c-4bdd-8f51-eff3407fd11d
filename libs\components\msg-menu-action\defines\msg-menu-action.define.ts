import { IMsgMenuActionItemDto } from "../interfaces/msg-menu-action.interface";

export const menuActionDefine = (): IMsgMenuActionItemDto[] => {
  return [
    {
      type: 'edit',
      label: 'i18n_edit',
      icon: 'Edit',
      hoverClass: 'hover:bg-blue-100'
    },
    {
      type: 'delete',
      label: 'i18n_delete',
      icon: 'Trash2',
      hoverClass: 'hover:bg-red-100'
    }
  ];
};
