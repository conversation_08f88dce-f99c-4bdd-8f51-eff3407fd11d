@use "responsive" as *;

.msg-reset-password-container {
  background: url('/assets/images/msg-login-bg-3.png') no-repeat center center fixed;
  background-size: cover;
  background-attachment: fixed;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: end;
  align-items: center;
}

.msg-reset-password-form {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.5);
  padding: 40px;
  animation: slideInFromRight 0.5s ease;
  width: 450px;

  @include respond-to-max('tablet') {
    animation: none;
    width: 100%;
    padding: 130px;


    @include respond-to-max('mobile') {
      padding: 40px;
    }
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(40px);
    opacity: 0;
  }

  100% {
    transform: translateX(0);
    opacity: 1;
  }
}


// @keyframes slideInFromLeft {
//   0% {
//     transform: translateX(-40px);
//     opacity: 0;
//   }

//   100% {
//     transform: translateX(0);
//     opacity: 1;
//   }
// }
