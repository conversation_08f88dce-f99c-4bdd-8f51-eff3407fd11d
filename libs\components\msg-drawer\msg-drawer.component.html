<!-- Overlay -->
@if (msgShowOverlay()) {
  <div [ngClass]="overlayClasses()"
    (click)="handleOverlayClick($event)"></div>
}

<!-- Drawer Container -->
<div #drawerElement
  [ngClass]="drawerClasses()"
  [ngStyle]="drawerStyles()">
  <!-- Header -->
  <ng-content select="[msg-drawer-header]">
    <div class="msg-drawer-header">
      @let msgWidthTitle = msgWidth() - 150;
      <div class="msg-drawer-title"
        nz-tooltip
        [nzTooltipTitle]="((msgTitleElement | msgTruncate | async)) ? msgTitle() : ''"
        [nzTooltipTrigger]="'hover'"
        [nzTooltipPlacement]="'top'">
      <h2 #msgTitleElement
        [style.max-width.px]="msgWidthTitle"
        [class]="'truncate'">{{ msgTitle() | translate }}</h2>
    </div>
    <div class="msg-drawer-actions">
      <msg-button-shared [msgType]="'text'"
        [msgClass]="'msg-drawer-close'"
        [msgIconRight]="'X'"
        [msgIconSize]="20"
        (msgClick)="handlerCloseDrawer($event)">
      </msg-button-shared>
    </div>
</div>
</ng-content>

<!-- Content -->
<div class="msg-drawer-content">
  <ng-content select="[msg-drawer-content]">
    <!-- <ng-content select="[msg-drawer-content]"></ng-content> -->
    <div class="msg-drawer-content-placeholder">
      <h3>Drawer Content</h3>
      <p>Please add your content here using content projection.</p>
    </div>
  </ng-content>
</div>

<!-- Footer -->
<ng-content select="[msg-drawer-footer]">
  <div class="msg-drawer-footer">
    <div class="msg-drawer-footer-actions">
      <msg-button-shared [msgText]="'i18n_cancel' | translate"
        [msgStrokeIcon]="3"
        [msgType]="'outline-secondary'"
        [msgClass]="'min-w-[70px]'"
        (msgClick)="handlerCloseDrawer($event)" />
      <msg-button-shared [msgText]="'i18n_save' | translate"
        [msgStrokeIcon]="3"
        [msgClass]="'min-w-[70px]'"
        (msgClick)="handlerSaveChanges($event)" />
    </div>
  </div>
</ng-content>

</div>
