import { InjectionToken } from '@angular/core';
import { IMsgNotificationConfigDto } from '../components/msg-notification/interfaces/msg-notification.interface';
export const MSG_NOTIFICATION_CONFIG = new InjectionToken<IMsgNotificationConfigDto>('MSG_NOTIFICATION_CONFIG');

export function provideMsgNotification(config: Partial<IMsgNotificationConfigDto> = {}) {
  const defaultConfig: IMsgNotificationConfigDto = {
    position: 'bottom-right',
    duration: 2000,
    showProgressBar: true,
    maxVisible: 6,
    preventDuplicate: true
  };

  return {
    provide: MSG_NOTIFICATION_CONFIG,
    useValue: {
      ...defaultConfig,
      ...config
    }
  };
}
