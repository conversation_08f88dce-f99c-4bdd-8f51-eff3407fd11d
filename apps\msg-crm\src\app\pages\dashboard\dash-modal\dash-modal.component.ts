import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, input, OnInit, output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MsgDrawerSharedComponent } from '@libs/components/msg-drawer';
import { NzInputModule } from 'ng-zorro-antd/input';

@Component({
  selector: 'msg-dash-modal',
  templateUrl: './dash-modal.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MsgDrawerSharedComponent,
    NzInputModule,
  ]
})
export class MsgDashModalComponent implements OnInit {
  public dashItem = input<any>();
  public dashTitle = input<string>('');
  public msgClose = output<void>();
  public msgOk = output<void>();

  ngOnInit(): void {
    console.log('DashModalComponent initialized with item:', this.dashItem());
  }

  protected handlerOkModal(): void {
    this.msgOk.emit();
  }

  protected handleAfterClose(): void {
    this.msgClose.emit();
  }
}
