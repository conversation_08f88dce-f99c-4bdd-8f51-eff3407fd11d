@if (msgVisible() || msgLeaving()) {
  <div class="msg-alert-version absolute top-[24px] right-[16px] z-[1000] rounded-[5px] p-4 shadow-uniform-md min-w-[400px] min-h-[200px] bg-white"
    [class.msg-alert-version-enter]=" msgVisible() && !msgLeaving()"
    [class.msg-alert-version-leave]="msgLeaving()">
    <div class="relative">
      <img class="absolute top-[-50px] right-[-45px] w-[150px] h-[150px] z-[-1]"
        [src]="msgEmoji()"
        loading="eager"
        alt="Msg Emoji" />
      <div class="flex items-center gap-2">
        <msg-icon-shared [msgName]="'Box'"
          [msgSize]="18"
          [msgClass]="'text-green-500'" />
        <div>
          <span class="text-[12px] uppercase font-semibold text-green-600">{{ 'i18n_msg_version' | translate: { version: msgConfig()?.version } }}</span>
        </div>
      </div>
      <div class="mt-2 text-sm text-gray-500">
        <div class="flex items-center gap-2 mb-1">
          <msg-icon-shared [msgName]="'Zap'"
            [msgSize]="18"
            [msgClass]="'text-blue-500'" />
          <div class="text-[13px] font-[450] !text-gray-900">{{ 'i18n_new_feature' | translate }}</div>
        </div>
        <div class="ml-2">
          <ul class="list-disc list-inside">
            <li class="text-gray-800 text-[12px]">{{ 'Bổ sung tính năng xuất báo cáo fis dashboard' }}</li>
            <li class="text-gray-800 text-[12px]">{{ 'Bổ sung tuỳ biến themes dark/light' | translate }}</li>
            <li class="text-gray-800 text-[12px]">{{ 'Bổ sung tính năng mới cho fis dashboard' | translate }}</li>
          </ul>
        </div>

        <div class="flex items-center gap-2 mb-1">
          <msg-icon-shared [msgName]="'Cog'"
            [msgSize]="18"
            [msgClass]="'text-red-500'" />
          <div class="text-[13px] font-[450] !text-gray-900">{{ 'i18n_new_fix_bug' | translate }}</div>
        </div>
        <div class="ml-2">
          <ul class="list-disc list-inside">
            <li class="text-gray-800 text-[12px]">{{ 'Fix lỗi không xuất được báo cáo fis dashboard' | translate }}</li>
            <li class="text-gray-800 text-[12px]">{{ 'Fix lỗi không hiển thị dữ liệu màn hình crm' | translate }}</li>
            <li class="text-gray-800 text-[12px]">{{ 'Fix lỗi không đổi được mật khẩu user' | translate }}</li>
          </ul>
        </div>
        <div class="flex justify-center items-center w-full mt-4">
          <msg-button-shared [msgText]="'i18n_msg_remind_later'"
            [msgType]="'primary'"
            [msgIconLeft]="'BellMinus'"
            [msgClass]="'w-50'"
            (msgClick)="onRemindLaterClick()" />
        </div>
      </div>
    </div>
  </div>
}
