@use "animations" as *;
@use "responsive" as *;

.msg-menu-item {
  &-category {
    &-name {
      text-transform: uppercase;
      font-size: 10px;
      padding: 4px 8px;
      font-weight: 600;
      color: var(--color-green-600);
      text-wrap: nowrap;
      white-space: nowrap;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    @include respond-to('tablet') {
      &.collapsed {
        display: none;
      }
    }
  }

  &.expanded {}

  &-icon-svg {
    mask-size: contain;
    mask-position: center;
    mask-repeat: no-repeat;
    -webkit-mask-size: contain;
    -webkit-mask-position: center;
    -webkit-mask-repeat: no-repeat;
    width: 25px;
    height: 25px;
    flex-shrink: 0;
  }

  .msg-menu-arrow-collapsed {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
    flex-shrink: 0;

    @include respond-to('tablet') {
      &.collapsed {
        opacity: 0;
        transform: translateX(10px);
        width: 0;
        pointer-events: none;
      }
    }
  }

  .msg-menu-link {
    position: relative;
    border-radius: 4px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 8px;
    gap: 12px;
    min-height: 30px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: var(--color-green-500);

    &.active {
      background-color: var(--color-green-200) !important;
    }

    &:hover:not(.active):not(.active-desktop) {
      background-color: var(--color-green-100) !important;
    }

    @include respond-to('tablet') {
      &.collapsed {
        padding: 0;
        justify-content: center;
        gap: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      &.active-desktop {
        color: var(--color-gray-900);
        background-color: var(--color-green-200) !important;

        span.msg-menu-item-name {
          color: var(--color-gray-900) !important;
        }
      }
    }

    .msg-menu-icon-container {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 16px;

      &.flex-1 {
        flex: 1;
      }

      &.collapsed-padding {
        @include respond-to('tablet') {
          padding-left: 0 !important;
        }
      }
    }

    .msg-menu-item-name {
      white-space: nowrap;
      overflow: hidden;
      text-wrap: nowrap;
      opacity: 1;
      transform: translateX(0);
      transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out, color 0.2s ease-in-out;
      flex: 1;
      font-weight: 450;
      font-size: 12px;
      line-height: 1.25;
      color: var(--color-gray-700);
      text-overflow: ellipsis;

      &.text-dark {
        color: var(--color-gray-900) !important;
      }

      &.text-green {
        color: var(--color-green-600) !important;
      }

      @include respond-to('tablet') {
        &.collapsed {
          opacity: 0;
          transform: translateX(-10px);
          width: 0;
          pointer-events: none;
          display: none;
        }
      }
    }
  }

  .msg-icon-shared {
    &.text-gray {
      color: var(--color-gray-900);
    }

    &.text-green {
      color: var(--color-green-600) !important;
    }

    &.text-gray-700 {
      color: var(--color-gray-700);
    }
  }

  .msg-submenu-content {
    padding-left: 4px;
    display: flex;
    flex-direction: column;
    margin-left: 5px !important;
  }

  .msg-submenu-container {
    margin-left: 10px;
    position: relative;
    @include grid-expand();

    &.expanded {
      grid-template-rows: 1fr;
    }

    &.collapsed {
      grid-template-rows: 0fr;
    }

    @include respond-to('tablet') {
      &.msg-submenu-container-collapsed {
        display: none;
      }

      &[hideMenuChild="true"] {
        display: none;
      }
    }

    .msg-submenu-border-vertical {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 5px;
      border-left: 1px solid var(--color-gray-200);
      margin-top: 4px;
    }
  }

  .msg-menu-item-child {
    margin-top: 4px;
  }

  .msg-submenu-icon {
    @include rotate();

    .expanded & {
      transform: rotate(90deg);
    }
  }

  .msg-animate-rotate-clockwise,
  msg-icon-shared.msg-animate-rotate-clockwise {
    @include rotate();
    transform: rotate(90deg);
  }

  .msg-animate-rotate-default,
  msg-icon-shared.msg-animate-rotate-default {
    @include rotate();
    transform: rotate(0deg);
  }
}

.msg-popover-submenu {
  width: 250px;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 6px 0 6px 6px;
  max-height: 450px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .menu-title {
    border-bottom: 1px solid var(--color-gray-200);
    padding-bottom: 4px;
    background-color: white;
    padding-right: 6px;

    &.menu-title-sticky {
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .menu-title-row {
      display: flex;
      align-items: center;
      // padding: 8px 10px;
      padding: 4px 8px;
      border-radius: 4px;

      .menu-title-content {
        display: grid;
        align-items: center;
        gap: 8px;
        width: 100%;

        .menu-title-text {
          font-size: 12px;
          font-weight: 450;
          color: var(--color-green-600);
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }

  .menu-items-container {
    &.menu-items-scrollable {
      overflow-y: auto;
      flex: 1;
      padding-top: 4px;
      padding-right: 6px;

      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--color-gray-300);
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: var(--color-gray-400);
      }
    }
  }
}

.menu-item-row {
  margin-top: 4px;

  .menu-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: color 0.2s ease-in-out;
    padding: 0 8px;
    min-height: 30px;
    border-radius: 4px;

    &:hover:not(.active) {
      background-color: var(--color-green-100) !important;
    }

    &.active {
      background-color: var(--color-green-200) !important;
    }

    .menu-item-info {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 0;
      flex: 1;

      .menu-item-text {
        font-size: 12px;
        text-overflow: ellipsis;
        overflow: hidden;
        color: var(--color-gray-700);
        font-weight: 450;
      }
    }

    .menu-item-arrow {
      transition: transform 0.2s ease-in-out;
      flex-shrink: 0;

      &.rotated {
        transform: rotate(90deg);
      }
    }
  }

  .msg-submenu-container {
    margin-left: 17px;
    position: relative;
    @include grid-expand();

    &.expanded {
      grid-template-rows: 1fr;
    }

    &.collapsed {
      grid-template-rows: 0fr;
    }

    .msg-submenu-border-vertical {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      border-left: 1px solid var(--color-gray-200);
      margin-top: 4px;
    }

    .msg-submenu-content {}
  }

  .menu-item-row {
    padding-left: 4px;
  }
}

::ng-deep {

  .msg-popover-content-menu-item.ant-popover-placement-right,
  .msg-popover-content-menu-item.ant-popover-placement-rightTop,
  .msg-popover-content-menu-item.ant-popover-placement-rightBottom {
    margin-left: 10px !important;
  }
}
