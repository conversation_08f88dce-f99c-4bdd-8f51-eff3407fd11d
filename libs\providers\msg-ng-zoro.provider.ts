import { BidiModule } from '@angular/cdk/bidi';
import { provideNzI18n, vi_VN } from 'ng-zorro-antd/i18n';
import { DefineConstant } from '../constants/msg-common.define';


export function provideNgZorroAntd(lang?: string) {
  const savedLang = typeof window !== 'undefined' ? localStorage.getItem('msg-language') : null;
  const selectedLang = lang || savedLang || 'vi';
  const selectedLanguage = DefineConstant.MSG_LANGUAGE[selectedLang as keyof typeof DefineConstant.MSG_LANGUAGE] || vi_VN;

  return [
    provideNzI18n(selectedLanguage),
    BidiModule,
  ];
}
