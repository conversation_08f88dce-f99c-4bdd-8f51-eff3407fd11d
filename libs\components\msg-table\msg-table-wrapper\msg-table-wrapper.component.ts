/* eslint-disable @typescript-eslint/no-explicit-any */
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input, output, signal, viewChild } from "@angular/core";
import { TranslatePipe } from '@ngx-translate/core';
import { MsgButtonSharedComponent } from '../../msg-button/msg-button.component';
import { MsgTableExpandedWidthDirective } from '../directives/msg-table-expanded-width.directive';
import { MsgTableDirective } from '../directives/msg-table.directive';
import { IMsgTableColumn, IMsgTableConfig, IMsgTableExpandMap, IMsgTableFunctionControl, IMsgTableHeader, IMsgTablePagination } from "../interfaces/msg-table.interfaces";
import { MsgTableSharedComponent } from "../msg-table.component";
import { MsgTableAttrPipe } from '../pipes/msg-table-attr.pipe';
import { MsgTableCellStylePipe } from '../pipes/msg-table-cell-style.pipe';
import { MsgTableRowColSpanPipe } from '../pipes/msg-table-row-col-span.pipe';
import { MsgTableRowStylePipe } from '../pipes/msg-table-row-style.pipe';
import { MsgTableTemplatePipe } from '../pipes/msg-table-template.pipe';

@Component({
  selector: 'msg-table-wrapper-shared',
  templateUrl: './msg-table-wrapper.component.html',
  styleUrls: ['./msg-table-wrapper.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TranslatePipe,
    NgTemplateOutlet,
    MsgTableSharedComponent,
    MsgTableDirective,
    MsgTableTemplatePipe,
    MsgButtonSharedComponent,
    MsgTableRowColSpanPipe,
    MsgTableAttrPipe,
    MsgTableRowStylePipe,
    MsgTableCellStylePipe,
    MsgTableExpandedWidthDirective,
  ],
  // NzToolTipModule,
  // MsgTruncatePipe,
  // AsyncPipe
})

export class MsgTableWrapperSharedComponent {
  public msgData = input<any[]>([]);
  public msgDataFooter = input<any[]>([]);
  public msgConfig = input<IMsgTableConfig>();
  public msgLoading = input<boolean>(false);
  public msgEmpty = input<boolean>(false);

  public msgPageChange = output<IMsgTablePagination>();
  public msgFunctionControl = output<IMsgTableFunctionControl>();

  protected table = viewChild.required<MsgTableSharedComponent>('tableRef');
  protected fieldKey = computed(() => this._computeFieldKey());
  protected leafColumns = computed(() => this._computeLeafColumns());
  protected headerRows = computed(() => this._computeHeaderRows());
  protected footerRows = computed(() => this._computeFooterRows());
  protected showExpanded = signal<Map<number, IMsgTableExpandMap>>(new Map());
  protected checkboxHeaderStates = signal<boolean | undefined>(undefined);
  protected tableWrapper = computed(() => this.table().tableWrapper?.nativeElement);

  constructor() { }
  protected onPageChange(event: IMsgTablePagination): void {
    this.msgPageChange.emit(event);
  }

  protected onFunctionControl(event: IMsgTableFunctionControl): void {
    this.msgFunctionControl.emit(event);
  }

  protected onCheckboxChange(col: IMsgTableHeader, item: any, event: Event, index: number): void {
    const checked = (event.target as HTMLInputElement).checked;
    item['checked'] = checked;
    col.onCheckChange?.(item, checked, index);
    this._computeCheckboxHeaderStates();
  }

  protected onExpandClick(col: IMsgTableHeader, item: any, index: number, tdRef: Element): void {
    const rowSpan = parseInt(tdRef.getAttribute('rowspan') || '1', 10);
    item['expand'] = !item['expand'];
    const expandedIndex = index + rowSpan - 1;
    if (item['expand']) {
      const template = col.expandedTemplate ? col.expandedTemplate(item, index) : null;
      const expanded = {
        index: expandedIndex,
        template: typeof template === 'object' ? template : null,
        content: typeof template === 'string' ? template : null,
        item: item,
      };

      col.onExpandChange?.(item, true, index);
      this.showExpanded.update(map => {
        const newMap = new Map(map);
        newMap.set(expandedIndex, expanded);
        return newMap;
      });

      return;
    }

    col.onExpandChange?.(item, false, index);
    this.showExpanded.update(map => {
      const newMap = new Map(map);
      newMap.delete(expandedIndex);
      return newMap;
    });
  }

  protected onCheckAllChange(col: IMsgTableHeader, event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    col.onCheckAll?.(checked, this.msgData());
    this.msgData().forEach((item) => {
      item['checked'] = checked;
    });

    this._computeCheckboxHeaderStates();
  }

  private _computeFieldKey(): string {
    return this.msgConfig()?.fieldKey || 'stt';
  }

  private _computeLeafColumns(): IMsgTableHeader[] {
    return this._getLeafColumns(this.msgConfig()?.header || []);
  }

  private _computeHeaderRows(): IMsgTableColumn[][] {
    return this._buildHeaderRows(this.msgConfig()?.header || []);
  }

  private _computeFooterRows(): IMsgTableColumn[][] {
    const footerColumns = this.msgConfig()?.footer;
    if (!footerColumns?.length) { return []; }
    if (Array.isArray(footerColumns) && Array.isArray(footerColumns[0])) {
      return footerColumns as IMsgTableColumn[][];
    }
    if (Array.isArray(footerColumns)) {
      return this._buildFooterRows(footerColumns as IMsgTableColumn[]);
    }
    return [];
  }

  private _computeCheckboxHeaderStates(): void {
    const hasPagination = !!this.msgConfig()?.pagination?.showPagination;
    const items = hasPagination ? this.table().paginatedData : this.msgData();
    const allChecked = items.every(item => item['checked']);
    this.checkboxHeaderStates.set(allChecked);
  }

  private _buildHeaderRows(columns: IMsgTableColumn[]): IMsgTableColumn[][] {
    if (!columns.length) { return []; }
    const maxDepth = this._getMaxDepth(columns);
    const rows: IMsgTableColumn[][] = Array.from({ length: maxDepth }, () => []);
    this._fillHeaderRows(columns, rows, 0, maxDepth);
    return rows;
  }

  private _buildFooterRows(footerColumns: IMsgTableColumn[]): IMsgTableColumn[][] {
    if (!footerColumns.length) { return []; }
    const maxDepth = this._getMaxDepthFooter(footerColumns);
    const rows: IMsgTableColumn[][] = Array.from({ length: maxDepth }, () => []);
    this._fillFooterRows(footerColumns, rows, 0, maxDepth);
    return rows;
  }

  private _getMaxDepth(columns: IMsgTableHeader[]): number {
    return Math.max(...columns.map(col => col.children?.length ? 1 + this._getMaxDepth(col.children) : 1));
  }

  private _getMaxDepthFooter(columns: IMsgTableColumn[]): number {
    return Math.max(...columns.map(col => col.children?.length ? 1 + this._getMaxDepthFooter(col.children) : 1));
  }

  private _fillHeaderRows(columns: IMsgTableColumn[], rows: IMsgTableColumn[][], currentDepth: number, maxDepth: number): void {
    for (const col of columns) {
      const colCopy = { ...col };
      if (!col.children?.length) {
        colCopy.rowSpan = maxDepth - currentDepth;
        colCopy.colSpan = 1;
        rows[currentDepth].push(colCopy);
        continue;
      }
      colCopy.colSpan = this._countLeafColumns([col]);
      colCopy.rowSpan = 1;
      rows[currentDepth].push(colCopy);
      this._fillHeaderRows(col.children, rows, currentDepth + 1, maxDepth);
    }
  }

  private _fillFooterRows(columns: IMsgTableColumn[], rows: IMsgTableColumn[][], currentDepth: number, maxDepth: number): void {
    for (const col of columns) {
      const colCopy = { ...col };
      if (!col.children?.length) {
        colCopy.rowSpan = maxDepth - currentDepth;
        colCopy.colSpan = 1;
        rows[currentDepth].push(colCopy);
        continue;
      }
      colCopy.colSpan = this._countLeafColumnsFooter([col]);
      colCopy.rowSpan = 1;
      rows[currentDepth].push(colCopy);
      this._fillFooterRows(col.children, rows, currentDepth + 1, maxDepth);
    }
  }

  private _getLeafColumns(columns: IMsgTableColumn[]): IMsgTableColumn[] {
    const leafColumns: IMsgTableColumn[] = [];
    for (const col of columns) {
      if (!col.children?.length) {
        leafColumns.push(col);
        continue;
      }

      leafColumns.push(...this._getLeafColumns(col.children));
    }
    return leafColumns;
  }

  private _countLeafColumns(columns: IMsgTableColumn[]): number {
    return columns.reduce((count, col) => count + (col.children?.length ? this._countLeafColumns(col.children) : 1), 0);
  }

  private _countLeafColumnsFooter(columns: IMsgTableColumn[]): number {
    return columns.reduce((count, col) => count + (col.children?.length ? this._countLeafColumnsFooter(col.children) : 1), 0);
  }
}
