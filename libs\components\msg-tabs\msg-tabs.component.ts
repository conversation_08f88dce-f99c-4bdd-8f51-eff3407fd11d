import { AfterViewChecked, ChangeDetectionStrategy, Component, ElementRef, OnInit, Renderer2, computed, effect, inject, input, output, signal, viewChild } from "@angular/core";
import { MsgIconSharedComponent } from '../msg-icon/msg-icon.component';
import { IMsgTabDto, IMsgTabFunctionControl, TYPE_MSG_TAB, TYPE_MSG_TAB_STYLE } from './interfaces/msg-tabs.interface';

@Component({
  selector: 'msg-tabs-shared',
  templateUrl: './msg-tabs.component.html',
  styleUrls: ['./msg-tabs.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    MsgIconSharedComponent,
  ],
})
export class MsgTabsSharedComponent implements AfterViewChecked, OnInit {
  public msgTabs = input<IMsgTabDto[]>([]);
  public msgTabType = input<TYPE_MSG_TAB>('horizontal');
  public msgTabStyle = input<TYPE_MSG_TAB_STYLE>('border');
  public msgActiveTab = input<string | null>(null);

  public msgTabChange = output<IMsgTabDto>();
  public msgFunctionControl = output<IMsgTabFunctionControl>();
  public msgTabsChange = output<IMsgTabDto[]>();

  private _tabsContainer = viewChild.required<ElementRef<HTMLElement>>('tabsContainer');
  private _tabIndicator = viewChild.required<ElementRef<HTMLElement>>('tabIndicator');
  private _renderer2 = inject(Renderer2);

  protected activeTabIndex = signal<number>(0);
  protected hoveredTabIndex = signal<number>(-1);
  protected slideDirection = signal<'left' | 'right' | null>(null);
  private _selectedByClick = signal<boolean>(false);

  protected computedTabs = computed(() => {
    const tabs = this.msgTabs();
    const activeTabKey = this.msgActiveTab();
    const currentActiveIndex = this.activeTabIndex();
    const isClickSelected = this._selectedByClick();

    return tabs.map((tab, index) => ({
      ...tab,
      active: isClickSelected ? index === currentActiveIndex : (activeTabKey ? tab.key === activeTabKey : index === currentActiveIndex)
    }));
  });

  private _activeTabIndex = signal<number>(0);
  private _prevActiveIndex = -1;

  constructor() {
    effect(() => {
      const activeTab = this.computedTabs().find(tab => tab.active);
      if (activeTab) {
        const index = this.computedTabs().indexOf(activeTab);
        this._activeTabIndex.set(index);
        this._updateIndicatorPosition(index);
      }
    });

    effect(() => {
      const activeTabKey = this.msgActiveTab();
      const tabs = this.msgTabs();

      if (activeTabKey && !this._selectedByClick()) {
        const tabIndex = tabs.findIndex(tab => tab.key === activeTabKey);
        if (tabIndex > -1 && !tabs[tabIndex].disabled) {
          const oldIndex = this.activeTabIndex();
          this.slideDirection.set(tabIndex > oldIndex ? 'right' : 'left');
          this.activeTabIndex.set(tabIndex);
        }
      }
    });

    this.msgFunctionControl.emit({
      setDisabled: this._setTabDisabled.bind(this),
      setActive: this._setActiveTab.bind(this),
      setCountBadge: this._setTabCountBadge.bind(this)
    });
  }

  ngOnInit(): void {
    this._emitFirstTabChange();
  }

  ngAfterViewChecked(): void {
    if (this._prevActiveIndex !== this._activeTabIndex()) {
      this._prevActiveIndex = this._activeTabIndex();
      this._updateIndicatorPosition(this._activeTabIndex());
    }
  }

  private _emitFirstTabChange(): void {
    if (!this.computedTabs().length) {
      return;
    }

    if (!this.msgActiveTab()) {
      this.msgTabChange.emit(this.computedTabs()[0]);
      return;
    }

    const activeTab = this.computedTabs().find(tab => this.msgActiveTab() === tab.key);
    this.msgTabChange.emit(activeTab || this.computedTabs()[0]);
  }

  protected handleTabClick(tab: IMsgTabDto, index: number): void {
    if (tab.disabled) {
      return;
    }
    const oldIndex = this.activeTabIndex();
    this.slideDirection.set(index > oldIndex ? 'right' : 'left');
    this._selectedByClick.set(true);
    this.activeTabIndex.set(index);
    this.msgTabChange.emit(tab);
  }

  protected handleTabHover(index: number): void {
    this.hoveredTabIndex.set(index);
  }

  protected handleTabLeave(): void {
    this.hoveredTabIndex.set(-1);
  }

  private _updateIndicatorPosition(activeIndex: number): void {
    const tabElements = this._tabsContainer().nativeElement.querySelectorAll('.msg-tab-item');
    const activeTab = tabElements[activeIndex] as HTMLElement;
    const indicator = this._tabIndicator().nativeElement;

    if (activeTab) {
      const { offsetLeft, offsetTop, offsetWidth, offsetHeight } = activeTab;

      if (this.msgTabType() === 'horizontal') {
        this._renderer2.setStyle(indicator, 'width', `${offsetWidth}px`);
        this._renderer2.setStyle(indicator, 'height', '2.5px');
        this._renderer2.setStyle(indicator, 'transform', `translateX(${offsetLeft}px)`);
        return;
      }

      this._renderer2.setStyle(indicator, 'width', '2.5px');
      this._renderer2.setStyle(indicator, 'height', `${offsetHeight}px`);
      this._renderer2.setStyle(indicator, 'transform', `translateY(${offsetTop}px)`);
    }
  }

  private _setTabDisabled(tabKey: string, disabled: boolean): void {
    const tabs = [...this.msgTabs()];
    const tabIndex = tabs.findIndex(t => t.key === tabKey);

    if (tabIndex > -1) {
      tabs[tabIndex] = { ...tabs[tabIndex], disabled };
      this.msgTabsChange.emit(tabs);
    }
  }

  private _setActiveTab(tabKey: string): void {
    const tabs = this.msgTabs();
    const tabIndex = tabs.findIndex(t => t.key === tabKey);

    if (tabIndex > -1 && !tabs[tabIndex].disabled) {
      this._selectedByClick.set(false);
      this.activeTabIndex.set(tabIndex);
      this.msgTabChange.emit(tabs[tabIndex]);
    }
  }

  private _setTabCountBadge(tabKey: string, count: number): void {
    const tabs = [...this.msgTabs()];
    const tabIndex = tabs.findIndex(t => t.key === tabKey);

    if (tabIndex > -1) {
      tabs[tabIndex] = { ...tabs[tabIndex], countBadge: count };
      this.msgTabsChange.emit(tabs);
    }
  }
}
