import { Route } from '@angular/router';

export const layoutRoutes: Route[] = [
  {
    path: '',
    loadComponent: () => import('./layouts.component').then(m => m.MsgLayoutsComponent),
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      },
      {
        path: 'login',
        loadComponent: () => import('../pages/auth/login/login.component').then(m => m.MsgLoginComponent)
      },
      {
        path: 'reset-password',
        loadComponent: () => import('../pages/auth/reset-password/reset-password.component').then(m => m.MsgResetPasswordComponent)
      },
      {
        path: '',
        loadChildren: () => import('./main/main.routes').then(m => m.mainRoutes)
      },

    ]
  }
];
