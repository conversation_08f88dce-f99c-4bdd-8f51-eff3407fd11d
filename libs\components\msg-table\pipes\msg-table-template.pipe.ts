/* eslint-disable @typescript-eslint/no-explicit-any */
import { inject, Pipe, PipeTransform, TemplateRef } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { IMsgTableColumn } from '../interfaces/msg-table.interfaces';

@Pipe({
  name: 'msgTableTemplate',
  standalone: true
})
export class MsgTableTemplatePipe implements PipeTransform {
  private _sanitizer = inject(DomSanitizer);
  private readonly _htmlRegex = /&[a-z]+;|&#\d+;|<[a-z]+[^>]*>/i;

  transform<T>(col: IMsgTableColumn<T>, item: T, defaultTemplate: TemplateRef<T>, index: number, data: T[], type?: 'template'): TemplateRef<T> | null;
  transform<T>(col: IMsgTableColumn<T>, item: T, defaultTemplate: TemplateRef<T>, index: number, data: T[], type?: 'html'): string;
  transform<T>(col: IMsgTableColumn<T>, item: T, defaultTemplate: TemplateRef<T>, index: number, data: T[], type?: 'text'): string;
  transform<T>(col: IMsgTableColumn<T>, item: T, defaultTemplate: TemplateRef<T>, index: number, data: T[], type?: 'template' | 'html' | 'text' | undefined) {
    if (!col.value) {
      return type === 'template' ? null : '';
    }

    const value = typeof col.value === 'string'
      ? (item as any)[col.value]
      : col.value(item, index, data);

    if (type === 'template') {
      return value instanceof TemplateRef ? value : null;
    }

    if (type === 'html') {
      if (typeof value === 'string' && this._htmlRegex.test(value)) {
        return this._sanitizer.bypassSecurityTrustHtml(value).toString();
      }

      return '';
    }

    return value?.toString() || '';
  }
}
