<msg-popover-shared [msgPlacement]="'bottomRight'"
  [msgTrigger]="'click'"
  [msgContent]="popoverContent"
  (msgVisibilityChange)="onVisibilityChange($event)">
  <msg-button-shared [msgIconRight]="'BellRing'"
    [msgType]="'text'"
    [msgSize]="'small'"
    [msgClass]="'w-full text-xs'" />
</msg-popover-shared>

<ng-template #popoverContent>
  <div class="w-[280px] bg-white rounded-[5px] z-50 overflow-auto flex flex-col gap-[8px]"
    [class]="msgLoading() ? 'h-[calc(100vh*0.4)]' : 'h-[calc(100vh*0.6)]'">
    @if (msgLoading()) {
      <div class="flex items-center justify-center h-full">
        <msg-spinner-shared [msgShowMessage]="false"
          [msgSize]="'small'" />
      </div>
    } @else {
      <!-- Header -->
      <div class="p-3 border-b border-gray-100 bg-gray-100 flex-shrink-0">
        <div class="flex items-center justify-between">
          <h3 class="!mb-0 text-sm font-semibold text-gray-900">Notifications</h3>
          <span class="text-xs text-gray-500">{{ msgListNotifications().length }} new</span>
        </div>
      </div>

      <!-- Notifications List -->
      <div class="flex-1 overflow-y-auto">
        @for (noti of msgListNotifications(); track noti.id || $index) {
          <div class="flex items-start gap-3 p-2 hover:bg-blue-50 transition-all duration-200 cursor-pointer border-l-3 border-transparent hover:border-blue-500">
            <div class="flex-shrink-0 mt-1">
              <msg-icon-shared [msgName]="'BellRing'"
                [msgSize]="16"
                [msgClass]="'text-blue-500'" />
            </div>
            <div class="flex flex-col flex-1 min-w-0">
              <span class="text-[13px] font-medium text-gray-900 truncate">{{ noti.title | translate }}</span>
              <p class="!mb-0 text-xs text-gray-600 mt-1 leading-relaxed line-clamp-2">{{ noti.message | translate }}</p>
              <div class="flex items-center gap-2 mt-2">
                <span class="text-[10px] text-gray-400">2 min ago</span>
                <div class="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              </div>
            </div>
          </div>
        }
      </div>

      <!-- Footer -->
      <div class="p-3 border-t border-gray-100 bg-gray-100 flex-shrink-0">
        <msg-button-shared [msgText]="'Mark all as read'"
          [msgType]="'outline-info'"
          [msgSize]="'small'"
          [msgClass]="'w-full text-xs'"
          (msgClick)="onMarkAllAsRead()" />
      </div>
    }
  </div>
</ng-template>
