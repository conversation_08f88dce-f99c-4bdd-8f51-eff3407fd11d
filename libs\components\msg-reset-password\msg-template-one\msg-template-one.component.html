<div class="msg-reset-password-container h-full w-full">
  <form msgFormSubmit
    [formGroup]="msgFormResetPassword"
    [msgLoading]="msgLoading()"
    class="msg-reset-password-form w-full min-w-[300px] h-full shadow-uniform-sm flex flex-col item-center justify-center bg-white">
    <div class="msg-welcome text-center flex flex-col items-center justify-center mb-6">
      <img class="w-[80px] h-[80px] mb-3"
        [src]="msgLogo()"
        alt="mat-sai-gon-logo" />
      <h1 class="text-xl font-[400] !text-green-600 !text-shadow-sm mb-0! p-0!">{{ msgTitle() | translate }}</h1>
      <!-- <p class="font-[400] text-gray-500 p-0! m-0! !pt-[5px] text-[13px]">{{ msgDescription() | translate }}</p> -->
    </div>
    <div class="space-y-4">
      <div>
        <label for="email"
          class="text-slate-600 text-[13px] font-medium mb-[5px] block">{{ 'i18n_email' | translate }}</label>
        <div class="flex flex-col">
          <div class="relative flex items-center">
            <input name="email"
              id="email"
              type="email"
              required
              autofocus
              autocomplete="email"
              formControlName="email"
              class="!text-[13px] w-full text-slate-900 text-sm border border-slate-300 pl-4 py-[10px] !pr-[45px] rounded-[3px] outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 !shadow-sm focus:!shadow-md !bg-white transition-all duration-200 "
              placeholder="<EMAIL>" />
            <div class="bg-white h-[42px] w-full absolute z-[-1] rounded-[3px]"></div>
            <msg-icon-shared [msgName]="'Mail'"
              [msgClass]="'text-slate-400'"
              [class]="'absolute right-4'"
              [msgSize]="20" />
          </div>
          <msg-validate-shared [msgControl]="msgFormResetPassword.get('email')" />
        </div>
      </div>
      <!-- <div>
        <label for="password"
          class="text-slate-600 text-[13px] font-medium mb-[5px] block">Mật khẩu mới</label>
        <div class="flex flex-col">
          <div class="relative flex items-center">
            <input required
              autocomplete="new-password"
              name="password"
              id="password"
              formControlName="password"
              [type]="msgShowPassword() ? 'text' : 'password'"
              class="!text-[13px] w-full text-slate-900 text-sm border border-slate-300 px-4 py-[10px] pr-8 rounded-[3px] outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 !shadow-sm focus:!shadow-md !bg-white transition-all duration-200"
              placeholder="Nhập mật khẩu mới" />
            <msg-icon-shared [msgName]="msgShowPassword() ? 'EyeOff': 'Eye'"
              (click)="toggleShowPassword($event)"
              [msgClass]="'text-slate-400 cursor-pointer'"
              [class]="'absolute right-4'"
              [msgSize]="20" />
          </div>
          <msg-validate-shared [msgControl]="msgFormResetPassword.get('password')" />
        </div>
      </div>
      <div>
        <label for="confirmPassword"
          class="text-slate-600 text-[13px] font-medium mb-[5px] block">Xác nhận mật khẩu</label>
        <div class="flex flex-col">
          <div class="relative flex items-center w-full">
            <input required
              autocomplete="new-password"
              name="confirmPassword"
              id="confirmPassword"
              formControlName="confirmPassword"
              [type]="msgShowConfirmPassword() ? 'text' : 'password'"
              class="!text-[13px] w-full text-slate-900 text-sm border border-slate-300 px-4 py-[10px] pr-8 rounded-[3px] outline-none focus:border-green-500 focus:ring-2 focus:ring-green-200 !shadow-sm focus:!shadow-md !bg-white transition-all duration-200"
              placeholder="Nhập lại mật khẩu mới" />
            <msg-icon-shared [msgName]="msgShowConfirmPassword() ? 'EyeOff': 'Eye'"
              (click)="toggleShowConfirmPassword($event)"
              [msgClass]="'text-slate-400 cursor-pointer'"
              [class]="'absolute right-4'"
              [msgSize]="20" />
          </div>
          <msg-validate-shared [msgControl]="msgFormResetPassword.get('confirmPassword')" />
        </div>
      </div> -->
      <div>
        <msg-button-shared [msgType]="'primary'"
          [msgText]="'i18n_reset_password'"
          [msgLoading]="msgLoading()"
          [msgButtonSubmit]="true"
          [msgClass]="'w-full text-center !font-[500] !text-[13px]'"
          [msgSize]="'large'"
          (msgClick)="handlerResetPassword()" />

        <div class="flex items-center justify-center mt-2">
          <msg-button-shared [msgType]="'text'"
            [msgIconLeft]="'ChevronLeft'"
            [msgClassIcon]="'font-[600]'"
            [msgStrokeIcon]="3"
            [msgDisabled]="msgLoading()"
            [msgClass]="'!text-green-600 !font-[500] text-[13px]'"
            [msgText]="'i18n_back_to_login' | translate"
            (msgClick)="handlerBackToLogin()" />
        </div>

        <!-- <msg-icon-shared [msgName]="'ChevronLeft'"
          [msgClass]="'text-green-600 mr-1'"
          [msgStroke]="3"
          [msgSize]="18" /> -->
        <!-- <a class="text-green-600 hover:underline text-sm font-[500] text-[13px]">
          {{ 'i18n_back_to_login' | translate }} -->
        <!-- </a> -->
      </div>
    </div>
  </form>
</div>
