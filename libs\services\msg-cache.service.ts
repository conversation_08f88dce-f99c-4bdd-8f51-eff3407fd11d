/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@angular/core';
import { MsgCrypto } from '../utils';

@Injectable({
  providedIn: 'root'
})

export class MsgCacheService {
  private static _encrypt(data: any): string {
    const payload = {
      __prefix: '##<<myPrefix>>',
      __type: typeof data,
      __length: JSON.stringify(data).length,
      data
    };

    return MsgCrypto.encrypt(payload);
  }

  private static _decrypt(data: any): any {
    const decrypted = MsgCrypto.decrypt(data);
    const parsed = typeof decrypted === 'string' ? JSON.parse(decrypted) : decrypted;
    if (parsed.__prefix !== '##<<myPrefix>>') {
      throw new Error('Invalid data prefix');
    }

    return parsed.data;
  }

  public get<T>(key: string): T {
    const _key = MsgCacheService._encrypt(key);
    const data = MsgCacheService._decrypt(localStorage.getItem(_key));
    return data as T;
  }

  public set(key: string, data: any): void {
    const _key = MsgCacheService._encrypt(key);
    const _value = MsgCacheService._encrypt(data);
    localStorage.setItem(_key, _value);
  }

  public delete(key: string): void {
    const _key = MsgCacheService._encrypt(key);
    localStorage.removeItem(_key);
  }

  public static GET<T>(key: string): T {
    const _key = MsgCacheService._encrypt(key);
    const data = MsgCacheService._decrypt(localStorage.getItem(_key));
    return data as T;
  }

  public static SET(key: string, data: any): void {
    const _key = MsgCacheService._encrypt(key);
    const _value = MsgCacheService._encrypt(data);
    localStorage.setItem(_key, _value);
  }

  public static DELETE(key: string): void {
    const _key = MsgCacheService._encrypt(key);
    localStorage.removeItem(_key);
  }
}
