/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@angular/core';
import { MsgCacheDefine } from '../constants';
import { MsgCrypto } from '../utils';
import { TYPE_CACHE_KEY } from './interfaces/msg-cache.interface';

@Injectable({
  providedIn: 'root'
})

export class MsgCacheService {
  private static _encrypt(data: any): string {
    const payload = {
      __prefix: '##<<msgPrefix>>',
      __type: typeof data,
      __length: JSON.stringify(data).length,
      data
    };

    return MsgCrypto.encrypt(payload);
  }

  private _getKey(key: string): string {
    return MsgCacheDefine[key as keyof typeof MsgCacheDefine] as string;
  }

  private static _getKey(key: TYPE_CACHE_KEY): string {
    return MsgCacheDefine[key as keyof typeof MsgCacheDefine] as string;
  }

  private static _decrypt(data: any): any {
    const decrypted = MsgCrypto.decrypt(data);
    const parsed = typeof decrypted === 'string' ? JSON.parse(decrypted) : decrypted;
    if (parsed.__prefix !== '##<<msgPrefix>>') {
      throw new Error('Invalid data prefix');
    }

    return parsed.data;
  }

  public get<T>(key: TYPE_CACHE_KEY): T {
    const actualKey = this._getKey(key);
    const _key = MsgCacheService._encrypt(actualKey);
    const data = MsgCacheService._decrypt(localStorage.getItem(_key));
    return data as T;
  }

  public set<T>(key: TYPE_CACHE_KEY, data: T): void {
    const actualKey = this._getKey(key);
    const _key = MsgCacheService._encrypt(actualKey);
    const _value = MsgCacheService._encrypt(data);
    localStorage.setItem(_key, _value);
  }

  public delete(key: string): void {
    const actualKey = this._getKey(key);
    const _key = MsgCacheService._encrypt(actualKey);
    localStorage.removeItem(_key);
  }

  public clear(): void {
    Object.keys(MsgCacheDefine).forEach((key) => {
      const _key = MsgCacheService._encrypt(key);
      localStorage.removeItem(_key);
    });
  }

  public static GET<T>(key: TYPE_CACHE_KEY): T {
    const actualKey = MsgCacheService._getKey(key);
    const _key = MsgCacheService._encrypt(actualKey);
    const data = MsgCacheService._decrypt(localStorage.getItem(_key));
    return data as T;
  }

  public static SET<T>(key: TYPE_CACHE_KEY, data: T): void {
    const actualKey = MsgCacheService._getKey(key);
    const _key = MsgCacheService._encrypt(actualKey);
    const _value = MsgCacheService._encrypt(data);
    localStorage.setItem(_key, _value);
  }

  public static DELETE(key: TYPE_CACHE_KEY): void {
    const actualKey = MsgCacheService._getKey(key);
    const _key = MsgCacheService._encrypt(actualKey);
    localStorage.removeItem(_key);
  }

  public static CLEAR(): void {
    Object.keys(MsgCacheDefine).forEach((key) => {
      const _key = MsgCacheService._encrypt(key);
      localStorage.removeItem(_key);
    });
  }
}
