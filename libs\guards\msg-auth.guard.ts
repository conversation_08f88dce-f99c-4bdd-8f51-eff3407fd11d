// import { inject } from '@angular/core';
// import { CanActivateFn, Router } from '@angular/router';
// import { catchError, map, of } from 'rxjs';
// import { MsgRoutesConstant } from '../constants/msg-router.define';

// export const msgAuthGuard: CanActivateFn = (route, state) => {
//   const _msgAuthService = inject(AuthService);
//   const _router = inject(Router);

//   return _msgAuthService.checkAuth().pipe(
//     map(isAuthenticated => {
//       if (isAuthenticated) {
//         return true;
//       }
//       _router.navigate([MsgRoutesConstant.LOGIN], { queryParams: { returnUrl: state.url } });
//       return false;
//     }),
//     catchError((error) => {
//       console.error(error);
//       _router.navigate([MsgRoutesConstant.LOGIN], { queryParams: { returnUrl: state.url } });
//       return of(false);
//     })
//   );
// };
