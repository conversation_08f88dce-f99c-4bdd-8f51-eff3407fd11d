@use "animations" as *;

:host {
  display: block;
  width: 100%;
  height: 100%;
}

.msg-spinner {
  display: flex;
  height: 100%;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;

  &-loader {
    border-radius: 50%;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--color-green-600);
    animation: msg-spin 1s linear infinite;
  }

  &-small {
    width: 40px;
    height: 40px;
    border-width: 5px;
  }

  &-medium {
    width: 50px;
    height: 50px;
    border-width: 5px;
  }

  &-large {
    width: 60px;
    height: 60px;
    border-width: 5px;
  }

  &-message {
    font-size: 14px;
    color: #666;
    text-align: center;
    margin: 0;
    margin-top: 5px;
  }

  &-hidden {
    display: none;
  }
}
