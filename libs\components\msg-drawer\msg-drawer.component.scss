@use "responsive" as *;
@use "animations" as *;

:host {
  .msg-drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: opacity .3s ease-out, visibility .3s ease-out;
    transform: translateZ(0);

    &.msg-drawer-overlay-visible {
      opacity: 1;
      visibility: visible;
    }
  }

  .msg-drawer {
    position: fixed;
    top: 0;
    height: 100vh;
    width: 600px;
    background-color: #ffffff;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    z-index: 110;
    display: flex;
    flex-direction: column;
    transform: translateZ(0);
    backface-visibility: hidden;
    contain: layout style;
    transition: transform .3s cubic-bezier(0.4, 0, 0.2, 1);

    &.msg-drawer-right {
      right: 0;
      transform: translateX(100%);

      &.msg-drawer-visible {
        transform: translateX(0);
      }
    }

    &.msg-drawer-left {
      left: 0;
      transform: translateX(-100%);

      &.msg-drawer-visible {
        transform: translateX(0);
      }
    }
  }

  .msg-drawer-header {
    position: sticky;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    padding: 8px 16px;
    min-height: 40px;
    flex-shrink: 0;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transform: translateZ(0);
    will-change: transform;

    .msg-drawer-title {
      h2 {
        margin: 0;
        font-size: 15px;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.5;
      }
    }

    .msg-drawer-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .msg-drawer-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background-color: #ffffff;
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    will-change: transform;

    .msg-drawer-content-placeholder {
      text-align: center;
      padding: 40px 20px;
      color: #6b7280;

      h3 {
        margin: 0 0 8px;
        font-size: 16px;
        font-weight: 500;
        color: #374151;
      }

      p {
        margin: 0;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }

  .msg-drawer-footer {
    position: sticky;
    bottom: 0;
    padding: 8px 16px;
    background-color: #ffffff;
    border-top: 1px solid #e5e7eb;
    flex-shrink: 0;
    z-index: 10;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
    transform: translateZ(0);
    will-change: transform;

    .msg-drawer-footer-actions {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      column-gap: 10px;
    }
  }

  @include respond-to-max('tablet') {
    .msg-drawer {
      width: calc(100vw - 20%) !important;
      max-width: none;
      contain: layout style;
      will-change: transform;
    }

    .msg-drawer-header {
      padding: 10px 16px;
    }

    .msg-drawer-content {
      padding: 16px;
    }

    .msg-drawer-footer {
      padding: 12px 16px;
    }
  }

  @include respond-to-max('mobile') {
    .msg-drawer {
      width: 100% !important;
    }
  }

  @media (prefers-reduced-motion: reduce) {

    .msg-drawer,
    .msg-drawer-overlay {
      transition: none !important;
    }
  }
}
