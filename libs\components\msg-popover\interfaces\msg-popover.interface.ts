import { NzTSType } from 'ng-zorro-antd/core/types';
import { NzTooltipTrigger } from 'ng-zorro-antd/tooltip';
import { TYPE_POPOVER_PLACEMENT } from '../../../interfaces/common.interface';

export interface IMsgPopoverConfigDto {
  placement?: TYPE_POPOVER_PLACEMENT;
  trigger?: NzTooltipTrigger | undefined;
  visible?: boolean;
  overlayClassName?: string;
  content?: NzTSType | undefined;
}

export interface IMsgPopoverFunctionControl {
  show: () => void;
  hide: () => void;
}
