/* eslint-disable @typescript-eslint/no-explicit-any */
import { ApplicationRef, ComponentRef, createComponent, EmbeddedViewRef, EnvironmentInjector, inject, Injectable, Injector, TemplateRef, Type, ViewContainerRef } from '@angular/core';
import { uuid } from '../utils';
@Injectable({
  providedIn: 'root'
})

export class MsgDynamicComponentService {
  private _appRef = inject(ApplicationRef);
  private _injector = inject(Injector);
  private _envInjector = inject(EnvironmentInjector);
  private _componentMap = new Map<string, ComponentRef<any>>();

  public createComponent<T>(
    component: Type<T>,
    container: HTMLElement | ViewContainerRef,
    injector?: Injector,
  ): ComponentRef<T> {
    if (container instanceof ViewContainerRef) {
      return container.createComponent(component, { injector: injector || this._injector });
    }

    const compRef = createComponent(component, {
      environmentInjector: this._envInjector,
      elementInjector: injector || this._injector
    });

    this._appRef.attachView(compRef.hostView);
    const compId = uuid();
    const hostEl = (compRef.hostView as EmbeddedViewRef<any>).rootNodes[0] as HTMLElement;
    if (hostEl && hostEl.setAttribute) {
      hostEl.setAttribute('msgId', compId);
      const selectors = ['msg-modal-shared', 'msg-drawer-shared'];
      selectors.forEach(sel => {
        hostEl.querySelectorAll(sel).forEach(el => {
          el.setAttribute('msgId', compId);
        });
      });
    }

    (container as HTMLElement).appendChild(hostEl);
    this._componentMap.set(compId, compRef);
    (compRef as any).__dynamicId = compId;

    return compRef;
  }

  public getComponentRefById<T = any>(id: string): ComponentRef<T> | undefined {
    return this._componentMap.get(id) as ComponentRef<T> | undefined;
  }

  public removeComponentById(id: string) {
    const compRef = this._componentMap.get(id);
    if (compRef) {
      this.removeComponent(compRef);
      this._componentMap.delete(id);
    }
  }

  public addToBody<T>(component: Type<T>, injector?: Injector): ComponentRef<T> {
    return this.createComponent(component, document.body, injector);
  }

  public addToElement<T>(component: Type<T>, element: HTMLElement, injector?: Injector): ComponentRef<T> {
    return this.createComponent(component, element, injector);
  }

  public addTemplateToBody(
    template: TemplateRef<any>,
    context: any = {},
    container: HTMLElement = document.body
  ): EmbeddedViewRef<any> {
    const viewRef = template.createEmbeddedView(context);
    this._appRef.attachView(viewRef);
    viewRef.rootNodes.forEach(node => container.appendChild(node));
    return viewRef;
  }

  public addTemplateToElement(
    template: TemplateRef<any>,
    context: any = {},
    element: HTMLElement
  ): EmbeddedViewRef<any> {
    const viewRef = template.createEmbeddedView(context);
    this._appRef.attachView(viewRef);
    viewRef.rootNodes.forEach(node => element.appendChild(node));
    return viewRef;
  }

  public removeComponent<T>(compRef: ComponentRef<T>) {
    if (compRef && !compRef.hostView.destroyed) {
      this._appRef.detachView(compRef.hostView);
      compRef.destroy();
    }
  }

  public removeTemplate(viewRef: EmbeddedViewRef<any>) {
    if (viewRef && !viewRef.destroyed) {
      this._appRef.detachView(viewRef);
      viewRef.destroy();
    }
  }

  public getComponentRef<T>(component: T): ComponentRef<T> | null {
    const appView = this._appRef.components.find(c => c.instance === component);
    return appView ? (appView as ComponentRef<T>) : null;
  }
}
