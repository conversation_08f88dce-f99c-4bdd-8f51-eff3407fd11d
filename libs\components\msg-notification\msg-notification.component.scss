@use 'sass:map';

:host {
  display: block;

  animation: notification-slide-in-right 0.3s cubic-bezier(0.16, 1, 0.3, 1);

  &.position-left {
    animation: notification-slide-in-left 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  &.position-center {
    animation: notification-fade-in-center 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  &.position-right {
    animation: notification-slide-in-right 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  &.hide {
    animation: notification-slide-out-right 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;

    &.position-left {
      animation: notification-slide-out-left 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
    }

    &.position-center {
      animation: notification-fade-out-center 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
    }

    &.position-right {
      animation: notification-slide-out-right 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
    }
  }
}

$notification-types: (
  'info': ('bg': var(--color-blue-50),
    'text': var(--color-blue-700),
    'accent': var(--color-blue-600),
  ),
  'warning': ('bg': var(--color-orange-50),
    'text': var(--color-orange-700),
    'accent': var(--color-orange-300),
  ),
  'danger': ('bg': var(--color-red-50),
    'text': var(--color-red-700),
    'accent': var(--color-red-600),
  ),
  'success': ('bg': var(--color-green-50),
    'text': var(--color-green-700),
    'accent': var(--color-green-600),
  ),
);

@mixin notification-type-styles($accent-color, $bg-color, $text-color) {
  background: $bg-color;
  color: $text-color;

  .msg-notification-icon {
    color: $accent-color;
  }

  .msg-notification-close:hover {
    background-color: color-mix(in srgb, $accent-color 10%, transparent) !important;
  }

  .msg-notification-progress-bar {
    background: $accent-color;
  }
}

@mixin notification-animation($transform-from, $animation-name-in, $animation-name-out) {
  .msg-notification {
    transform: $transform-from;
    opacity: 0;
    transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.3s ease-out;
    animation: #{$animation-name-in} 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;

    &.hide {
      animation: #{$animation-name-out} 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
    }
  }
}

@mixin notification-borders($side) {
  @each $type, $colors in $notification-types {
    .msg-notification-#{$type} {
      border-#{$side}: 3px solid #{map.get($colors, 'accent')};
    }
  }
}

.msg-notification {
  position: relative;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  pointer-events: auto;
  max-width: 380px;
  width: fit-content;
  min-width: 250px;
  min-height: 50px;
  opacity: 1;
  overflow: hidden;
  box-shadow: var(--shadow-uniform-sm);
  transition: box-shadow 0.3s cubic-bezier(0.16, 1, 0.3, 1), transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);

  &.hide {
    pointer-events: none;
  }

  &:hover {
    box-shadow: var(--shadow-uniform-md);
    transform: translateY(-2px);
  }

  &-icon {
    margin-right: 16px;
    font-size: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    flex-shrink: 0;
  }

  &-content {
    flex: 1;
    word-break: break-word;
    line-height: 1.5;
    font-size: 13px;
  }

  &-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 3px;
    background: rgba(255, 255, 255, 0.2);
    overflow: hidden;
    z-index: 1;
  }

  &-progress-bar {
    width: 100%;
    height: 100%;
    display: block;

    &.progress-countdown-left {
      animation: progress-countdown-left linear forwards;
    }

    &.progress-countdown-right {
      animation: progress-countdown-right linear forwards;
    }

    &.progress-countdown-center {
      animation: progress-countdown-center linear forwards;
    }

    &:not(.progress-countdown-left):not(.progress-countdown-right):not(.progress-countdown-center) {
      animation: progress-countdown linear forwards;
    }

    &.paused {
      animation-play-state: paused;
    }
  }

  @each $type, $colors in $notification-types {
    &-#{$type} {
      @include notification-type-styles(map.get($colors, 'accent'),
        map.get($colors, 'bg'),
        map.get($colors, 'text'));

      border-left: 3px solid #{map.get($colors, 'accent')};
    }
  }
}

@keyframes notification-slide-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes notification-slide-out-left {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes notification-slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes notification-slide-out-right {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes notification-fade-in-center {
  from {
    transform: scale(0.8) translateY(-20px);
    opacity: 0;
  }

  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes notification-fade-out-center {
  from {
    transform: scale(1) translateY(0);
    opacity: 1;
  }

  to {
    transform: scale(0.8) translateY(-20px);
    opacity: 0;
  }
}

@keyframes progress-countdown-left {
  from {
    transform: scaleX(1);
    transform-origin: left;
  }

  to {
    transform: scaleX(0);
    transform-origin: left;
  }
}

@keyframes progress-countdown-right {
  from {
    transform: scaleX(1);
    transform-origin: right;
  }

  to {
    transform: scaleX(0);
    transform-origin: right;
  }
}

@keyframes progress-countdown-center {
  from {
    transform: scaleX(1);
    transform-origin: left;
  }

  to {
    transform: scaleX(0);
    transform-origin: left;
  }
}

@keyframes progress-countdown {
  from {
    transform: scaleX(1);
    transform-origin: left;
  }

  to {
    transform: scaleX(0);
    transform-origin: left;
  }
}
