@mixin button-type($bg-color, $border-color: $bg-color, $text-color: #fff, $hover-bg: null, $hover-border: null) {
  background: $bg-color !important;
  border-color: $border-color !important;
  color: $text-color !important;
  transition: all 0.2s ease !important;

  $hover-bg-color: if($hover-bg, $hover-bg, darken($bg-color, 8%));
  $hover-border-color: if($hover-border, $hover-border, darken($border-color, 8%));

  &:hover,
  &:focus {
    background: $hover-bg-color !important;
    border-color: $hover-border-color !important;
    color: $text-color !important;
  }
}

@mixin button-transparent($color, $border-color: $color, $has-border: true, $shadow: null, $hover-bg: null) {
  background: transparent !important;
  color: $color !important;
  transition: all 0.2s ease !important;

  @if not $has-border {
    border: unset !important;
    border-color: unset !important;
    box-shadow: unset !important;
  }

  @else {
    border-color: $border-color !important;
  }

  @if $shadow {
    box-shadow: $shadow !important;
  }

  $hover-bg-color: if($hover-bg, $hover-bg, rgba($color, 0.1));

  &:hover,
  &:focus {
    background: $hover-bg-color !important;
    color: $color !important;

    @if not $has-border {
      border: unset !important;
      border-color: unset !important;
    }

    @else {
      border-color: $border-color !important;
    }
  }
}

@mixin disable-wave {
  outline: none !important;

  &::after {
    display: none !important;

    .ant-wave {
      display: none !important;
    }

    .ant-wave-anim {
      animation: none !important;
    }
  }
}

.msg-button {
  &-count-badge {
    position: absolute;
    top: -6px;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 20px;
    min-width: 20px;
    padding: 0 6px;
    color: var(--color-neutral-50);
    background: var(--color-red-600);
    border-radius: 50px;
    transform: translateX(50%);
    z-index: 10;
    box-shadow: var(--shadow-uniform-xs);

    &-text {
      font-size: 10px;
      font-weight: 500;
      line-height: 1;
    }
  }

  &-flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  &-font-medium {
    font-weight: 450;
  }
}

::ng-deep {
  button.ant-btn {
    box-shadow: var(--shadow-uniform-sm);
    border-radius: 3px;
    font-weight: 500;
    transition: all 0.2s ease;

    &[msgSize='default'] {
      padding: 4px 8px;
      font-size: 13px;
    }

    &[msgSize='small'] {
      padding: 0px 4px;
      font-size: 11px;
    }

    &[msgDisableWave='true'] {
      @include disable-wave;
    }

    &[msgDisabled='true'] {
      cursor: not-allowed;
      opacity: 0.7;
      @include disable-wave;

      &:focus,
      &:hover {
        cursor: not-allowed;
        opacity: 0.7;
        outline: none;
      }
    }

    &[msgType='primary'] {
      @include button-type(var(--color-green-500), var(--color-green-500), #ffffff, var(--color-green-600), var(--color-green-600));
    }

    &[msgType='secondary'] {
      @include button-type(var(--color-slate-50), var(--color-slate-200), var(--color-slate-600), var(--color-slate-200), var(--color-slate-300));
    }

    &[msgType='warn'] {
      @include button-type(var(--color-orange-500), var(--color-orange-500), #ffffff, var(--color-orange-600), var(--color-orange-600));
    }

    &[msgType='success'] {
      @include button-type(var(--color-green-500), var(--color-green-500), #ffffff, var(--color-green-600), var(--color-green-600));
    }

    &[msgType='danger'] {
      @include button-type(var(--color-red-500), var(--color-red-500), #ffffff, var(--color-red-600), var(--color-red-600));
    }

    &[msgType='info'] {
      @include button-type(var(--color-blue-500), var(--color-blue-500), #ffffff, var(--color-blue-600), var(--color-blue-600));
    }

    &[msgType='outline'] {
      @include button-transparent(var(--color-green-500), var(--color-green-500), true, null, rgba(34, 197, 94, 0.1));
    }

    &[msgType='outline-secondary'] {
      @include button-transparent(var(--color-slate-600), var(--color-slate-300), true, 0 1px 2px 0 rgb(0 0 0 / 0.05), rgba(100, 116, 139, 0.1));
    }

    &[msgType='outline-warn'] {
      @include button-transparent(var(--color-orange-500), var(--color-orange-500), true, null, rgba(249, 115, 22, 0.1));
    }

    &[msgType='outline-success'] {
      @include button-transparent(var(--color-green-500), var(--color-green-500), true, null, rgba(34, 197, 94, 0.1));
    }

    &[msgType='outline-danger'] {
      @include button-transparent(var(--color-red-500), var(--color-red-500), true, null, rgba(239, 68, 68, 0.1));
    }

    &[msgType='outline-info'] {
      @include button-transparent(var(--color-blue-500), var(--color-blue-500), true, null, rgba(59, 130, 246, 0.1));
    }

    &[msgType='link'] {
      @include button-transparent(var(--color-blue-500), var(--color-blue-500), false);

      &:hover,
      &:focus {
        color: var(--color-blue-600) !important;
        text-decoration: underline;
      }
    }

    &[msgType='text'] {
      @include button-transparent(var(--color-slate-600), var(--color-slate-600), false);

      &:hover,
      &:focus {
        color: var(--color-slate-700);
        background: rgba(100, 116, 139, 0.1) !important;
      }
    }

    &[msgIconOnly="true"] {
      aspect-ratio: 1;
      min-width: 32px;
      min-height: 32px;
      padding: 6px;
      display: inline-flex;
      align-items: center;
      justify-content: center;

      .msg-button-flex-center {
        width: 100%;
        height: 100%;
        gap: 0;
      }

      &[msgSize='small'] {
        min-width: 27px;
        min-height: 27px;
        padding: 4px;
      }

      &[msgSize='large'] {
        min-width: 37px;
        min-height: 37px;
        padding: 8px;
      }
    }
  }
}
