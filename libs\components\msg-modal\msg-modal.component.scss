@use "responsive" as *;
@use "animations" as *;

:host {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    visibility 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &[data-visible="true"] {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;

    .msg-modal-overlay {
      opacity: 1;
    }

    .msg-modal-container {
      opacity: 1;
      transform: scale(1) translateZ(0);
    }
  }
}

// Overlay styles
.msg-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
  z-index: -1;
  will-change: opacity;
}

// Container styles
.msg-modal-container {
  position: relative;
  background: white;
  border-radius: 8px;
  width: 420px;
  max-width: 90%;
  max-height: 90vh;
  pointer-events: auto;
  opacity: 0;
  transform: scale(0.7) translateZ(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
  backface-visibility: hidden;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.msg-modal-service-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;

  .msg-modal-icon {
    width: 72px;
    height: 72px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    &.msg-modal-icon-info {
      background-color: var(--color-blue-50);
      color: var(--color-blue-600);
    }

    &.msg-modal-icon-success {
      background-color: var(--color-green-50);
      color: var(--color-green-600);
    }

    &.msg-modal-icon-warn {
      background-color: var(--color-orange-50);
      color: var(--color-orange-600);

      msg-icon-shared {
        margin-bottom: 5px;
      }
    }

    &.msg-modal-icon-danger {
      background-color: var(--color-red-50);
      color: var(--color-red-600);
    }
  }

  .msg-modal-message {
    width: 100%;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--color-gray-900);
      margin: 0 0 8px;
    }

    p {
      color: var(--color-gray-600);
      margin: 0;
      font-size: 13px;
      line-height: 1.5;
    }
  }
}

.msg-modal-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  border-top: 1px solid #e5e7eb;
  padding: 16px;

  &[msg-service-mode="true"] {
    border: none;
  }

  &-actions {
    display: flex;
    gap: 12px;
  }
}

.msg-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px 16px;
  flex-shrink: 0;

  .msg-modal-title {
    flex: 1;
    margin-right: 16px;

    h2 {
      margin: 0;
      font-size: 17px;
      font-weight: 600;
      color: #1f2937;
      line-height: 1.5;
    }
  }

  .msg-modal-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }
}

.msg-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;

  .msg-modal-content-placeholder {
    text-align: center;
    color: #6b7280;

    h3 {
      margin: 0 0 8px;
      font-size: 16px;
      font-weight: 500;
      color: #374151;
    }

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

// Responsive design
@include respond-to-max('tablet') {
  .msg-modal-container {
    width: 95vw;
    max-width: none;
    margin: 0 10px;
  }

  .msg-modal-header {
    padding: 14px 16px;

    .msg-modal-title h2 {
      font-size: 16px;
    }
  }

  .msg-modal-content {
    padding: 20px;
  }

  .msg-modal-footer {
    padding: 16px;

    .msg-modal-footer-actions {
      gap: 12px;
    }
  }
}

// Dark theme support
:host-context(.dark) {
  .msg-modal-overlay {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .msg-modal-container {
    background-color: var(--color-gray-800);
  }

  .msg-modal-header {
    border-bottom-color: #374151;

    .msg-modal-title h2 {
      color: #f9fafb;
    }
  }

  .msg-modal-content {
    .msg-modal-content-placeholder {
      color: #9ca3af;

      h3 {
        color: #f3f4f6;
      }
    }

    .msg-modal-service-content {
      .msg-modal-message {
        h3 {
          color: var(--color-gray-100);
        }

        p {
          color: var(--color-gray-400);
        }
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .msg-modal-container {
    width: 80vw;
    max-width: none;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  :host {
    transition: none !important;
  }

  .msg-modal-container {
    transition: none !important;
  }

  .msg-modal-overlay {
    transition: none !important;
  }
}
