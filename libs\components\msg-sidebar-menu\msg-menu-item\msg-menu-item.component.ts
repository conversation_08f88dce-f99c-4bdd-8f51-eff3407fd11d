import { Async<PERSON>ipe, NgTemplateOutlet } from "@angular/common";
import { ChangeDetectionStrategy, Component, computed, effect, input, model, OnInit, output, signal, untracked } from "@angular/core";
import { RouterModule } from "@angular/router";
import { NzPopoverModule } from "ng-zorro-antd/popover";
import { NzToolTipModule } from "ng-zorro-antd/tooltip";
import { MsgTruncatePipe } from '../../../pipes/msg-truncate.pipe';
import { MsgIconSharedComponent } from "../../msg-icon/msg-icon.component";
import { IMsgMenuDto } from '../interfaces/msg-sidebar-menu.interface';

@Component({
  selector: 'msg-menu-item',
  templateUrl: './msg-menu-item.component.html',
  styleUrls: ['./msg-menu-item.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    MsgIconSharedComponent,
    RouterModule,
    NzToolTipModule,
    NzPopoverModule,
    AsyncPipe,
    MsgTruncatePipe,
    NgTemplateOutlet,
  ],
})
export class MsgMenuItemComponent implements OnInit {
  public readonly msgMenuItem = model<IMsgMenuDto>();
  public readonly msgMenuChangeUpdateParent = input<string | null>(null);
  public readonly msgCollapsed = input<boolean>(false);
  public readonly msgMenuActive = output<IMsgMenuDto | null>();

  protected readonly menuHover = signal<IMsgMenuDto | null>(null);
  protected readonly msgShowSubMenuTablet = signal<boolean>(false);
  protected readonly msgSubMenuTablet = signal<IMsgMenuDto | null>(null);
  protected readonly msgMenuParentActive = signal<boolean>(false);

  protected readonly msgTextColorClass = computed(() => {
    const menuItem = this.msgMenuItem();
    if (!menuItem) {
      return 'text-gray-700';
    }

    if (menuItem.level === 0) {
      return '!text-green-600';
    }

    if (menuItem.active && menuItem.level !== 0) {
      return '!text-gray-900';
    }

    return 'text-gray-700';
  });

  protected readonly msgArrowIconColorClass = computed(() => {
    const menuItem = this.msgMenuItem();
    if (!menuItem) {
      return 'text-gray-700';
    }

    return menuItem.level === 0 ? 'text-green' : 'text-gray-700';
  });

  protected readonly trackByMenuId = (index: number, item: IMsgMenuDto): string => item.id;

  constructor() {
    effect(() => {
      if (!this.msgCollapsed()) {
        this.msgShowSubMenuTablet.set(false);
      }
    });

    effect(() => {
      const msgMenuParentActive = this.msgMenuChangeUpdateParent();
      if (msgMenuParentActive) {
        untracked(() => {
          this.msgMenuParentActive.set(this._hasActiveChildren(this.msgMenuItem()));
        });
      }
    });
  }

  ngOnInit(): void { }

  protected handlerClickMenuItem(event: MouseEvent, menuItem: IMsgMenuDto): void {
    event.stopPropagation();
    if (!menuItem.children?.length) {
      this.navigateToMenuItem(menuItem);
      return;
    }

    if (!menuItem?.children?.length || this.msgCollapsed()) { return; }
    menuItem.expanded = !menuItem.expanded;
  }

  protected navigateToMenuItem(menuItem: IMsgMenuDto): void {
    if (menuItem.children?.length || !menuItem.path) { return; }
    this.msgMenuActive.emit(menuItem);
  }

  protected handlerLeaveMenuItem(event: MouseEvent): void {
    event.stopPropagation();
    this.menuHover.set(null);
  }

  protected handlerMouseMenuItem(event: MouseEvent, menuItem: IMsgMenuDto): void {
    event.stopPropagation();
    this.menuHover.set(menuItem);
  }

  protected handlerShowSubMenuTablet(event: MouseEvent, menuItem: IMsgMenuDto): void {
    event.stopPropagation();
    if (!menuItem.children?.length || !this.msgCollapsed()) { return; }

    this.msgSubMenuTablet.set(menuItem);
    this.msgShowSubMenuTablet.update(value => !value);
  }

  protected handlerPopoverChange(visible: boolean): void {
    if (!visible) {
      this.msgShowSubMenuTablet.set(false);
    }

    const menuItem = this.msgMenuItem();
    if (menuItem) {
      menuItem.expanded = true;
    }
  }

  protected handlerClickSubmenuItem(item: IMsgMenuDto): void {
    if (item.children?.length) {
      item.expanded = !item.expanded;
      return;
    }

    this.navigateToMenuItem(item);
    this.msgShowSubMenuTablet.set(false);
  }

  private _hasActiveChildren(menu: IMsgMenuDto | undefined): boolean {
    if (!menu?.children) { return false; }
    return menu.children.some(child => child.active || this._hasActiveChildren(child));
  }
}
