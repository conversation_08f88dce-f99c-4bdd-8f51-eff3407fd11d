{
  "compileOnSave": false,
  "compilerOptions": {
    "rootDir": ".",
    "sourceMap": true,
    "declaration": false,
    "moduleResolution": "node",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "importHelpers": true,
    "target": "ES2022",
    "module": "esnext",
    "lib": [
      "es2020",
      "dom"
    ],
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "baseUrl": ".",
    "paths": {
      "@libs/components/msg-icon": [
        "libs/components/msg-icon/msg-icon.component.ts"
      ],
      "@libs/components/msg-icon/interfaces": [
        "libs/components/msg-icon/interfaces/index.ts"
      ],
      "@libs/components/msg-sidebar-menu": [
        "libs/components/msg-sidebar-menu/msg-sidebar-menu.component.ts"
      ],
      "@libs/components/msg-sidebar-menu/interfaces": [
        "libs/components/msg-sidebar-menu/interfaces/index.ts"
      ],
      "@libs/components/msg-button": [
        "libs/components/msg-button/msg-button.component.ts"
      ],
      "@libs/components/msg-button/interfaces": [
        "libs/components/msg-button/interfaces/index.ts"
      ],
      "@libs/components/msg-login": [
        "libs/components/msg-login/msg-login.component.ts"
      ],
      "@libs/components/msg-reset-password": [
        "libs/components/msg-reset-password/msg-reset-password.component.ts"
      ],
      "@libs/components/msg-table/interfaces": [
        "libs/components/msg-table/interfaces/index.ts"
      ],
      "@libs/components/msg-spinner/interfaces": [
        "libs/components/msg-spinner/interfaces/index.ts"
      ],
      "@libs/components/msg-spinner": [
        "libs/components/msg-spinner/msg-spinner.component.ts"
      ],
      "@libs/components/msg-table": [
        "libs/components/msg-table/msg-table.module.ts"
      ],
      "@libs/components/msg-skeleton/interfaces": [
        "libs/components/msg-skeleton/interfaces/index.ts"
      ],
      "@libs/components/msg-skeleton": [
        "libs/components/msg-skeleton/msg-skeleton.component.ts"
      ],
      "@libs/components/msg-notification": [
        "libs/components/msg-notification/msg-notification.component.ts"
      ],
      "@libs/components/msg-validate": [
        "libs/components/msg-validate/msg-validate.module.ts"
      ],
      "@libs/components/msg-menu-action": [
        "libs/components/msg-menu-action/msg-menu-action.component.ts"
      ],
      "@libs/components/msg-menu-action/interfaces": [
        "libs/components/msg-menu-action/interfaces/index.ts"
      ],
      "@libs/components/msg-drawer": [
        "libs/components/msg-drawer/msg-drawer.component.ts"
      ],
      "@libs/components/msg-drawer/interfaces": [
        "libs/components/msg-drawer/interfaces/index.ts"
      ],
      "@libs/components/msg-modal": [
        "libs/components/msg-modal/msg-modal.component.ts"
      ],
      "@libs/components/msg-modal/interfaces": [
        "libs/components/msg-modal/interfaces/index.ts"
      ],
      "@libs/components/msg-popover": [
        "libs/components/msg-popover/msg-popover.component.ts"
      ],
      "@libs/components/msg-popover/interfaces": [
        "libs/components/msg-popover/interfaces/index.ts"
      ],
      "@libs/components/msg-tabs": [
        "libs/components/msg-tabs/msg-tabs.component.ts"
      ],
      "@libs/components/msg-tabs/interfaces": [
        "libs/components/msg-tabs/interfaces/index.ts"
      ],
      "@libs/components/msg-page-401": [
        "libs/components/msg-page-401/msg-page-401.component.ts"
      ],
      "@libs/components/msg-page-404": [
        "libs/components/msg-page-404/msg-page-404.component.ts"
      ],
      "@libs/components/msg-page-403": [
        "libs/components/msg-page-403/msg-page-403.component.ts"
      ],
      "@libs/components/msg-empty": [
        "libs/components/msg-empty/msg-empty.component.ts"
      ],
      "@libs/components/msg-alert-version": [
        "libs/components/msg-alert-version/msg-alert-version.component.ts"
      ],
      "@libs/components/msg-alert-version/interfaces": [
        "libs/components/msg-alert-version/interfaces/index.ts"
      ],
      "@libs/components/msg-bell-notifications": [
        "libs/components/msg-bell-notifications/msg-bell-notifications.component.ts"
      ],
      "@libs/components/msg-bell-notifications/interfaces": [
        "libs/components/msg-bell-notifications/interfaces/index.ts"
      ],
      "@libs/components/msg-header-title": [
        "libs/components/msg-header-title/msg-header-title.component.ts"
      ],
      "@libs/constants": [
        "libs/constants/index.ts"
      ],
      "@libs/services": [
        "libs/services/index.ts"
      ],
      "@libs/directives": [
        "libs/directives/index.ts"
      ],
      "@libs/pipes": [
        "libs/pipes/index.ts"
      ],
      "@libs/utils": [
        "libs/utils/index.ts"
      ],
      "@libs/providers": [
        "libs/providers/index.ts"
      ],
      "@libs/assets/*": [
        "libs/assets/*"
      ],
      "@libs/interfaces": [
        "libs/interfaces/index.ts"
      ],
    }
  },
  "exclude": [
    "node_modules",
    "tmp"
  ]
}
