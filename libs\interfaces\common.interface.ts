/**
 * <PERSON><PERSON><PERSON> dữ liệu đại diện cho một mảng có thể chứa các phần tử khác nhau.
 * Ví dụ: TYPE_ARRAY_ELEMENT<[1, 'a', true]> sẽ trả về 1 | 'a' | true
 */
export type TYPE_ARRAY_ELEMENT<T> = T extends readonly (infer E)[] ? E : never;
/**
 * Chuyển đổi các kiểu dữ liệu số, chuỗi, boolean thành chuỗi.
 * Ví dụ: TYPE_STRINGIFY_UNION<1 | 2 | 3> sẽ trả về "1" | "2" | "3"
 */
export type TYPE_STRINGIFY_UNION<T> = T extends number | string | boolean ? `${T}` : never;

export type TYPE_POPOVER_PLACEMENT = 'top' | 'left' | 'right' | 'bottom' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' | 'leftTop' | 'leftBottom' | 'rightTop' | 'rightBottom' | Array<string>;
