import { inject, Injectable } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { NzI18nService, vi_VN } from 'ng-zorro-antd/i18n';
import { DefineConstant } from "../constants/msg-common.define";

@Injectable({
  providedIn: 'root'
})

export class MsgTranslateService {
  private _translate = inject(TranslateService);
  private _nzI18n = inject(NzI18nService);
  private static _currentLang = 'vi';

  constructor() {
    this._initializeTranslate();
  }

  private _initializeTranslate(): void {
    this._translate.setDefaultLang('vi');
    const savedLang = localStorage.getItem('msg-language') || 'vi';
    this._translate.use(savedLang);
    this._updateNgZorroLocale(savedLang);
    MsgTranslateService._currentLang = savedLang;
  }

  private _updateNgZorroLocale(lang: string): void {
    const locale = DefineConstant.MSG_LANGUAGE[lang as keyof typeof DefineConstant.MSG_LANGUAGE] || vi_VN;
    this._nzI18n.setLocale(locale);
  }

  public changeLanguage(lang: string): void {
    this._translate.use(lang);
    localStorage.setItem('msg-language', lang);
    this._updateNgZorroLocale(lang);
  }

  public getCurrentLanguage(): string {
    return this._translate.currentLang || 'vi';
  }

  public static getCurrentLanguage(): string {
    return MsgTranslateService._currentLang;
  }

  public instant(key: string, params?: Record<string, unknown>): string {
    return this._translate.instant(key, params);
  }

  public get(key: string, params?: Record<string, unknown>) {
    return this._translate.get(key, params);
  }
}
