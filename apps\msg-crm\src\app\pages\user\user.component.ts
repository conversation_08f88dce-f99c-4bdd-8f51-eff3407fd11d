import { ChangeDetectionStrategy, Component, computed, OnInit, signal } from '@angular/core';
import { MsgSkeletonSharedComponent } from '@libs/components/msg-skeleton';
import { MsgTableModule } from '@libs/components/msg-table';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzSelectModule } from 'ng-zorro-antd/select';

@Component({
  selector: 'msg-user',
  templateUrl: './user.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [NzCardModule, NzDatePickerModule, NzSelectModule, MsgTableModule, MsgSkeletonSharedComponent]
})
export class MsgUserComponent implements OnInit {
  public loadingCard = signal(true);

  public userData = signal([
    { id: 1, name: '<PERSON><PERSON><PERSON><PERSON>', email: '<EMAIL>', role: 'Admin', created: '2024-06-01' },
    { id: 2, name: '<PERSON><PERSON><PERSON><PERSON>h<PERSON>', email: '<EMAIL>', role: 'User', created: '2024-06-02' },
    { id: 3, name: 'Lê Văn C', email: '<EMAIL>', role: 'User', created: '2024-06-03' },
    { id: 4, name: 'Phạm Văn D', email: '<EMAIL>', role: 'User', created: '2024-06-04' },
    { id: 5, name: 'Hoàng Thị E', email: '<EMAIL>', role: 'Admin', created: '2024-06-05' },
    { id: 6, name: 'Đỗ Văn F', email: '<EMAIL>', role: 'User', created: '2024-06-06' },
    { id: 7, name: 'Ngô Thị G', email: '<EMAIL>', role: 'User', created: '2024-06-07' },
    { id: 8, name: 'Bùi Văn H', email: '<EMAIL>', role: 'User', created: '2024-06-08' },
    { id: 9, name: 'Vũ Thị I', email: '<EMAIL>', role: 'User', created: '2024-06-09' },
    { id: 10, name: 'Lý Văn J', email: '<EMAIL>', role: 'User', created: '2024-06-10' },
    { id: 11, name: 'Nguyễn Thị K', email: '<EMAIL>', role: 'User', created: '2024-06-11' },
    { id: 12, name: 'Trần Văn L', email: '<EMAIL>', role: 'User', created: '2024-06-12' },
    { id: 13, name: 'Lê Thị M', email: '<EMAIL>', role: 'User', created: '2024-06-13' },
    { id: 14, name: 'Phạm Văn N', email: '<EMAIL>', role: 'User', created: '2024-06-14' },
    { id: 15, name: 'Hoàng Thị O', email: '<EMAIL>', role: 'Admin', created: '2024-06-15' },
    { id: 16, name: 'Đỗ Văn P', email: '<EMAIL>', role: 'User', created: '2024-06-16' },
    { id: 17, name: 'Ngô Thị Q', email: '<EMAIL>', role: 'User', created: '2024-06-17' },
    { id: 18, name: 'Bùi Văn R', email: '<EMAIL>', role: 'User', created: '2024-06-18' },
    { id: 19, name: 'Vũ Thị S', email: '<EMAIL>', role: 'User', created: '2024-06-19' },
    { id: 20, name: 'Lý Văn T', email: '<EMAIL>', role: 'User', created: '2024-06-20' },
    { id: 21, name: 'Nguyễn Thị U', email: '<EMAIL>', role: 'User', created: '2024-06-21' },
    { id: 22, name: 'Trần Văn V', email: '<EMAIL>', role: 'User', created: '2024-06-22' },
    { id: 23, name: 'Lê Thị W', email: '<EMAIL>', role: 'User', created: '2024-06-23' },
    { id: 24, name: 'Phạm Văn X', email: '<EMAIL>', role: 'User', created: '2024-06-24' },
    { id: 25, name: 'Hoàng Thị Y', email: '<EMAIL>', role: 'Admin', created: '2024-06-25' },
    { id: 26, name: 'Đỗ Văn Z', email: '<EMAIL>', role: 'User', created: '2024-06-26' },
    { id: 27, name: 'Ngô Thị AA', email: '<EMAIL>', role: 'User', created: '2024-06-27' },
    { id: 28, name: 'Bùi Văn BB', email: '<EMAIL>', role: 'User', created: '2024-06-28' },
    { id: 29, name: 'Vũ Thị CC', email: '<EMAIL>', role: 'User', created: '2024-06-29' },
    { id: 30, name: 'Lý Văn DD', email: '<EMAIL>', role: 'User', created: '2024-06-30' },
    { id: 31, name: 'Nguyễn Thị EE', email: '<EMAIL>', role: 'User', created: '2024-07-01' },
    { id: 32, name: 'Trần Văn FF', email: '<EMAIL>', role: 'User', created: '2024-07-02' },
    { id: 33, name: 'Lê Thị GG', email: '<EMAIL>', role: 'User', created: '2024-07-03' },
    { id: 34, name: 'Phạm Văn HH', email: '<EMAIL>', role: 'User', created: '2024-07-04' },
    { id: 35, name: 'Hoàng Thị II', email: '<EMAIL>', role: 'Admin', created: '2024-07-05' },
    { id: 36, name: 'Đỗ Văn JJ', email: '<EMAIL>', role: 'User', created: '2024-07-06' },
    { id: 37, name: 'Ngô Thị KK', email: '<EMAIL>', role: 'User', created: '2024-07-07' },
    { id: 38, name: 'Bùi Văn LL', email: '<EMAIL>', role: 'User', created: '2024-07-08' },
    { id: 39, name: 'Vũ Thị MM', email: '<EMAIL>', role: 'User', created: '2024-07-09' },
    { id: 40, name: 'Lý Văn NN', email: '<EMAIL>', role: 'User', created: '2024-07-10' },
  ]);
  public loading = signal(false);

  public userTableConfig = computed(() => ({
    fieldKey: 'id',
    header: [
      { label: 'ID', value: (item: any) => item.id, width: 60, class: 'text-center' },
      { label: 'Tên', value: (item: any) => item.name, width: 150 },
      { label: 'Email', value: (item: any) => item.email, width: 200 },
      { label: 'Vai trò', value: (item: any) => item.role, width: 100 },
      { label: 'Ngày tạo', value: (item: any) => item.created, width: 120 },
    ],
    pagination: {
      showPagination: true,
      pageSize: 10,
      pageSizeOptions: [10, 20, 50],
      sizePagination: 'small'
    }
  }));

  ngOnInit(): void {
    setTimeout(() => this.loadingCard.set(false), 2000);
  }
}
