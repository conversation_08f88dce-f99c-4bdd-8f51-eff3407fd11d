<div class="msg-notification msg-notification-{{ notificationData().type }}"
  (mouseover)="handlerMouseOver($event)"
  (mouseleave)="handlerMouseLeave($event)">

  <div class="msg-notification-icon">
    @switch (notificationData().type) {
      @case ('info') {
        <ng-template [ngTemplateOutlet]="notificationIcon"
          [ngTemplateOutletContext]="{ icon: 'BadgeInfo' }"></ng-template>
      }
      @case ('warning') {
        <ng-template [ngTemplateOutlet]="notificationIcon"
          [ngTemplateOutletContext]="{ icon: 'TriangleAlert' }"></ng-template>
      }
      @case ('danger') {
        <ng-template [ngTemplateOutlet]="notificationIcon"
          [ngTemplateOutletContext]="{ icon: 'MessageSquareWarning' }"></ng-template>
      }
      @case ('success') {
        <ng-template [ngTemplateOutlet]="notificationIcon"
          [ngTemplateOutletContext]="{ icon: 'CheckCheck' }"></ng-template>
      }
    }
  </div>

  <div class="msg-notification-content">
    {{ notificationData().message }}
  </div>

  <msg-button-shared [msgType]="'text'"
    [msgClass]="'msg-notification-close'"
    [msgIconRight]="'X'"
    [msgSize]="'small'"
    (msgClick)="handlerRemoveNotification($event)" />

  @if (notificationData().duration && notificationData().duration! > 0 && msgShowProcessBar()) {
    <div class="msg-notification-progress">
      <div class="msg-notification-progress-bar {{ getProgressBarAnimationClass() }}"
        [style.animation-duration.ms]="notificationData().duration!"
        [class.paused]="notificationData().isPaused"></div>
    </div>
  }
</div>

<ng-template #notificationIcon
  let-icon="icon">
  <msg-icon-shared [msgName]="icon"
    [msgSize]="24"
    [msgStroke]="2" />
</ng-template>
