import { ChangeDetectionStrategy, Component, input, output, signal } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { MsgButtonSharedComponent } from '../msg-button/msg-button.component';
import { MsgIconSharedComponent } from '../msg-icon/msg-icon.component';
import { MsgPopoverSharedComponent } from '../msg-popover/msg-popover.component';
import { MsgSpinnerSharedComponent } from '../msg-spinner/msg-spinner.component';
import { IMsgBellNotificationsDto } from './interfaces/msg-bell-notifications.interface';

@Component({
  selector: 'msg-bell-notifications-shared',
  templateUrl: './msg-bell-notifications.component.html',
  styleUrls: ['./msg-bell-notifications.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    MsgButtonSharedComponent,
    TranslatePipe,
    MsgIconSharedComponent,
    MsgSpinnerSharedComponent,
    MsgPopoverSharedComponent
  ]
})

export class MsgBellNotificationsSharedComponent {
  public msgListNotifications = input<IMsgBellNotificationsDto[]>([]);
  public msgLoading = input<boolean>(true);
  public msgAutoLoad = input<boolean>(false);
  public msgOpen = output<void>();
  public msgMarkAllAsRead = output<void>();

  protected msgVisible = signal<boolean>(false);
  protected msgLeaving = signal<boolean>(false);
  constructor() { }

  protected onMarkAllAsRead(): void {
    this.msgMarkAllAsRead.emit();
  }

  protected onVisibilityChange(isVisible: boolean): void {
    if (isVisible) {
      this.msgOpen.emit();
    }
  }
}
