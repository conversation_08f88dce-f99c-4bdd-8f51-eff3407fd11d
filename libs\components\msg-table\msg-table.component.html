<div class="msg-table-container">
  <div class="msg-table-wrapper"
    #tableWrapper>
    @let hasPagination = msgPagination()?.showPagination && msgData().length;
    @let pagination = msgPagination();
    @if (msgLoading()) {
      <msg-spinner-shared [msgLoading]="msgLoading()" />
    } @else {
      @if (msgData().length) {
        <table #msgTable
          msgResizeObserver
          (msgResize)="onTableResize()"
          [class]="'msg-table msg-table-unified'"
          [attr.has-pagination]="hasPagination ? 'true' : 'false'">
          <ng-content select="thead" />
          <ng-content select="tbody" />
          <ng-content select="tfoot" />
        </table>
      } @else {
        <div class="msg-table-empty">
          <msg-empty-shared [msgDescription]="'i18n_no_data' | translate"
            [msgSizeIcon]="32" />
        </div>
      }
    }
  </div>

  @if (hasPagination && pagination) {
    <div class="msg-table-pagination">
      <nz-pagination [nzPageIndex]="msgPageIndex()"
        [nzDisabled]="msgLoading()"
        [nzSize]="isMobile() ? 'small' : 'default'"
        [nzTotal]="msgTotal()"
        [nzPageSize]="msgPageSize()"
        [nzSize]="pagination.sizePagination || 'default'"
        [nzShowSizeChanger]="pagination.showSizeChanger"
        [nzShowQuickJumper]="pagination.showQuickJumper"
        [nzPageSizeOptions]="pagination.pageSizeOptions!"
        (nzPageIndexChange)="onPageChange($event)"
        (nzPageSizeChange)="onPageSizeChange($event)" />
    </div>
  }
</div>
