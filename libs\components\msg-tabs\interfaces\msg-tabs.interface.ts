import { TYPE_MSG_ICON_NAME } from "../../msg-icon/interfaces/msg-icon.interface";

export type TYPE_MSG_TAB = 'vertical' | 'horizontal';
export type TYPE_MSG_TAB_STYLE = 'border' | 'underline';

export interface IMsgTabDto {
  key: string;
  name: string;
  active?: boolean;
  disabled?: boolean;
  countBadge?: number;
  icon?: TYPE_MSG_ICON_NAME;
}

export interface IMsgTabFunctionControl {
  setDisabled: (tabKey: string, disabled: boolean) => void;
  setActive: (tabKey: string) => void;
  setCountBadge: (tabKey: string, count: number) => void;
}
