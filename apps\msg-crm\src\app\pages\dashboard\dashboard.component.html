<div class="w-full flex flex-col h-[100dvh]">
  <div class="sticky top-0 z-[20] shrink-0">
    <div class="pl-[40px] h-[45px] bg-white flex items-center justify-between md:px-[16px] text-dark font-semibold text-[13px] border-b border-gray-300 uppercase z-[1000]">
      <div class="truncate max-w-[250px] sm:max-w-[500px]">Báo cáo tài chính Dashboard</div>
    </div>
    <div class="w-full">
      <div class="w-full bg-white py-2 px-[16px] shadow-sm">
        <div class="flex flex-col sm:flex-row sm:flex-wrap sm:justify-end items-end gap-2">
          <div class="flex items-center w-full sm:w-auto gap-2">
            <!-- Date Picker -->
            <nz-date-picker [ngModel]="date()"
              nzNoAnimation
              nzFormat="dd/MM/yyyy"
              class="w-full sm:w-[150px] min-w-[150px]"
              (ngModelChange)="handlerChangeDate($event)" />

            <!-- Site Selector -->
            <nz-select nzShowSearch
              [ngModel]="selectedSite()"
              (ngModelChange)="selectedSite.set($event)"
              class="w-full sm:w-[150px] min-w-[150px]"
              nzAllowClear
              nzPlaceHolder="Select a site">
              @for (site of listOfSite(); track site) {
                <nz-option [nzValue]="site"
                  [nzLabel]="site" />
              }
            </nz-select>
          </div>
          <div class="flex items-center gap-2">
            <msg-button-shared class="flex-1 sm:w-[25]"
              [msgText]="'Check Notify'"
              (msgClick)="showNotifyModal()"
              [msgType]="'danger'"
              [msgIconRight]="'Activity'" />

            <msg-button-shared class="flex-1 sm:w-[25]"
              [msgText]="'Xuất báo cáo'"
              (msgClick)="showSuccessModal()"
              [msgType]="'success'"
              [msgIconRight]="'Files'" />
            <msg-button-shared class="flex-1 sm:w-[25]"
              [msgType]="'outline-success'"
              [msgShowTooltip]="true"
              [msgTooltip]="{
                content: 'Check Modal',
                placement: 'top',
                trigger: 'hover'
              }"
              [msgIconRight]="'Activity'"
              (msgClick)="handlerReport()" />

            <msg-popover-shared [msgPlacement]="'bottomRight'"
              [msgTrigger]="'click'"
              (msgFunctionControl)="onFunctionControl($event)"
              [msgContent]="popoverContent">
              <msg-button-shared [msgType]="'outline-secondary'"
                [msgIconRight]="'Funnel'"
                [msgSize]="'default'"
                [msgCount]="10" />
            </msg-popover-shared>
          </div>
        </div>
      </div>
      <!-- <div class="flex flex-wrap items-center gap-2 min-w-max bg-white h-[50px] px-[16px] shadow-sm justify-between">
        <div class="flex items-center gap-2">
          <nz-date-picker [ngModel]="date()"
            nzNoAnimation
            nzFormat="dd/MM/yyyy"
            [class]="'w-[150px] min-w-[150px]'"
            (ngModelChange)="handlerChangeDate($event)" />

          <nz-select nzShowSearch
            [ngModel]="selectedSite()"
            (ngModelChange)="selectedSite.set($event)"
            [class]="'w-[150px] min-w-[150px]'"
            nzAllowClear
            nzPlaceHolder="Select a site">
            @for (site of listOfSite(); track site) {
              <nz-option [nzValue]="site"
                [nzLabel]="site" />
            }
          </nz-select>
        </div>
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-2">
            <msg-button-shared [msgText]="'Check Notify'"
              (msgClick)="showNotifyModal()"
              [msgType]="'danger'"
              [msgIconRight]="'Activity'" />
            <msg-button-shared [msgText]="'Xuất báo cáo'"
              (msgClick)="showSuccessModal()"
              [msgType]="'success'"
              [msgIconRight]="'Files'" />
            <msg-button-shared [msgText]="'Check Modal'"
              [msgType]="'outline-success'"
              [msgIconRight]="'Activity'"
              (msgClick)="handlerReport()" />

          </div>
          <msg-popover-shared [msgPlacement]="'bottomRight'"
            [msgTrigger]="'click'"
            (msgFunctionControl)="onFunctionControl($event)"
            [msgContent]="popoverContent">
            <msg-button-shared [msgType]="'outline-secondary'"
              [msgIconRight]="'Funnel'"
              [msgSize]="'default'"
              [msgCount]="10" />
          </msg-popover-shared>
        </div>
      </div> -->
    </div>
  </div>

  <div class="flex-1 min-h-0 m-[16px] bg-white rounded-[5px] shadow-md p-2">
    <msg-tabs-shared [msgTabs]="tabs()"
      [msgTabType]="'horizontal'"
      (msgTabChange)="handleTabChange($event)"
      #tabsContainer>

      @switch (activeTab()?.key) {
        @case ('financial') {
          <div class="h-full flex flex-col">
            <div class="flex-1 min-h-0 border border-gray-200 rounded overflow-auto">
              @defer (on viewport) {
                <msg-table-wrapper-shared [msgConfig]="tableConfig()"
                  [msgData]="dashboardData()"
                  [msgLoading]="loading()"
                  (msgPageChange)="onPageChange($event)"
                  (msgFunctionControl)="onFunctionControlTable($event)" />
              } @placeholder {
                <msg-spinner-shared [msgLoading]="true" />
              }
            </div>
          </div>
        }
        @case ('summary') {
          @defer (on viewport) {
            <div class="h-full flex flex-col p-4">
              <div class="text-center text-gray-500">
                <h3 class="text-lg font-semibold mb-2">Tổng quan thống kê</h3>
                <p>Chức năng đang được phát triển</p>
              </div>
            </div>
          } @placeholder {
            <msg-spinner-shared [msgLoading]="true" />
          }
        }
        @default {
          <div class="h-full p-4 text-center text-gray-500">
            Không có nội dung để hiển thị
          </div>
        }
      }
    </msg-tabs-shared>
  </div>
</div>

<!-- Action template -->
<ng-template #actionTemplate
  let-item>
  <msg-menu-action-shared (msgClickAction)="handlerActionClick($event, item)">
    <msg-button-shared [msgType]="'outline-secondary'"
      [msgSize]="'small'"
      [msgIconSize]="18"
      [msgIconRight]="'Ellipsis'" />
  </msg-menu-action-shared>
</ng-template>

<!-- Expand template -->
<ng-template #expandTemplate
  let-item
  let-index>
  @if (item.children) {
    <msg-table-wrapper-shared [msgConfig]="expandTableConfig()"
      [msgData]="item.children" />
  } @else {
    <div class="p-4 text-gray-500">
      Không có dữ liệu mở rộng cho mục này.
    </div>
  }
</ng-template>

<ng-template #popoverContent>
  <div class="w-[300px] bg-white rounded-[20px] p-[16px] z-50 overflow-auto flex flex-col gap-[8px]">
    <div class="flex flex-col gap-[4px]">
      <label class="text-[12px] font-[450] text-gray-700 w-full">Filter by site</label>
      <nz-select [nzMaxTagCount]="1"
        [class]="'w-full'"
        [nzMaxTagPlaceholder]="tagPlaceHolder"
        nzMode="multiple"
        nzPlaceHolder="Please select"
        [(ngModel)]="listOfSelectedValue">
        @for (item of listOfSite(); track item) {
          <nz-option [nzLabel]="item"
            [nzValue]="item"></nz-option>
        }
      </nz-select>
      <ng-template #tagPlaceHolder
        let-selectedList>+{{ selectedList.length }} selected</ng-template>
    </div>

    <div class="flex flex-col gap-[4px]">
      <label class="text-[12px] font-[450] text-gray-700 w-full">Filter by site</label>
      <nz-select [nzMaxTagCount]="1"
        [class]="'w-full'"
        [nzMaxTagPlaceholder]="tagPlaceHolder"
        nzMode="multiple"
        nzPlaceHolder="Please select"
        [(ngModel)]="listOfSelectedValue">
        @for (item of listOfSite(); track item) {
          <nz-option [nzLabel]="item"
            [nzValue]="item"></nz-option>
        }
      </nz-select>
      <ng-template #tagPlaceHolder
        let-selectedList>and {{ selectedList.length }} more selected</ng-template>
    </div>

    <div class="flex gap-[8px] mt-[5px] justify-end">
      <msg-button-shared [msgText]="'i18n_cancel'"
        [msgType]="'outline-secondary'"
        [msgClass]="'min-w-[70px]'"
        (msgClick)="handlerPopover()">

      </msg-button-shared>
      <msg-button-shared [msgText]="'i18n_apply'"
        [msgType]="'primary'"
        [msgClass]="'min-w-[70px]'"
        (msgClick)="handlerPopover()" />
    </div>
  </div>
</ng-template>
