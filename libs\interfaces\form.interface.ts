import { FormArray, FormControl, FormGroup } from '@angular/forms';
type TYPE_TO_PRIMITIVE<T> =
  T extends string ? string :
  T extends number ? number :
  T extends boolean ? boolean :
  T;

type TYPE_CONTROL_FORM<T> =
  T extends (infer U)[] ? FormArray<TYPE_CONTROL_FORM<U>> :
  T extends object ? FormGroup<TYPE_FORM<T>> :
  FormControl<TYPE_TO_PRIMITIVE<T> | null>;

export type TYPE_FORM<T> = {
  [K in keyof T]: TYPE_CONTROL_FORM<T[K]>;
};

export type TYPE_KEY<T> = {
  [key: string]: T;
};
