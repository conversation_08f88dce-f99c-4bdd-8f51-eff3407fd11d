import { Directive, ElementRef, inject, input, OnDestroy, output } from '@angular/core';

@Directive({
  selector: '[msgResizeObserver], [msg-resize-observer]',
})

export class MsgResizeObserverDirective implements OnDestroy {
  private _observer: ResizeObserver;
  private _el = inject(ElementRef);
  private _debounceTimer: ReturnType<typeof setTimeout> | null = null;

  public msgDebounce = input<number>(100);
  public msgResize = output<DOMRectReadOnly>();

  constructor() {
    this._observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        this._debouncedEmit(entry.contentRect);
      }
    });

    this._observer.observe(this._el.nativeElement);
  }

  private _debouncedEmit(contentRect: DOMRectReadOnly): void {
    if (this._debounceTimer) {
      clearTimeout(this._debounceTimer);
    }

    this._debounceTimer = setTimeout(() => {
      this.msgResize.emit(contentRect);
    }, this.msgDebounce());
  }

  ngOnDestroy() {
    if (this._debounceTimer) {
      clearTimeout(this._debounceTimer);
    }

    this._observer.unobserve(this._el.nativeElement);
    this._observer.disconnect();
  }
}
