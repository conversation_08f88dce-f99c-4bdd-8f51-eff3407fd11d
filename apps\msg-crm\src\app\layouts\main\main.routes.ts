import { Route } from '@angular/router';
import { RoutesConstant } from '../../shared/constants/routes.define';

export const mainRoutes: Route[] = [
  {
    path: '',
    loadComponent: () => import('./layout/layout.component').then(m => m.Msg<PERSON>ainLayoutComponent),
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      },
      {
        path: RoutesConstant.LOGIN,
        loadComponent: () => import('../../pages/auth/login/login.component').then(m => m.MsgLoginComponent)
      },
      {
        path: RoutesConstant.DASHBOARD,
        loadComponent: () => import('../../pages/dashboard/dashboard.component').then(m => m.MsgDashboardComponent)
      },
      {
        path: RoutesConstant.USER,
        loadComponent: () => import('../../pages/user/user.component').then(m => m.MsgUserComponent)
      },
      {
        path: RoutesConstant.FIS_DASHBOARD,
        loadComponent: () => import('../../pages/fis-dashboard/fis-dashboard.component').then(m => m.MsgFisDashboardComponent)
      },
      {
        path: RoutesConstant.PAGE_404,
        loadComponent: () => import('@libs/components/msg-page-404').then(m => m.MsgPage404SharedComponent)
      },
      {
        path: RoutesConstant.PAGE_401,
        loadComponent: () => import('@libs/components/msg-page-401').then(m => m.MsgPage401SharedComponent)
      },
      {
        path: RoutesConstant.PAGE_403,
        loadComponent: () => import('@libs/components/msg-page-403').then(m => m.MsgPage403SharedComponent)
      },
      {
        path: '**',
        loadComponent: () => import('@libs/components/msg-page-404').then(m => m.MsgPage404SharedComponent)
      }
    ]
  }
];
