import { NgOptimizedImage } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { TranslatePipe } from '@ngx-translate/core';
import { MsgRoutesConstant } from '../../constants/msg-router.define';
import { MsgButtonSharedComponent } from '../msg-button/msg-button.component';

@Component({
  selector: 'msg-page-401-shared',
  templateUrl: './msg-page-401.component.html',
  styleUrls: ['./msg-page-401.component.scss'],
  standalone: true,
  imports: [
    TranslatePipe,
    MsgButtonSharedComponent,
    NgOptimizedImage
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class MsgPage401SharedComponent {
  private _router = inject(Router);

  protected onBackHomeClick(): void {
    this._router.navigate([MsgRoutesConstant.DASHBOARD]);
  }
}
