import { DOCUMENT } from '@angular/common';
import { Directive, ElementRef, inject, input, output } from '@angular/core';

@Directive({
  selector: '[msg-click-outside], [msgClickOutside]',
  standalone: true,
  host: {
    '(document:click)': 'handleDocumentClick($event)',
    '(document:touchstart)': 'handleDocumentClick($event)'
  }
})
export class MsgClickOutsideDirective {
  public msgClickOutsideIgnore = input<string[]>([]);
  public msgClickOutside = output<void>();
  private readonly _document = inject(DOCUMENT);
  private readonly _elementRef = inject(ElementRef<HTMLElement>);

  public handleDocumentClick(event: Event): void {
    const target = event.target as Node;

    if (!target || !this._elementRef.nativeElement) {
      return;
    }

    if (this._elementRef.nativeElement.contains(target)) {
      return;
    }

    if (this._isClickedOnIgnoredElement(target)) {
      return;
    }

    this.msgClickOutside.emit();
  }

  private _isClickedOnIgnoredElement(target: Node): boolean {
    const ignoredIds = this.msgClickOutsideIgnore();

    if (ignoredIds.length === 0) {
      return false;
    }

    return ignoredIds.some(elementId => {
      const element = this._document.getElementById(elementId);
      return element?.contains(target) ?? false;
    });
  }
}
