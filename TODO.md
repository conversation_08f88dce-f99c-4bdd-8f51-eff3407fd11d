1.<PERSON><PERSON>m sortkey table
2.<PERSON><PERSON><PERSON> comp label
3.<PERSON><PERSON><PERSON> comp empty
4.<PERSON><PERSON><PERSON> page 404
5.<PERSON>àm abstract service
6.Làm cache service with indexdb, localstorage, cache memory
7.Làm interceptor provider
8.Làm guard provider
9.<PERSON>àm demo compoent page
10.<PERSON>àm pipe permission, service permission
11.Update device screen service
12.Update component language
13.Làm comp step

docker cp "C:\Users\<USER>\Desktop\angular-workspace-msg\output\msg-crm" msg:/var/www/msg-crm

docker cp "C:\Users\<USER>\Desktop\angular-workspace-msg\dist\apps\msg-crm" msg:/var/www/msg-crm
docker exec msg rm -rf /var/www/msg-crm
docker cp C:\Users\<USER>\Desktop\angular-workspace-msg\dist\apps\msg-crm msg:/var/www/

docker cp C:\Users\<USER>\Desktop\angular-workspace-msg\dist\apps\msg-crm msg:/var/www
