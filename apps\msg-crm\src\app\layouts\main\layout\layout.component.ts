import { Component, signal } from "@angular/core";
import { RouterOutlet } from "@angular/router";
import { MsgAlertVersionSharedComponent } from "@libs/components/msg-alert-version";
import { IMsgAlertVersionDto } from "@libs/components/msg-alert-version/interfaces";
import { MsgSidebarMenuSharedComponent } from '@libs/components/msg-sidebar-menu';
import { IMsgControlSidebarMenuDto, IMsgMenuDto } from "@libs/components/msg-sidebar-menu/interfaces";
import { MENU_APP } from '../../../shared/constants/menu.define';
import { MsgMainFooterComponent } from "../footer/footer.component";
import { MsgHeaderComponent } from "../header/header.component";

@Component({
  selector: "msg-main-layout",
  templateUrl: "./layout.component.html",
  styleUrls: ["./layout.component.scss"],
  standalone: true,
  imports: [
    MsgHeaderComponent,
    MsgMainFooterComponent,
    MsgSidebarMenuSharedComponent,
    RouterOutlet,
    MsgAlertVersionSharedComponent
  ]
})

export class MsgMainLayoutComponent {
  protected menus = signal<IMsgMenuDto[]>(MENU_APP);
  protected functionControlSidebar = signal<IMsgControlSidebarMenuDto | undefined>(undefined);
  protected infoApp = signal<IMsgAlertVersionDto>({
    version: '1.0.0',
    message: 'Welcome to MSG CRM',
  });

  protected handlerFunctionControlSidebar(functionControl: IMsgControlSidebarMenuDto): void {
    this.functionControlSidebar.set(functionControl);
  }
}
