import { Pipe, PipeTransform } from '@angular/core';
import { IMsgTableColumn } from '../interfaces';

@Pipe({
  name: 'msgTableCellStyle',
  standalone: true
})
export class MsgTableCellStylePipe implements PipeTransform {
  transform<T>(value: IMsgTableColumn['style'] | IMsgTableColumn['class'], item: T, index: number, data: T[]): string {
    if (!value) {
      return '';
    }

    return typeof value === 'string' ? value : value(item, index, data);
  }
}
