<header class="h-[45px] top-0 w-[200px] fixed border-b border-gray-300 flex justify-end items-center right-0 pr-[16px] bg-white z-[50]">
  <div class="flex items-center justify-end w-full h-full *:flex-nowrap gap-[20px]">
    <!-- Language Selector -->
    <!-- <div class="msg-header-change-language cursor-pointer"
      [title]="'i18n_language' | translate">
      <nz-select [ngModel]="currentLanguage()"
        [class]="'min-w-[120px] max-w-[120px]'"
        (ngModelChange)="handlerChangeLanguage($event)"
        [nzSize]="'default'"
        [nzDropdownMatchSelectWidth]="true"
        [nzShowSearch]="false"
        [nzBorderless]="true"
        [nzDropdownClassName]="'msg-header-change-language-select'">
        @for (language of languages(); track language.value) {
          <nz-option [nzValue]="language.value"
            [nzLabel]="language.label | translate">
            <span class="flex items-center gap-1">
              <img [src]=""
                class="w-[16px] h-[16px] rounded-full" />
              <span class="text-xs">{{ language.label | translate }}</span>
            </span>
          </nz-option>
        }
      </nz-select>
    </div> -->

    <!-- Notifications -->
    <msg-bell-notifications-shared [msgListNotifications]="msgListNotifications()"
      [msgLoading]="msgLoading()"
      (msgOpen)="handlerFetchNotifications()" />
    <!-- User Avatar -->
    <nz-avatar nzIcon="user"
      nz-popover
      [nzSrc]="msgLogo()"
      [nzPopoverPlacement]="'bottomRight'"
      [nzPopoverTrigger]="'click'"
      [nzPopoverVisible]="isPopoverVisible()"
      (click)="handlerTogglePopover($event)"
      (nzPopoverVisibleChange)="handleVisibleChange($event)"
      [nzPopoverContent]="contentTemplate"
      [nzPopoverOverlayClassName]="'msg-popover-content'"
      [class]="'msg-avatar-border-shadow cursor-pointer min-w-[32px]'" />
  </div>
</header>

<ng-template #contentTemplate>
  <div class="w-[200px] bg-white rounded-[20px] pt-[8px]">
    <div class="flex flex-col gap-1 px-[16px]">
      <div class="text-dark font-medium text-sm truncate"
        title="Vũ Ngọc Bội">Vũ Ngọc Bội</div>
      <div class="text-gray-500 text-xs truncate min-w-0"
        title="<EMAIL>">boi.vn&#64;matsaigon.com</div>
    </div>
    <nz-divider [class]="'my-2!'" />
    <div class="flex flex-col gap-1 p-[4px]">
      <div class="flex items-center gap-1 cursor-pointer hover:bg-green-100 px-[16px] py-[8px] rounded-[3px]">
        <msg-icon-shared msgName="User" />
        <span class="text-xs font-medium text-nowrap truncate min-w-0">{{ 'i18n_profile' | translate }}</span>
      </div>
      <div class="flex items-center gap-1 cursor-pointer hover:bg-red-100 px-[16px] py-[8px] rounded-[3px]"
        (click)="handlerLogOut($event)">
        <msg-icon-shared msgName="LogOut" />
        <span class="text-xs font-medium text-nowrap truncate min-w-0">{{ 'i18n_logout' | translate }}</span>
      </div>
    </div>
  </div>
</ng-template>
