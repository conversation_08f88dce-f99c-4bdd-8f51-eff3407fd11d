$breakpoint-xs: 480px;
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

@mixin respond-to($breakpoint) {
  @if $breakpoint =='mobile' {
    @media (min-width: #{$breakpoint-xs}) {
      @content;
    }
  }

  @else if $breakpoint =='tablet' {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  }

  @else if $breakpoint =='desktop' {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }

  @else if $breakpoint =='desktop-xl' {
    @media (min-width: #{$breakpoint-xl}) {
      @content;
    }
  }

  @else if $breakpoint =='desktop-2xl' {
    @media (min-width: #{$breakpoint-2xl}) {
      @content;
    }
  }

  @else {
    @warn "Invalid breakpoint: #{$breakpoint}. Use: mobile, tablet, desktop, desktop-xl, desktop-2xl";
  }
}

@mixin respond-to-max($breakpoint) {
  @if $breakpoint =='mobile' {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }

  @else if $breakpoint =='tablet' {
    @media (max-width: #{$breakpoint-md - 1px}) {
      @content;
    }
  }

  @else if $breakpoint =='desktop' {
    @media (max-width: #{$breakpoint-lg - 1px}) {
      @content;
    }
  }

  @else if $breakpoint =='desktop-xl' {
    @media (max-width: #{$breakpoint-xl - 1px}) {
      @content;
    }
  }

  @else if $breakpoint =='desktop-2xl' {
    @media (max-width: #{$breakpoint-2xl - 1px}) {
      @content;
    }
  }

  @else {
    @warn "Invalid breakpoint: #{$breakpoint}. Use: mobile, tablet, desktop, desktop-xl, desktop-2xl";
  }
}

.msg-mobile-only {
  @include respond-to('tablet') {
    display: none !important;
  }
}

.z {
  @include respond-to-max('mobile') {
    display: none !important;
  }
}

.msg-desktop-up {
  @include respond-to-max('tablet') {
    display: none !important;
  }
}
