/* eslint-disable @typescript-eslint/no-explicit-any */
import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams } from "@angular/common/http";
import { inject, Injectable } from "@angular/core";
import { TranslateService } from "@ngx-translate/core";
import { Observable, of } from "rxjs";
import { catchError } from "rxjs/operators";
import { MsgNotificationService } from "./msg-notification.service";

@Injectable({
  providedIn: 'root'
})
export abstract class MsgHttpAbstractService {
  protected abstract baseUrl: string;
  private _http = inject(HttpClient);
  private _notificationService = inject(MsgNotificationService);
  private _translateService = inject(TranslateService);

  private _setHeaders(): HttpHeaders {
    let headers = new HttpHeaders();
    const userToken = localStorage.getItem('userToken');
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Accept', 'application/json');

    if (userToken) {
      headers = headers.set('Authorization', `Bearer ${userToken}`);
    }

    return headers;
  }

  private _handleError(error: Error): Observable<any> {
    const statusCode = error instanceof HttpErrorResponse ? error.status : 500;
    switch (statusCode) {
      case 500:
        this._notificationService.danger(this._translateService.instant('i18n_error_code_500'));
        break;
      case 404:
        this._notificationService.danger(this._translateService.instant('i18n_error_code_404'));
        break;
      case 401:
        this._notificationService.danger(this._translateService.instant('i18n_error_code_401'));
        break;

      default:
        this._notificationService.danger(error.message);
        break;
    }

    return of(null);
  }

  protected get<T>(endpoint: string, params?: HttpParams): Observable<T> {
    const options = { headers: this._setHeaders(), params: params };
    return this._http.get<T>(`${this.baseUrl}/${endpoint}`, options).pipe(
      catchError(this._handleError)
    );
  }

  protected post<T>(endpoint: string, body: any): Observable<T> {
    const options = { headers: this._setHeaders() };
    return this._http.post<T>(`${this.baseUrl}/${endpoint}`, body, options).pipe(
      catchError(this._handleError)
    );
  }

  protected put<T>(endpoint: string, body: any): Observable<T> {
    const options = { headers: this._setHeaders() };
    return this._http.put<T>(`${this.baseUrl}/${endpoint}`, body, options).pipe(
      catchError(this._handleError)
    );
  }

  protected delete<T>(endpoint: string): Observable<T> {
    const options = { headers: this._setHeaders() };
    return this._http.delete<T>(`${this.baseUrl}/${endpoint}`, options).pipe(
      catchError(this._handleError)
    );
  }
}
