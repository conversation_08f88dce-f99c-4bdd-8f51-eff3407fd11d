@if (msgMenuItem(); as menuItem) {
  @if (menuItem.isCategory) {
    <div class="msg-menu-item-category"
      [class.collapsed]="msgCollapsed()">
      <span class="msg-menu-item-category-name">
        {{ menuItem.name }}
      </span>
    </div>
  } @else {
    @let showSubMenu = menuItem?.children?.length && msgCollapsed();
<div class="msg-menu-item"
  [class.expanded]="menuItem?.expanded">
  <a nz-popover
    [nzPopoverPlacement]="'rightTop'"
    [nzPopoverTrigger]="showSubMenu ? 'click': null"
    [nzPopoverVisible]="showSubMenu ? msgShowSubMenuTablet() : false"
    (click)="handlerShowSubMenuTablet($event, menuItem)"
    (nzPopoverVisibleChange)="handlerPopoverChange($event)"
    [nzPopoverContent]="subMenuContent"
    [nzPopoverOverlayClassName]="'msg-popover-content msg-popover-content-menu-item'"
    nz-tooltip
    [nzTooltipTitle]="(menuItemName | msgTruncate: msgCollapsed() | async) ? menuItem?.name : ''"
    [nzTooltipPlacement]="'right'"
    [class.active-desktop]="(msgCollapsed() && msgMenuParentActive())"
    [class.active]="menuItem?.active"
    [class.hover]="menuHover()?.id === menuItem?.id"
    [class.collapsed]="msgCollapsed()"
    class="msg-menu-link"
    (click)="handlerClickMenuItem($event, menuItem)"
    (mouseleave)="handlerLeaveMenuItem($event)"
    (mouseenter)="handlerMouseMenuItem($event, menuItem)">
  <div class="msg-menu-icon-container"
    [class.flex-1]="!msgCollapsed()"
    [class.collapsed-padding]="msgCollapsed()">
    @if (menuItem?.icon) {
      <msg-icon-shared [msgName]="menuItem.icon!"
        [msgSize]="16"
        [msgClass]="msgTextColorClass()"
        class="msg-icon-shared" />
    } @if (menuItem?.iconCustom) {
      <div class="msg-menu-item-icon-svg"
        aria-label="menuItem.name"
        [style.mask-image]="'url(' + menuItem.iconCustom + ')'"
        [style.-webkit-mask-image]="'url(' + menuItem.iconCustom + ')'"
        [style.background-color]="'var(--color-green-600)'">
      </div>
    }

    <span #menuItemName
      [class.collapsed]="msgCollapsed()"
      [class]="'msg-menu-item-name ' + msgTextColorClass()">
      {{ menuItem?.name }}
    </span>
    </div>

    @if (menuItem?.children?.length) {
      <msg-icon-shared [msgName]="'ChevronRightIcon'"
        class="msg-menu-arrow-collapsed"
        [class.collapsed]="msgCollapsed()"
        [class.msg-animate-rotate-clockwise]="menuItem.expanded"
        [class.msg-animate-rotate-default]="!menuItem.expanded"
        [msgClass]="msgArrowIconColorClass()"
        [msgSize]="16" />
    }
    </a>

    @if (menuItem?.children?.length) {
      <div class="msg-submenu-container"
        [attr.hideMenuChild]="msgCollapsed()"
        [class.expanded]="menuItem.expanded"
        [class.collapsed]="!menuItem.expanded">
        <div class="msg-submenu-border-vertical"></div>
        <div class="msg-submenu-content">
          @for (childMenu of menuItem.children; track trackByMenuId($index, childMenu);) {
            <msg-menu-item [msgMenuItem]="childMenu"
              class="msg-menu-item-child"
              [msgCollapsed]="msgCollapsed()"
              [msgMenuChangeUpdateParent]="msgMenuChangeUpdateParent()"
              (msgMenuActive)="msgMenuActive.emit($event)" />
          }
        </div>
      </div>
    }
    </div>
  }
}

<ng-template #subMenuContent>
  <div class="msg-popover-submenu">
    @if (msgMenuItem(); as menuItem) {
      <!-- Menu Title (sticky header) -->
      @if (menuItem.name) {
        <div class="menu-title menu-title-sticky">
          <div class="menu-title-row">
            <div class="menu-title-content">
              <span class="menu-title-text">
                {{ menuItem.name }}
              </span>
            </div>
          </div>
        </div>
      }

      <!-- Scrollable Menu Items Container -->
      <div class="menu-items-container menu-items-scrollable">
        @if (menuItem.children?.length) {
          @for (childMenu of menuItem.children; track trackByMenuId($index, childMenu)) {
            <ng-container [ngTemplateOutlet]="menuTemplate"
              [ngTemplateOutletContext]="{ menu: childMenu }" />
          }
        }
      </div>
    }
  </div>
</ng-template>

<ng-template #menuTemplate
  let-menu="menu"
  let-level="level">
  <!-- Menu Item Display -->
  <div class="menu-item-row">
    <div class="menu-item-content"
      [class.active]="menu.active"
      (click)="handlerClickSubmenuItem(menu)">
      <div class="menu-item-info">
        @if (menu.icon) {
          <msg-icon-shared [msgName]="menu.icon"
            [msgSize]="16"
            [msgClass]="menu.active ? 'text-gray-900' : 'text-gray-700'"
            [class]="'msg-icon-shared'" />
        }
        <span class="menu-item-text"
          [class.!text-gray-900]="menu.active">
          {{ menu.name }}
        </span>
      </div>

      @if (menu.children?.length) {
        <msg-icon-shared [msgName]="'ChevronRightIcon'"
          [msgSize]="16"
          [msgClass]="menu.active ? 'text-gray-900' : 'text-gray-700'"
          [class]="'menu-item-arrow'"
          [class.rotated]="menu.expanded" />
      }
    </div>
    <!-- Children với auto nesting và animation -->
    @if (menu?.children?.length) {
      <div class="msg-submenu-container"
        [class.expanded]="menu.expanded"
        [class.collapsed]="!menu.expanded">
        <div class="msg-submenu-border-vertical"></div>
        <div class="msg-submenu-content">
          @for (childMenu of menu.children; track trackByMenuId($index, childMenu)) {
            <ng-container [ngTemplateOutlet]="menuTemplate"
              [ngTemplateOutletContext]="{ menu: childMenu }" />
          }
        </div>
      </div>
    }
  </div>
</ng-template>
