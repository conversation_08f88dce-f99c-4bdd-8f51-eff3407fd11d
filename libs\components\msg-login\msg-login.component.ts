import { ChangeDetectionStrategy, Component, input } from "@angular/core";
import { MsgLoginTemplateOneSharedComponent } from "./msg-template-one/msg-template-one.component";
import { MsgLoginTemplateThreeSharedComponent } from "./msg-template-three/msg-template-three.component";
import { MsgLoginTemplateTwoSharedComponent } from "./msg-template-two/msg-template-two.component";

@Component({
  selector: 'msg-login-shared',
  templateUrl: './msg-login.component.html',
  standalone: true,
  imports: [
    MsgLoginTemplateOneSharedComponent,
    MsgLoginTemplateTwoSharedComponent,
    MsgLoginTemplateThreeSharedComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class MsgLoginSharedComponent {
  public msgTemplate = input<number>(1);
  constructor() { }
}
