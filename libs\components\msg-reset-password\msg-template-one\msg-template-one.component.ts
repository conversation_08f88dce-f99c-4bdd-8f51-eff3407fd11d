import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslatePipe } from '@ngx-translate/core';
import { MsgButtonSharedComponent } from "../../msg-button/msg-button.component";
import { MsgIconSharedComponent } from '../../msg-icon/msg-icon.component';
import { MsgValidateModule } from '../../msg-validate/msg-validate.module';
import { MsgResetPasswordAbstractComponent } from '../msg-reset-password-abstract.component';

@Component({
  selector: 'msg-reset-password-template-one',
  templateUrl: './msg-template-one.component.html',
  styleUrls: ['./msg-template-one.component.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    MsgIconSharedComponent,
    MsgButtonSharedComponent,
    MsgValidateModule,
    TranslatePipe
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class MsgResetPasswordTemplateOneComponent extends MsgResetPasswordAbstractComponent { }
