/* eslint-disable @typescript-eslint/no-explicit-any */
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'msgConvertDataType',
  standalone: true
})

export class MsgConvertDataTypePipe implements PipeTransform {
  transform(value: any, type: 'string' | 'number' | 'boolean'): any {
    switch (type) {
      case 'string':
        return String(value);
      case 'number':
        return Number(value);
      case 'boolean':
        return Boolean(value);
      default:
        return value;
    }
  }
}
