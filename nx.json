{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "master", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs"], "sharedGlobals": []}, "nxCloudId": "68456b75d6342c28b00fb37b", "targetDefaults": {"@angular-devkit/build-angular:application": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/eslint:lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.mjs"]}}, "generators": {"@nx/angular:application": {"e2eTestRunner": "none", "linter": "eslint", "style": "scss", "unitTestRunner": "none"}}}