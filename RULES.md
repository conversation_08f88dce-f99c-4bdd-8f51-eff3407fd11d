# Angular Development Rules

## Category: Code Quality

### Rule: Expert Angular Programming
You are an expert Angular programmer using TypeScript, Angular 18 and Jest that focuses on producing clear, readable code.

### Rule: Thoughtful Development
You are thoughtful, give nuanced answers, and are brilliant at reasoning. You carefully provide accurate, factual, thoughtful answers and are a genius at reasoning.

### Rule: Step-by-Step Approach
Before providing an answer, think step by step, and provide a detailed, thoughtful answer. If you need more information, ask for it.

## Category: Code Standards

### Rule: Correct Code
Always write correct, up to date, bug free, fully functional and working code.

### Rule: Performance Focus
Focus on performance, readability, and maintainability. Before providing an answer, double check your work.

### Rule: Proper Imports
Include all required imports, and ensure proper naming of key components.

## Category: Code Structure

### Rule: Nesting Limit
Do not nest code more than 2 levels deep.

### Rule: ForNext Function
Prefer using the forNext function, located in libs/smart-ngrx/src/common/for-next.function.ts instead of for(let i;i < length;i++), forEach or for(x of y).

### Rule: Code Standards Compliance
Code should obey the rules defined in the .eslintrc.json, .prettierrc, .htmlhintrc, and .editorconfig files.

## Category: Function Guidelines

### Rule: Parameter Limit
Functions and methods should not have more than 4 parameters.

### Rule: Function Length
Functions should not have more than 50 executable lines.

### Rule: Line Length
Lines should not be more than 80 characters.

## Category: Documentation

### Rule: JSDoc Preservation
When refactoring existing code, keep jsdoc comments intact.

### Rule: Concise Communication
Be concise and minimize extraneous prose.

### Rule: Honesty
If you don't know the answer to a request, say so instead of making something up.

## Category: ESLint Configuration

### Rule: ESLint Compliance
Code must strictly follow all ESLint rules defined in .eslintrc.json. No ESLint warnings or errors are allowed.

### Rule: TypeScript Strict Mode
Use TypeScript strict mode settings. Enable strict null checks, strict function types, and no implicit any.

### Rule: Import Organization
Organize imports in the following order: Angular imports, third-party libraries, relative imports. Use ESLint auto-fix for import sorting.

### Rule: Prefer Const
Use 'const' by default, 'let' only when reassignment is needed. Never use 'var'.

### Rule: Arrow Functions
Prefer arrow functions over function declarations for callbacks and short functions.

## Category: Angular Modern API

### Rule: Signal API Required
Must use Angular Signals API instead of traditional reactive patterns. Use signal(), computed(), effect() for state management.

### Rule: Modern Input/Output
Use new input() and output() functions instead of @Input() and @Output() decorators.
```typescript
// ✅ Correct - New API
name = input<string>('');
nameChange = output<string>();

// ❌ Wrong - Old API  
@Input() name: string = '';
@Output() nameChange = new EventEmitter<string>();
```

### Rule: Control Flow Syntax
Use Angular 18+ control flow syntax in templates:
- @if/@else instead of *ngIf
- @for instead of *ngFor  
- @switch/@case instead of *ngSwitch

```html
<!-- ✅ Correct - New syntax -->
@if (user) {
  <p>Welcome {{user.name}}</p>
} @else {
  <p>Please login</p>
}

@for (item of items; track item.id) {
  <div>{{item.name}}</div>
}

<!-- ❌ Wrong - Old syntax -->
<p *ngIf="user">Welcome {{user.name}}</p>
<div *ngFor="let item of items; trackBy: trackFn">{{item.name}}</div>
```

### Rule: Computed Values
Use computed() for derived state instead of getters or manual calculations.

```typescript
// ✅ Correct
fullName = computed(() => `${this.firstName()} ${this.lastName()}`);

// ❌ Wrong
get fullName() {
  return `${this.firstName} ${this.lastName}`;
}
```

### Rule: Effect Usage
Use effect() for side effects that respond to signal changes. Prefer computed() over effect() when possible.

```typescript
// ✅ Correct
effect(() => {
  console.log('User changed:', this.user());
});
```

## Category: Template Best Practices

### Rule: Track Functions
Always provide track functions in @for loops for performance optimization.

### Rule: Signal in Templates
Access signals directly in templates with parentheses: {{signal()}}

### Rule: Conditional Rendering
Use @if/@else blocks instead of hidden properties or *ngIf with async pipe.

## Category: Performance Optimization

### Rule: No Functions in Templates
Do not call functions directly in HTML templates as they will be executed on every change detection cycle, causing performance issues. Use computed properties, signals, or memoization instead.

Bad:
```html
<div>{{ calculateTotal() }}</div>
<div *ngIf="isValid()">Content</div>
```

Good:
```html
<div>{{ totalSignal() }}</div>
<div>{{ computedTotal() }}</div>
@if (isValidSignal()) {
  <div>Content</div>
}
```

### Rule: Use Memoization for Complex Calculations
For complex calculations that don't need reactive updates, use memoization or move logic to component methods with proper caching.

## Category: Styling Guidelines

### Rule: Tailwind CSS Priority
Use Tailwind CSS utility classes as the primary styling approach. Prefer utility classes over custom CSS whenever possible for consistency and maintainability.

```html
<!-- ✅ Correct - Tailwind utilities -->
<div class="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">
  <h2 class="text-xl font-semibold text-gray-800">Title</h2>
</div>

<!-- ❌ Wrong - Custom CSS classes -->
<div class="custom-card">
  <h2 class="custom-title">Title</h2>
</div>
```

### Rule: Leverage Existing SCSS Files
Utilize the existing SCSS architecture in libs/assets/scss/:
- Import _layout.scss for layout utilities and grid systems
- Use _animations.scss for consistent animation patterns
- Apply _ng-zorro.scss for component library customizations
- Implement _responsive.scss for breakpoint mixins
- Use _scrollbar.scss for custom scrollbar styling

### Rule: SCSS Import Order
Import SCSS files in the following order in your component styles:
```scss
@import 'libs/assets/scss/layout';
@import 'libs/assets/scss/animations';
@import 'libs/assets/scss/responsive';
// Component-specific styles below
```

### Rule: Animation Consistency
Use animations from _animations.scss instead of creating new ones. If new animations are needed, add them to the shared file for reusability.

```scss
// ✅ Correct - Use existing animations
.fade-in {
  @include fadeInAnimation();
}

// ❌ Wrong - Custom animation
.my-fade {
  animation: myCustomFade 0.3s ease-in;
}
```

### Rule: Responsive Design
Use mixins from _responsive.scss for consistent breakpoint handling:

```scss
.component {
  @include mobile {
    padding: 1rem;
  }
  
  @include tablet {
    padding: 2rem;
  }
  
  @include desktop {
    padding: 3rem;
  }
}
```

### Rule: Layout Utilities
Prefer layout utilities from _layout.scss for common layout patterns like flex containers, grid systems, and spacing utilities.

### Rule: Tailwind + SCSS Combination
When Tailwind utilities are insufficient, combine them with SCSS mixins/functions from existing files rather than writing pure CSS:

```html
<div class="flex items-center custom-layout">
```

```scss
.custom-layout {
  @include layoutMixin();
  // Additional custom styles
}
```

## Category: Component Structure Rules

### Rule: Component Interface Index Files
Only create index.ts files in interfaces folders of shared component libraries. Do not create index files in other component directories.

```
// ✅ Correct structure
libs/components/msg-button/interfaces/
├── index.ts
└── msg-button.interface.ts

// ❌ Wrong - No index in main component folder  
libs/components/msg-button/
├── index.ts ❌ 
└── msg-button.component.ts
```

## Category: Tailwind CSS Modern Usage

### Rule: No @apply Directive
Do not use Tailwind's @apply directive in SCSS/CSS files. Tailwind CSS 4.1+ has deprecated this syntax. Use utility classes in HTML templates or convert to standard CSS properties.

```scss
// ❌ Wrong - @apply deprecated
.button {
  @apply bg-blue-500 text-white px-4 py-2;
}

// ✅ Correct - Standard CSS properties with Tailwind variables and px units
.button {
  background-color: var(--color-blue-500);
  color: var(--color-white);
  padding: 16px 32px;
}
```

```html
<!-- ✅ Best - Utility classes in template -->
<button class="bg-blue-500 text-white px-4 py-2">Button</button>
```

## Category: TypeScript Configuration

### Rule: Component Aliases Required
Always add TypeScript path aliases in tsconfig.base.json for every new shared component for better import organization and shorter paths.

```json
// ✅ Required alias pattern
"@libs/components/msg-component": [
  "libs/components/msg-component/msg-component.component.ts"
],
"@libs/components/msg-component/interfaces": [
  "libs/components/msg-component/interfaces/index.ts"
]
```

## Category: CSS/SCSS Units and Styling

### Rule: Use Pixels Over REM and Tailwind CSS Variables
Use px units instead of rem for consistency across the project in SCSS and CSS files. Utilize Tailwind CSS custom properties for colors and other design tokens.

```scss
// ✅ Correct - px units with Tailwind variables
.component {
  padding: 16px;
  margin: 8px;
  font-size: 14px;
  background-color: var(--color-blue-500);
  color: var(--color-gray-900);
  border: 1px solid var(--color-gray-300);
}

// ❌ Wrong - rem units and hardcoded colors
.component {
  padding: 1rem;
  margin: 0.5rem;
  font-size: 0.875rem;
  background-color: #3b82f6;
  color: #111827;
}
```

### Rule: Simple Dash-Separated Class Names
Use simple dash-separated class names instead of BEM methodology for better readability and consistency.

```scss
// ✅ Correct - Simple dash separation
.msg-spinner-icon {
  animation: spin 1s linear infinite;
  color: var(--color-blue-600);
}

.msg-spinner-text {
  font-weight: 500;
  margin-top: 8px;
}

.msg-spinner-overlay {
  position: fixed;
  background-color: var(--color-black);
}

// ❌ Wrong - BEM methodology
.msg-spinner__icon {
  animation: spin 1s linear infinite;
}

.msg-spinner__text {
  font-weight: 500;
}

.msg-spinner--overlay {
  position: fixed;
}
```

### Rule: Modern SCSS Imports with @use
Use @use instead of @import for SCSS files. Take advantage of simplified paths available in project.json configuration.

```scss
// ✅ Correct - @use with simplified paths
@use 'layout';
@use 'animations';
@use 'responsive';

// ❌ Wrong - @import with full paths
@import 'libs/assets/scss/layout';
@import 'libs/assets/scss/animations';
@import 'libs/assets/scss/responsive';
```

### Rule: SCSS Path Configuration Check
Before using simplified @use paths, verify the paths are configured in project.json. Use the simplified syntax when available.

```scss
// ✅ Available simplified imports (check project.json)
@use 'animations';
@use 'layout';
@use 'responsive';
@use 'ng-zorro';
@use 'scrollbar';
```
