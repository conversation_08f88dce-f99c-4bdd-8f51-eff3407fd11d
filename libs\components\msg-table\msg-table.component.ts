/* eslint-disable @typescript-eslint/no-explicit-any */
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, effect, ElementRef, inject, input, OnDestroy, OnInit, output, Renderer2, signal, viewChild } from "@angular/core";
import { toSignal } from '@angular/core/rxjs-interop';
import { TranslatePipe } from "@ngx-translate/core";
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { MsgResizeObserverDirective } from "../../directives";
import { MsgDeviceService } from "../../services";
import { ENUM_DEVICE_TYPE } from "../../services/interfaces/msg-device.interface";
import { MsgSpinnerSharedComponent } from "../msg-spinner/msg-spinner.component";
import { IMsgTableConfig, IMsgTableFunctionControl, IMsgTablePagination } from "./interfaces/msg-table.interfaces";

@Component({
  selector: 'msg-table-shared',
  templateUrl: './msg-table.component.html',
  styleUrls: ['./msg-table.component.scss'],
  host: { class: 'msg-table-shared' },
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    MsgResizeObserverDirective,
    MsgSpinnerSharedComponent,
    NzPaginationModule,
    TranslatePipe
  ],
})
export class MsgTableSharedComponent<T = unknown> implements OnInit, OnDestroy, AfterViewInit {
  public msgLoading = input<boolean>(false);
  public msgData = input<T[]>([]);
  public msgDataFooter = input<T[]>([]);
  public msgPagination = input<IMsgTableConfig['pagination']>({
    isServerSide: false,
    showSizeChanger: true,
    showQuickJumper: false,
    showPagination: true,
    pageSizeOptions: [10, 20, 30, 40, 50],
    total: 0
  });

  public msgPageChange = output<IMsgTablePagination>();
  public msgFunctionControl = output<IMsgTableFunctionControl>();

  private _renderer = inject(Renderer2);
  private _msgDeviceService = inject(MsgDeviceService);
  private _hasHandleResize = signal<boolean>(true);
  private _msgTable = viewChild<ElementRef<HTMLDivElement>>('msgTable');
  private _tableWrapper = viewChild<ElementRef<HTMLDivElement>>('tableWrapper');
  private _msgDevice = toSignal(this._msgDeviceService.deviceType$);

  public msgPageIndex = signal<number>(1);
  public msgPageSize = signal<number>(10);
  public msgTotal = signal<number>(0);
  public isMobile = computed(() => this._msgDevice() === ENUM_DEVICE_TYPE.MOBILE);
  private _localData = signal<T[]>([]);
  private _stickyRight = signal<boolean>(false);

  // Observers for DOM changes
  private _mutationObserver?: MutationObserver;
  private _resizeObserver?: ResizeObserver;

  public footerData = computed(() => {
    return this.msgDataFooter();
  });

  ngOnInit(): void {
    this.msgFunctionControl.emit({
      onUpdate: this._onUpdate.bind(this),
      onDelete: this._onDelete.bind(this),
      getPagination: this.getPagination.bind(this)
    });
  }

  ngAfterViewInit(): void {
    this._setupObservers();
    this._initializeTable();
  }

  constructor() {
    // Consolidated effect to handle all state changes
    effect(() => {
      const data = this.msgData();
      const loading = this.msgLoading();
      const pagination = this.msgPagination();
      const pageSize = this.msgPageSize();
      const pageIndex = this.msgPageIndex();

      this._handleStateChanges(data, loading, pagination, pageSize, pageIndex);
    });
  }

  protected onTableResize(): void {
    this._setupStickyTBody();
    this.onTableScroll();
  }

  public get tableElement(): ElementRef<HTMLDivElement> | undefined {
    return this._msgTable();
  }

  public get paginatedData(): any[] {
    if (!this.msgPagination()?.showPagination || this.msgPagination()?.isServerSide) {
      return this._localData();
    }

    const start = (this.msgPageIndex() - 1) * this.msgPageSize();
    return this._localData().slice(start, start + this.msgPageSize());
  }

  public getPagination(): IMsgTablePagination {
    return {
      pageIndex: this.msgPageIndex(),
      pageSize: this.msgPageSize(),
      total: this.msgTotal()
    };
  }

  private _onUpdate(item: T): void {
    const currentPageStart = (this.msgPageIndex() - 1) * this.msgPageSize();
    const allData = [...this._localData()];
    const index = allData.findIndex(x => x === item);
    if (index !== -1) {
      allData.splice(index, 1);
    }

    allData.splice(currentPageStart, 0, item);
    this._localData.set(allData);
    this._setupTableClasses();
    this._setupRowColSpan();
  }

  private _onDelete(item: T): void {
    const allData = [...this._localData()];
    const index = allData.findIndex(x => x === item);
    if (index === -1) { return; }

    allData.splice(index, 1);

    if (this.msgPagination()?.isServerSide) {
      this._localData.set(allData);
      this._setupTableClasses();
      this._setupRowColSpan();
      return;
    }

    this._handleClientDelete(allData);
  }

  private _handleClientDelete(allData: T[]): void {
    const currentPageIndex = this.msgPageIndex();
    const pageSize = this.msgPageSize();
    const totalPages = Math.ceil(allData.length / pageSize);

    if (allData.length === 0 || currentPageIndex > totalPages) {
      if (currentPageIndex > 1) {
        this.msgPageIndex.set(currentPageIndex - 1);
      }
      this._localData.set(allData);
      this._setupTableClasses();
      this._setupRowColSpan();
      return;
    }

    const currentPageStart = (currentPageIndex - 1) * pageSize;
    const currentPageItems = allData.slice(currentPageStart, currentPageStart + pageSize);
    if (currentPageItems.length < pageSize && currentPageIndex < totalPages) {
      const remainingItems = allData.slice(currentPageStart + currentPageItems.length);
      const itemsNeeded = Math.min(pageSize - currentPageItems.length, remainingItems.length);

      if (itemsNeeded > 0) {
        const itemsToMove = remainingItems.slice(0, itemsNeeded);
        allData.splice(currentPageStart + currentPageItems.length, itemsToMove.length);
        allData.splice(currentPageStart + currentPageItems.length, 0, ...itemsToMove);
      }
    }

    if (allData.slice(currentPageStart, currentPageStart + pageSize).length === 0 && currentPageIndex > 1) {
      this.msgPageIndex.set(currentPageIndex - 1);
    }

    this._localData.set(allData);
    this._setupTableClasses();
    this._setupRowColSpan();
  }

  public onPageChange(pageIndex: number): void {
    this.msgPageIndex.set(pageIndex);
    this._emitPaginationChange();
  }

  public onPageSizeChange(pageSize: number): void {
    this.msgPageSize.set(pageSize);
    this.msgPageIndex.set(1);
    this._emitPaginationChange();
  }

  private _emitPaginationChange(): void {
    this.msgPageChange.emit({
      pageIndex: this.msgPageIndex(),
      pageSize: this.msgPageSize(),
      total: this.msgTotal()
    });
  }

  private _resetScrollAndRecalculate(): void {
    requestAnimationFrame(() => {
      const wrapper = this._tableWrapper()?.nativeElement;
      if (wrapper) {
        wrapper.scrollLeft = 0;
        wrapper.scrollTop = 0;
      }

      this._setupTableClasses();
      this._setupRowColSpan();
      this._setupStickyTBody();
      this._setupHoverHandling();
    });
  }

  protected onTableScroll(): void {
    const target = this._tableWrapper()?.nativeElement;
    if (!target) {
      return;
    }

    const scrollLeft = target.scrollLeft;
    const scrollWidth = target.scrollWidth;
    const clientWidth = target.clientWidth;

    const hasScrollLeft = scrollLeft > 0;
    const hasScrollRight = scrollLeft > 0 && (scrollLeft + 1) < scrollWidth - clientWidth;
    const tableWrapper = target.parentElement;
    if (tableWrapper) {
      tableWrapper.classList.toggle('msg-table-scroll-left', hasScrollLeft);
      tableWrapper.classList.toggle('msg-table-scroll-right', scrollLeft === 0 && this._stickyRight() ? true : hasScrollRight);
    }
  }

  private _setupTableClasses(): void {
    const table = this._msgTable()?.nativeElement;
    if (!table) {
      return;
    }

    const _processTableSection = (
      table: HTMLElement,
      sectionSelector: string,
      sectionClass: string,
      cellSelector: string,
      cellClassPrefix: string,
      addIndex: boolean) => {
      const section = table.querySelector(sectionSelector);
      if (!section) {
        return;
      }

      this._renderer.addClass(section, sectionClass);
      const rowsThead = section.querySelectorAll(':scope > tr');
      rowsThead.forEach(row => {
        this._renderer.addClass(row, `${cellClassPrefix}-tr`);
        const cells = row.querySelectorAll(cellSelector);
        cells.forEach((cell, index) => {
          this._renderer.addClass(cell, `${cellClassPrefix}-${cellSelector}`);
          if (addIndex) {
            this._renderer.setAttribute(cell, 'msg-index', (index + 1).toString());
          }
        });
      });
    };

    this._renderer.addClass(table, 'msg-table');
    _processTableSection(table, 'thead', 'msg-table-thead', 'th', 'msg-table-thead', true);
    _processTableSection(table, 'tbody', 'msg-table-tbody', 'td', 'msg-table-tbody', true);
    _processTableSection(table, 'tfoot', 'msg-table-tfoot', 'td', 'msg-table-tfoot', true);
  }

  private _setupRowColSpan(): void {
    const table = this._msgTable()?.nativeElement;
    if (!table) {
      return;
    }

    const elementRowCol = table.querySelectorAll(':scope > thead > tr > td[msg-row], :scope > thead > tr > th[msg-row], :scope > tbody > tr > td[msg-row], :scope > tbody > tr > th[msg-row], :scope > tfoot > tr > td[msg-row], :scope > tfoot > tr > th[msg-row], :scope > thead > tr > td[msg-col], :scope > thead > tr > th[msg-col], :scope > tbody > tr > td[msg-col], :scope > tbody > tr > th[msg-col], :scope > tfoot > tr > td[msg-col], :scope > tfoot > tr > th[msg-col]');

    if (!elementRowCol || elementRowCol.length === 0) {
      return;
    }

    const cellsToSkip = new Set<string>();
    elementRowCol.forEach(el => {
      const cell = el as HTMLElement;
      const rowSpan = el.getAttribute('msg-row') || el.getAttribute('rowspan');
      const colSpan = el.getAttribute('msg-col') || el.getAttribute('colspan');
      if (rowSpan && parseInt(rowSpan) > 1) {
        const shouldRender = this._shouldRenderRowSpanCell(cell, cellsToSkip);
        if (!shouldRender) {
          this._renderer.setStyle(cell, 'display', 'none');
          return;
        }
        this._renderer.setAttribute(cell, 'rowspan', rowSpan);
      }

      if (colSpan && parseInt(colSpan) > 1) {
        this._renderer.setAttribute(cell, 'colspan', colSpan);
      }
    });
  }

  private _getColIndex(row: HTMLTableRowElement, cell: HTMLTableCellElement): number {
    const cells = Array.from(row.children) as HTMLTableCellElement[];
    let index = 0;
    for (const c of cells) {
      if (c === cell) { break; }
      const colspan = parseInt(c.getAttribute('colspan') || '1');
      index += colspan;
    }
    return index;
  }

  private _shouldRenderRowSpanCell(cell: HTMLElement, cellsToSkip: Set<string>): boolean {
    const row = cell.parentElement as HTMLTableRowElement;
    if (!row || !(cell instanceof HTMLTableCellElement)) { return true; }

    const table = this._msgTable()?.nativeElement;
    if (!table) { return false; }

    const parentSection = row.parentElement as HTMLTableSectionElement;
    if (!parentSection) { return true; }

    const rows = Array.from(parentSection.querySelectorAll(':scope > tr:not(.msg-tr-expand)'));
    const currentRowIndex = rows.indexOf(row);
    const colIndex = this._getColIndex(row, cell);

    const key = `${currentRowIndex}-${colIndex}`;
    if (cellsToSkip.has(key)) { return false; }

    const rowSpan = parseInt(cell.getAttribute('msg-row') || '1');
    if (rowSpan > 1) {
      const groupIdRandom = Date.now().toString(36) + Math.random().toString(36).substring(2, 5);
      const groupId = `row-group-${groupIdRandom}`;
      for (let i = 0; i < rowSpan; i++) {
        const targetRow = rows[currentRowIndex + i];
        if (targetRow) {
          targetRow.setAttribute('msg-index-number', groupId);
        }
      }

      for (let i = 1; i < rowSpan; i++) {
        const skipKey = `${currentRowIndex + i}-${colIndex}`;
        cellsToSkip.add(skipKey);
      }
    }

    return true;
  }

  private _handleRowHover(row: HTMLTableRowElement, isHover: boolean): void {
    const table = this._msgTable()?.nativeElement;
    if (!table) { return; }

    const groupId = row.getAttribute('msg-index-number');
    if (!groupId) { return; }

    const allRows = table.querySelectorAll(`tr[msg-index-number="${groupId}"]`);

    requestAnimationFrame(() => {
      allRows.forEach(r => {
        const cells = Array.from(r.children) as HTMLTableCellElement[];
        cells.forEach(cell => {
          if (isHover) {
            this._renderer.addClass(cell, 'msg-row-hover');
          } else {
            this._renderer.removeClass(cell, 'msg-row-hover');
          }
        });
      });
    });
  }

  private _calculateStickyTheadTfoot(cell: HTMLElement, direction: 'left' | 'right' = 'left'): number {
    const table = this._msgTable()?.nativeElement;
    let offset = 0;
    if (!table) {
      return offset;
    }

    const cellRect = cell.getBoundingClientRect();
    const tableRect = table.getBoundingClientRect();
    const currentCellLeft = Math.round(cellRect.left - tableRect.left);
    const currentCellRight = Math.round(cellRect.right - tableRect.left);

    const allTrs = Array.from(table.querySelectorAll(':scope > thead > tr.msg-table-thead-tr')) as HTMLTableRowElement[];
    if (!allTrs.length) {
      return offset;
    }

    const stickyCellsMap: Map<number, { cell: HTMLElement, width: number }> = new Map();
    const targetClass = direction === 'left' ? 'msg-sticky-left' : 'msg-sticky-right';

    allTrs.forEach((tr) => {
      const cells = Array.from(tr.children) as HTMLElement[];
      cells.forEach((cellElement) => {
        const isSticky = cellElement.className.includes(targetClass);

        if (isSticky) {
          const cellRect = cellElement.getBoundingClientRect();
          const cellLeft = Math.round(cellRect.left - tableRect.left);
          const cellRight = Math.round(cellRect.right - tableRect.left);

          const key = direction === 'left' ? cellLeft : cellRight;
          if (!stickyCellsMap.has(key) || stickyCellsMap.get(key)?.cell !== cellElement) {
            stickyCellsMap.set(key, {
              cell: cellElement,
              width: cellRect.width
            });
          }
        }
      });
    });

    const sortedStickyCells = Array.from(stickyCellsMap.entries()).sort((a, b) => {
      return direction === 'left' ? a[0] - b[0] : b[0] - a[0];
    });

    for (const [position, { cell: stickyCell, width }] of sortedStickyCells) {
      if (stickyCell === cell) {
        break;
      }

      if (direction === 'left' && position < currentCellLeft) {
        offset += width;
        continue;
      }

      if (direction === 'right' && position > currentCellRight) {
        offset += width;
        continue;
      }
    }

    return offset;
  }

  private _setupStickyShadow(cellIndex: number, cells: HTMLElement[], direction: 'left' | 'right'): void {
    if (cells.length === 0) {
      return;
    }

    const shadowClass = direction === 'left' ? 'msg-sticky-shadow-left' : 'msg-sticky-shadow-right';
    const cellElement = cells[cellIndex];
    if (!cellElement) {
      return;
    }

    const isThElement = cellElement.tagName.toLowerCase() === 'th';
    if (direction === 'right' && isThElement) {
      if (cellElement === cells[0]) {
        this._renderer.addClass(cellElement, shadowClass);
      }
    }


    if (cellIndex === cells.length - 1 && isThElement && direction === 'left') {
      this._renderer.addClass(cells[cellIndex], shadowClass);
    }

    const isTdElement = cellElement.tagName.toLowerCase() === 'td';
    const targetIndex = direction === 'left' ? cells.length - 1 : 0;
    if (cellIndex === targetIndex && isTdElement) {
      this._renderer.addClass(cells[cellIndex], shadowClass);
    }
  }

  private _setupStickyTheadTfoot(): void {
    const table = this._msgTable()?.nativeElement;
    if (!table) {
      return;
    }

    const headerRows = table.querySelectorAll(':scope > thead.msg-table-thead > tr.msg-table-thead-tr');
    const handleSticky = (rows: NodeListOf<Element>) => {
      const cellsLeftInfo: Array<{ cell: HTMLElement, leftOffset: number, index: number }> = [];
      const cellsRightInfo: Array<{ cell: HTMLElement, rightOffset: number, index: number }> = [];
      rows.forEach(tr => {
        const cellsLeft = Array.from(tr.children).filter(cell => cell.className.includes('msg-sticky-left')) as HTMLElement[];
        cellsLeft.forEach((cell, index) => {
          const leftOffset = this._calculateStickyTheadTfoot(cell as HTMLElement, 'left');
          cellsLeftInfo.push({ cell, leftOffset, index });
        });

        const cellsRight = Array.from(tr.children).filter(cell => cell.className.includes('msg-sticky-right')) as HTMLElement[];
        cellsRight.forEach((cell, index) => {
          const rightOffset = this._calculateStickyTheadTfoot(cell as HTMLElement, 'right');
          cellsRightInfo.push({ cell, rightOffset, index });
        });
        cellsLeftInfo.forEach(({ cell, leftOffset, index }) => {
          // this._renderer.setStyle(cell, 'outline', '0.5px solid var(--color-gray-200)');
          this._setupStickyShadow(index, cellsLeft, 'left');
          requestAnimationFrame(() => {
            this._renderer.setStyle(cell, 'left', `${leftOffset}px`);
          });
        });

        cellsRightInfo.forEach(({ cell, rightOffset, index }) => {
          // this._renderer.setStyle(cell, 'outline', '0.5px solid var(--color-gray-200)');
          this._setupStickyShadow(index, cellsRight, 'right');
          requestAnimationFrame(() => {
            this._renderer.setStyle(cell, 'right', `${rightOffset}px`);
          });
        });
      });
    };

    if (headerRows && headerRows.length !== 0) {
      handleSticky(headerRows);
    }

    const footerRows = table.querySelectorAll(':scope > tfoot.msg-table-tfoot > tr.msg-table-tfoot-tr');
    if (footerRows && footerRows.length !== 0) {
      handleSticky(footerRows);
    }
  }

  private _setupStickyTBody(): void {
    const table = this._msgTable()?.nativeElement;
    if (!table) {
      return;
    }

    const bodyRows = table.querySelectorAll(':scope > tbody.msg-table-tbody > tr.msg-table-tbody-tr');
    if (!bodyRows || bodyRows.length === 0) {
      return;
    }

    const updates: Array<{ rowIndex: number, cells: HTMLElement[], direction: 'left' | 'right' }> = [];

    bodyRows.forEach((tr, index) => {
      const cellsLeft = Array.from(tr.children).filter(cell => cell.className.includes('msg-sticky-left')) as HTMLElement[];
      const cellsRight = Array.from(tr.children).filter(cell => cell.className.includes('msg-sticky-right')) as HTMLElement[];

      if (cellsLeft.length > 0) {
        updates.push({ rowIndex: index, cells: cellsLeft, direction: 'left' });
      }

      if (cellsRight.length > 0) {
        updates.push({ rowIndex: index, cells: cellsRight, direction: 'right' });
      }
    });

    updates.forEach(({ rowIndex, cells, direction }) => {
      this._calculateStickyBody(bodyRows, rowIndex, cells, direction);
    });
  }

  private _setupHoverHandling(): void {
    const table = this._msgTable()?.nativeElement;
    if (!table) {
      return;
    }

    const rows = table.querySelectorAll(':scope > tbody.msg-table-tbody > tr.msg-table-tbody-tr, :scope > tfoot.msg-table-tfoot > tr.msg-table-tfoot-tr');
    rows.forEach(row => {
      this._renderer.listen(row, 'mouseenter', () => {
        this._handleRowHover(row as HTMLTableRowElement, true);
      });

      this._renderer.listen(row, 'mouseleave', () => {
        this._handleRowHover(row as HTMLTableRowElement, false);
      });
    });
  }

  private _calculateStickyBody(
    bodyRows: NodeListOf<Element>,
    indexTr: number,
    cells: HTMLElement[],
    direction: 'left' | 'right' = 'left'
  ): void {
    const total = cells.length;
    const getCellWidth = (cell: HTMLElement): number => {
      return cell.getBoundingClientRect().width;
    };

    const findCellInAnotherRow = (colIndex: number): HTMLElement | null => {
      let count = 0;
      for (let i = indexTr - 1; i >= 0 && count < 4; i--, count++) {
        const row = bodyRows[i];
        const rowCells = Array.from(row.children) as HTMLElement[];
        const otherCell = rowCells[colIndex];
        if (otherCell?.style.display !== 'none') {
          return otherCell;
        }
      }
      return null;
    };

    const getOffset = (cellIndex: number): number => {
      let offset = 0;

      const indices =
        direction === 'left'
          ? [...Array(cellIndex).keys()]
          : [...Array(total - cellIndex - 1).keys()].map(i => total - 1 - i);

      for (const i of indices) {
        const cell = cells[i];
        if (cell.style.display === 'none') {
          const altCell = findCellInAnotherRow(i);
          if (altCell) { offset += getCellWidth(altCell); }
          continue;
        }

        offset += getCellWidth(cell);
      }

      return offset;
    };

    const iterateIndices = direction === 'left' ? [...cells.keys()] : [...cells.keys()].reverse();
    iterateIndices.forEach((index) => {
      const cell = cells[index];
      const offset = getOffset(index);
      this._renderer.setStyle(cell, direction, `${offset}px`);
      this._setupStickyShadow(index, cells, direction);
    });
  }

  /**
   * Setup observers for DOM changes instead of using interval polling
   */
  private _setupObservers(): void {
    const table = this._msgTable()?.nativeElement;
    if (!table) { return; }

    // Setup MutationObserver to watch for DOM changes
    this._mutationObserver = new MutationObserver(() => {
      this._handleDOMChanges();
    });

    this._mutationObserver.observe(table, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class']
    });

    // Setup ResizeObserver to watch for size changes
    this._resizeObserver = new ResizeObserver(() => {
      this._handleTableResize();
    });

    this._resizeObserver.observe(table);
  }

  /**
   * Initialize table setup
   */
  private _initializeTable(): void {
    // Use requestAnimationFrame to ensure DOM is ready
    requestAnimationFrame(() => {
      this._setupStickyTheadTfoot();
      this._calculateStickyRight();
    });
  }

  /**
   * Handle DOM changes detected by MutationObserver
   */
  private _handleDOMChanges(): void {
    const table = this._msgTable()?.nativeElement;
    const firstStickyRightElement = table?.querySelector('.msg-sticky-right');

    if (firstStickyRightElement) {
      this._setupStickyTheadTfoot();
      this._calculateStickyRight();
    }
  }

  /**
   * Handle table resize detected by ResizeObserver
   */
  private _handleTableResize(): void {
    this._calculateStickyRight();
    this._setupStickyTBody();
  }

  /**
   * Calculate sticky right state
   */
  private _calculateStickyRight(): void {
    const tableElement = this._msgTable()?.nativeElement;
    const parentTable = tableElement?.parentElement;
    const parentWidthViewPort = parentTable?.clientWidth || 0;
    const scrollContentWidth = tableElement?.scrollWidth || 0;
    this._stickyRight.set(scrollContentWidth > parentWidthViewPort);
  }

  /**
   * Consolidated state change handler
   */
  private _handleStateChanges(
    data: T[],
    loading: boolean,
    pagination: IMsgTableConfig['pagination'],
    pageSize: number,
    pageIndex: number
  ): void {
    // Handle data changes
    this._localData.set([...data]);

    // Handle pagination changes
    if (pagination?.isServerSide) {
      this.msgTotal.set(pagination.total || 0);
    } else {
      this.msgTotal.set(this._localData().length);
      if (pagination?.pageSizeOptions?.length) {
        this.msgPageSize.set(pagination.pageSizeOptions[0]);
      }
    }

    // Handle page size/index changes
    if (pageSize && pageIndex) {
      this._resetScrollAndRecalculate();
    }

    // Setup table after data/loading changes
    this._setupTableClasses();
    this._setupRowColSpan();

    // Handle loading state changes
    if (!loading) {
      this._setupStickyTheadTfoot();

      requestAnimationFrame(() => {
        this._setupStickyTBody();
        this._setupHoverHandling();
      });
    }
  }

  ngOnDestroy(): void {
    // Cleanup observers
    this._mutationObserver?.disconnect();
    this._resizeObserver?.disconnect();

    // Reset signals
    this._localData.set([]);
    this.msgPageIndex.set(1);
    this.msgPageSize.set(10);
    this.msgTotal.set(0);
    this._hasHandleResize.set(false);
  }
}
