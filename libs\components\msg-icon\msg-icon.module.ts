import { NgModule } from "@angular/core";
import { Activity, AlignJustify, ArrowLeft, ArrowRight, BadgeInfo, BarChart3, BellMinus, BellRing, Box, Briefcase, ChartColumnIcon, Check, CheckCheck, ChevronDown, ChevronDownIcon, ChevronLeft, ChevronLeftIcon, ChevronRight, ChevronRightIcon, ChevronsLeft, ChevronsRight, ChevronUpIcon, Cog, Crown, Edit, Ellipsis, Eye, EyeClosed, EyeOff, FileDown, Files, Funnel, Inbox, Info, Layers, LayoutDashboardIcon, LayoutGrid, LoaderCircle, Lock, LogOut, LogsIcon, LucideAngularModule, Mail, Menu, MenuIcon, MessageSquareDot, MessageSquareWarning, Minus, MousePointer, Package, PackageOpen, PanelLeftClose, PanelLeftOpen, PieChart, Plus, PlusIcon, Search, Settings, ShoppingCart, SlidersHorizontal, Target, Trash2, TrendingUp, TriangleAlert, User, UserCheck, Users, UsersRoundIcon, WarehouseIcon, Workflow, X, Zap } from "lucide-angular";

export const MSG_ICONS = {
  MessageSquareDot, CheckCheck, TriangleAlert, MessageSquareWarning, Check, Edit, Mail, LayoutGrid, ArrowLeft, ArrowRight, Menu, Lock, LogOut, User, Settings, ChevronLeft, ChevronRight, ChevronDown, Plus, Search, Users, Eye, EyeClosed, EyeOff, Ellipsis, PanelLeftClose, PanelLeftOpen, LayoutDashboardIcon, ChartColumnIcon, UsersRoundIcon, LogsIcon, ChevronDownIcon, ChevronUpIcon, ChevronRightIcon, BarChart3, TrendingUp, Package, ShoppingCart, UserCheck, Activity, Zap, MousePointer, Info, Layers, Briefcase, Target, PieChart, Workflow, WarehouseIcon, MenuIcon, ChevronLeftIcon, LoaderCircle, ChevronsLeft, ChevronsRight, BellRing, SlidersHorizontal, Trash2, FileDown, Funnel, AlignJustify, X, Files, Crown, BadgeInfo, PlusIcon, Minus, Box, Cog, BellMinus, PackageOpen, Inbox
} as const;

@NgModule({
  imports: [LucideAngularModule.pick(MSG_ICONS)],
  exports: [LucideAngularModule],
})
export class MsgIconModule { }
