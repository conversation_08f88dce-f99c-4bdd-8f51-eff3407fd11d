# Scripts cho Angular Workspace

## Scripts có sẵn

### Smart Runner (scripts/smart-runner.js)
Script thông minh tự động setup và chạy project:

```bash
npm run list                 # Hiển thị danh sách projects
npm run start <project-name>  # Chạy project cụ thể
npm run build <project-name>  # Build project cụ thể
```

**Chức năng tự động:**
- ✅ Kiểm tra và cài đặt node_modules nếu chưa có
- ✅ Kiểm tra và thiết lập <PERSON>sky hooks nếu chưa có
- ✅ Liệt kê các project có thể chạy
- ✅ Chạy project được chỉ định

### Validate Commit (scripts/validate-commit.js)
Script kiểm tra format commit message (được Husky gọi tự động).

## Husky Hooks

### Pre-commit Hook
- Tự động chạy `lint-staged` trước khi commit
- Lint và format các file đã thay đổi
- Chặn commit nếu có lỗi lint

### Commit Message Hook
- Kiểm tra format commit message
- B<PERSON>t buộc phải có prefix: `feat:`, `fix:`, `chore:`, etc.

## Format Commit Message

```
<type>[optional scope]: <description>
```

### Các type được phép:
- `feat`: Tính năng mới
- `fix`: Sửa lỗi
- `docs`: Chỉ thay đổi documentation
- `style`: Thay đổi format code (không ảnh hưởng logic)
- `refactor`: Refactor code
- `test`: Thêm hoặc sửa tests
- `chore`: Thay đổi build tools, dependencies
- `perf`: Cải thiện performance
- `ci`: Thay đổi CI/CD
- `build`: Thay đổi build system
- `revert`: Revert commit trước
- `init`: Commit đầu tiên

### Ví dụ:
```bash
git commit -m "feat: add user authentication"
git commit -m "fix(auth): resolve login validation issue"
git commit -m "chore(deps): update Angular to v19"
git commit -m "docs: update README with setup instructions"
```

### Prettier format in package.json

```json
"lint-staged": {
  "*.{ts,js,html}": [
    "eslint --fix",
    "prettier --write"
  ],
  "*.{scss,css}": [
    "prettier --write"
  ]
}
```
