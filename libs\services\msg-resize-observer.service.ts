import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class MsgResizeObserverService implements OnDestroy {
  private _observer: ResizeObserver;

  constructor() {
    this._observer = new ResizeObserver((entries) => {
      entries.forEach(entry => {
        const element = entry.target as HTMLElement;
        element.dispatchEvent(new CustomEvent('onResized', {
          detail: entry.contentRect
        }));
      });
    });
  }

  protected observe(element: HTMLElement) {
    this._observer.observe(element);
  }

  protected unobserve(element: HTMLElement) {
    this._observer.unobserve(element);
  }

  ngOnDestroy() {
    this._observer.disconnect();
  }
}
