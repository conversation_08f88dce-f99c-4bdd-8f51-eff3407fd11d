// msg-device.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ENUM_DEVICE_TYPE, IDeviceScreenSizeDto } from './interfaces/msg-device.interface';

@Injectable({
  providedIn: 'root'
})
export class MsgDeviceService {
  private static readonly _BREAKPOINTS = {
    MOBILE: 480,
    TABLET: 768,
    DESKTOP: 1024
  };

  private _deviceTypeSubject = new BehaviorSubject<ENUM_DEVICE_TYPE>(this._detectDeviceType());
  private _screenSizeSubject = new BehaviorSubject<IDeviceScreenSizeDto>(this._getScreenSize());
  private _resizeObserver: ResizeObserver;

  constructor() {
    this._resizeObserver = new ResizeObserver(() => this._updateDeviceInfo());
    this._resizeObserver.observe(document.body);
  }

  public get deviceType$(): Observable<ENUM_DEVICE_TYPE> {
    return this._deviceTypeSubject.asObservable();
  }

  public get screenSize$(): Observable<IDeviceScreenSizeDto> {
    return this._screenSizeSubject.asObservable();
  }

  public get currentDeviceType(): ENUM_DEVICE_TYPE {
    return this._deviceTypeSubject.value;
  }

  public get currentScreenSize(): IDeviceScreenSizeDto {
    return this._screenSizeSubject.value;
  }

  private _updateDeviceInfo(): void {
    this._deviceTypeSubject.next(this._detectDeviceType());
    this._screenSizeSubject.next(this._getScreenSize());
  }

  private _detectDeviceType(): ENUM_DEVICE_TYPE {
    const width = window.innerWidth;
    if (width <= MsgDeviceService._BREAKPOINTS.MOBILE) {
      return ENUM_DEVICE_TYPE.MOBILE;
    }

    if (width <= MsgDeviceService._BREAKPOINTS.TABLET) {
      return ENUM_DEVICE_TYPE.TABLET;
    }

    return ENUM_DEVICE_TYPE.DESKTOP;
  }

  private _getScreenSize(): IDeviceScreenSizeDto {
    return {
      width: window.innerWidth,
      height: window.innerHeight
    };
  }
}
