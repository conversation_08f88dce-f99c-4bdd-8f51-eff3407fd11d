import { ChangeDetectionStrategy, Component, input } from "@angular/core";
import { MsgResetPasswordTemplateOneComponent } from "./msg-template-one/msg-template-one.component";

@Component({
  selector: 'msg-reset-password-shared',
  templateUrl: './msg-reset-password.component.html',
  styleUrls: ['./msg-reset-password.component.scss'],
  standalone: true,
  imports: [
    MsgResetPasswordTemplateOneComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class MsgResetPasswordSharedComponent {
  public msgTemplate = input<number>(1);
  constructor() { }
}
