import { ChangeDetectionStrategy, Component, input, OnInit, output, signal } from '@angular/core';
import { NzTSType } from 'ng-zorro-antd/core/types';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzTooltipTrigger } from 'ng-zorro-antd/tooltip';
import { TYPE_POPOVER_PLACEMENT } from '../../interfaces/common.interface';
import { IMsgPopoverFunctionControl } from './interfaces/msg-popover.interface';

@Component({
  selector: 'msg-popover-shared',
  templateUrl: './msg-popover.component.html',
  styleUrls: ['./msg-popover.component.scss'],
  standalone: true,
  imports: [
    NzPopoverModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class MsgPopoverSharedComponent implements OnInit {
  public msgPlacement = input<TYPE_POPOVER_PLACEMENT>('left');
  public msgTrigger = input<NzTooltipTrigger | undefined>('click');
  public msgContent = input<NzTSType | undefined>(undefined);
  public msgOverlayClassName = input<string>('msg-popover-content msg-popover-content-component');
  public msgDisableClickOutside = input<boolean>(false);

  public msgVisibilityChange = output<boolean>();
  public msgFunctionControl = output<IMsgPopoverFunctionControl>();
  protected msgVisible = signal<boolean>(false);

  ngOnInit(): void {
    this.msgFunctionControl.emit({
      show: () => this.show(),
      hide: () => this.hide(),
    });
  }

  protected onVisibilityChange(isVisible: boolean): void {
    if (!isVisible) {
      this.msgVisible.set(false);
    }

    this.msgVisibilityChange.emit(isVisible);
  }

  protected show(): void {
    this.msgVisible.set(true);
    this.msgVisibilityChange.emit(true);
  }

  protected hide(): void {
    this.msgVisible.set(false);
    this.msgVisibilityChange.emit(false);
  }

  protected onContentClick(event: MouseEvent): void {
    event.stopPropagation();
    if (this.msgTrigger() === 'click') {
      this.msgVisible.set(!this.msgVisible());
    }
  }
}
