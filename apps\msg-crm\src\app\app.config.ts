import { registerLocaleData } from '@angular/common';
import { provideHttpClient, withFetch } from '@angular/common/http';
import vi from '@angular/common/locales/vi';
import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';
import { provideDocument, provideLucideIcons, provideMsgIndexDb, provideMsgNotification, provideNgZorroAntd, provideTranslate } from '@libs/providers';
import { appRoutes } from './app.routes';
registerLocaleData(vi);

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(appRoutes),
    provideAnimations(),
    provideHttpClient(withFetch()),
    provideNgZorroAntd(),
    provideDocument(),
    provideTranslate(),
    provideLucideIcons(),
    provideMsgNotification(),
    provideMsgIndexDb()
  ],
};
