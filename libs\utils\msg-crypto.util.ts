import * as CryptoJ<PERSON> from "crypto-js";
import { MsgIndexDbDefine } from "../services/defines/msg-indexdb.define";
import { getCpuInfo } from "./msg-common.util";

export class MsgCrypto {
  private static readonly _IV = CryptoJS.enc.Utf8.parse('Msg2025%#!@');
  private static readonly _CONFIG = {
    iv: MsgCrypto._IV,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  } as const;

  private static _generateKeyCrypto(): CryptoJS.lib.WordArray {
    const cpuInfo = getCpuInfo().split('').reverse().join('');
    const mix = cpuInfo + '::' + MsgIndexDbDefine.D_KEY_CRYPTO + navigator.userAgent;
    const base = btoa(mix).slice(3, -2);
    return CryptoJS.SHA256(base);
  }

  /**
   * Encrypts data using AES encryption
   * @param value - Data to encrypt (any JSON-serializable value)
   * @returns Encrypted string
   * @throws Error if encryption fails
   */
  public static encrypt(value: unknown): string {
    try {
      const jsonString = JSON.stringify(value);
      if (!jsonString) {
        throw new Error('Failed to serialize data');
      }

      const parsedText = CryptoJS.enc.Utf8.parse(jsonString);
      return CryptoJS.AES.encrypt(parsedText, this._generateKeyCrypto(), this._CONFIG).toString();
    } catch (error) {
      throw new Error(`Encryption failed: ${(error as Error).message}`);
    }
  }

  /**
   * Decrypts an encrypted string
   * @param value - Encrypted string to decrypt
   * @returns Decrypted data
   * @throws Error if decryption or parsing fails
   */
  public static decrypt<T = unknown>(value: string): T {
    try {
      if (!value) {
        throw new Error('Empty value provided');
      }

      let decryptedString = '';
      try {
        const bytes = CryptoJS.AES.decrypt(value, this._generateKeyCrypto(), this._CONFIG);
        decryptedString = bytes.toString(CryptoJS.enc.Utf8);
        if (!decryptedString) {
          throw new Error('Decryption produced empty result');
        }

        return JSON.parse(decryptedString) as T;
      } catch (error) {
        if (error instanceof SyntaxError) {
          return decryptedString as T;
        }
        throw new Error(`Decryption failed: ${(error as Error).message}`);
      }
    } catch (error) {
      throw new Error(`Decryption failed: ${(error as Error).message}`);
    }
  }
}
