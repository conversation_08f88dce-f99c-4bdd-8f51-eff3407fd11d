import nx from '@nx/eslint-plugin';
import baseConfig from '../../eslint.config.mjs';

export default [
  ...baseConfig,
  ...nx.configs['flat/angular'],
  ...nx.configs['flat/angular-template'],
  {
    files: ['**/*.ts'],
    rules: {
      '@angular-eslint/directive-selector': [
        'error',
        {
          type: 'attribute',
          prefix: 'msg',
          style: 'camelCase',
        },
      ],
      '@angular-eslint/component-selector': [
        'error',
        {
          type: 'element',
          prefix: 'msg',
          style: 'kebab-case',
        },
      ],
      'no-relative-imports': 'off',
      '@typescript-eslint/no-restricted-imports': 'off'
    },
  },
  // Override riêng cho constructor rule
  {
    files: ['**/*.ts'],
    rules: {
      "@angular-eslint/no-empty-lifecycle-method": ["off"],
      '@typescript-eslint/explicit-member-accessibility': [
        'error',
        {
          accessibility: 'explicit',
          overrides: {
            accessors: 'explicit',
            constructors: 'off', // Skip constructor
            methods: 'explicit',
            properties: 'explicit',
            parameterProperties: 'explicit'
          },
          ignoredMethodNames: [
            "transform",
            'ngOnInit',
            'ngOnDestroy',
            'ngOnChanges',
            'ngAfterViewInit',
            'ngAfterViewChecked',
            'ngAfterContentInit',
            'ngAfterContentChecked',
            'ngDoCheck'
          ]
        }
      ],
      // Rule: Class naming cho Angular Components/Directives/Pipes/Services
      '@typescript-eslint/naming-convention': [
        'error',
        {
          selector: 'class',
          filter: {
            regex: '.*(Component|Directive|Pipe|Service)$',
            match: true
          },
          format: ['PascalCase'],
          prefix: ['Msg']
        }
      ],
    },
  },
  {
    files: ['**/*.html'],
    // Override or add rules here
    rules: {},
  },
];
