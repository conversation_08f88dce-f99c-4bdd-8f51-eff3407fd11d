import { ChangeDetectionStrategy, Component, computed, effect, inject, input, signal } from "@angular/core";
import { AbstractControl, ControlContainer, FormArray, FormGroupDirective, ValidationErrors } from "@angular/forms";
import { TranslatePipe, TranslateService } from "@ngx-translate/core";
import { take } from "rxjs";
import { uuid } from "../../utils/msg-common.util";
import { IMsgControlStateDto, IMsgMessageErrorDto } from "./interfaces/msg-validate.interface";
@Component({
  selector: 'msg-validate-shared',
  templateUrl: './msg-validate.component.html',
  styleUrls: ['./msg-validate.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TranslatePipe
  ],
})

export class MsgValidateSharedComponent {
  public msgControl = input<string | AbstractControl | null>('');
  public msgError = input<string>('');

  public _msgSubmit = signal<boolean>(true);
  protected msgMessageError = computed(() => this._computeErrorMessages());
  protected msgShowError = computed(() => this._shouldShowErrors());

  private _controlState = signal<IMsgControlStateDto | null>(null);
  private _formSubmitted = signal<boolean>(false);
  private _activeControl = computed(() => {
    const controlInput = this.msgControl();

    if (controlInput && typeof controlInput === 'object') {
      return controlInput as AbstractControl;
    }

    if (typeof controlInput === 'string' && controlInput && this._controlContainer?.control) {
      return this._getControlByPath(controlInput, this._controlContainer.control);
    }

    return null;
  });

  private _translate = inject(TranslateService);
  private _controlContainer = inject(ControlContainer, { optional: true });
  private _formGroupDirective = inject(FormGroupDirective, { optional: true });

  constructor() {
    effect((onCleanup) => {
      const control = this._activeControl();
      if (control) {
        const updateState = () => {
          this._controlState.set({
            invalid: control.invalid,
            dirty: control.dirty,
            touched: control.touched,
            pending: control.pending
          });
        };

        if (this._msgSubmit() && !this._formSubmitted()) {
          this._controlState.set(null);
          return;
        }

        updateState();
        const statusSub = control.statusChanges.subscribe(updateState);
        const valueSub = control.valueChanges.subscribe(updateState);

        onCleanup(() => {
          statusSub.unsubscribe();
          valueSub.unsubscribe();
        });

        return;
      }

      this._controlState.set(null);
    });

    this._detectFormSubmission();
  }

  public setMsgSubmit(value: boolean): void {
    this._msgSubmit.set(value);
  }

  private _detectFormSubmission(): void {
    if (this._formGroupDirective) {
      this._formGroupDirective.ngSubmit.pipe(take(1)).subscribe(() => {
        this._formSubmitted.set(true);
      });
    }
  }

  private _shouldShowErrors(): boolean {
    const state = this._controlState();
    if (!state || !state.invalid || state.pending) {
      return false;
    }

    if (this._msgSubmit()) {
      return this._formSubmitted() && state.invalid;
    }

    return !!(state.dirty || state.touched);
  }

  private _computeErrorMessages(): IMsgMessageErrorDto[] {
    const currentControl = this._activeControl();
    const state = this._controlState();

    if (!currentControl || !state || !state.invalid) {
      return [];
    }

    if (this._msgSubmit() && !this._formSubmitted()) {
      return [];
    }

    if (!this._msgSubmit() && !(state.dirty || state.touched)) {
      return [];
    }

    if (state.pending) {
      return [{ id: uuid(), message: this._translate.instant('i18n_validating') }];
    }

    const messages: IMsgMessageErrorDto[] = [];
    const errors: ValidationErrors = currentControl.errors || {};

    if (this.msgError()) {
      messages.push({ id: uuid(), message: this._translate.instant(this.msgError()) });
    }

    for (const key of Object.keys(errors)) {
      const error = errors[key];
      let message = '';
      switch (key) {
        case 'required':
          message = this._translate.instant('i18n_required');
          break;
        case 'minlength':
          message = this._translate.instant('i18n_minlength', { requiredLength: error.requiredLength, actualLength: error.actualLength });
          break;
        case 'maxlength':
          message = this._translate.instant('i18n_maxlength', { requiredLength: error.requiredLength, actualLength: error.actualLength });
          break;
        case 'pattern':
          message = this._translate.instant('i18n_pattern');
          break;
        case 'username':
          message = this._translate.instant('i18n_invalid_username');
          break;
        case 'email':
          message = this._translate.instant('i18n_invalid_email');
          break;
        case 'passwordMismatch':
          message = this._translate.instant('i18n_password_mismatch');
          break;
        case 'validMailMsg':
          message = this._translate.instant('i18n_valid_mail_msg');
          break;
        default:
          message = this._translate.instant('i18n_unknown_error', { key: key });
      }
      messages.push({ id: uuid(), message });
    }

    return messages;
  }


  private _getControlByPath(path: string, rootControl: AbstractControl): AbstractControl | null {
    if (!path || !rootControl) {
      return null;
    }

    const parts = path.split('.');
    let currentControl = rootControl;

    for (const part of parts) {
      if (!currentControl) {
        return null;
      }

      if (!isNaN(Number(part)) && currentControl instanceof FormArray) {
        const index = Number(part);
        currentControl = currentControl.at(index);
        continue;
      }

      const nextControl = currentControl.get(part);
      if (!nextControl) {
        return null;
      }

      currentControl = nextControl;
    }

    return currentControl;
  }
}
