export type TYPE_MSG_BUTTON = 'primary' | 'success' | 'warn' | 'danger' | 'info' | 'secondary' | 'outline' | 'link' | 'text' | 'outline-success' | 'outline-warn' | 'outline-danger' | 'outline-info' | 'outline-secondary';

export type TYPE_MSG_BUTTON_SHAPE = 'square' | 'circle';
export interface IMsgTooltipDto {
  content: string;
  placement?: 'top' | 'left' | 'right' | 'bottom' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' | 'leftTop' | 'leftBottom' | 'rightTop' | 'rightBottom';
  trigger?: 'hover' | 'click' | 'focus';
}
